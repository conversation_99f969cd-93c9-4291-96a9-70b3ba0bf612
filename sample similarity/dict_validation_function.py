import json
import random
from sklearn.feature_extraction import DictVectorizer
from sklearn.metrics.pairwise import cosine_similarity


def flatten_json(data, parent_key="", sep="."):
    items = []
    for k, v in data.items():
        new_key = f"{parent_key}{sep}{k}" if parent_key else k
        if isinstance(v, dict):
            items.extend(flatten_json(v, new_key, sep=sep).items())
        elif isinstance(v, list):
            items.append((new_key, " ".join(map(str, v))))
        else:
            items.append((new_key, str(v)))
    return dict(items)


def json_to_string(data):
    return " ".join(f"{k}:{v}" for k, v in sorted(data.items()))


def nested_json_tfidf_cosine_similarity(json1, json2):
    flat1 = flatten_json(json1)
    flat2 = flatten_json(json2)

    # str1 = json_to_string(flat1)
    # str2 = json_to_string(flat2)

    vectorizer = DictVectorizer().fit([flat1, flat2])
    vectors = vectorizer.transform([flat1, flat2])

    return cosine_similarity(vectors[0], vectors[1])[0][0]


# --------------------
# Main script
# --------------------
input_file = "sample_similarity_jsons.json"
output_file = "dict_validation_results.json"

attribute_1 = "ground_truth_json"
attribute_2 = "generated_json"

same_sample_scores = []
different_sample_scores = []

with open(input_file, "r", encoding="utf-8") as f:
    data = json.load(f)

# Pre-parse all records for efficiency
parsed_data = []
for record in data:
    if not record.get(attribute_2):
        continue
    try:
        json1 = json.loads(record[attribute_1])
        json2 = json.loads(record[attribute_2])
        parsed_data.append((json1, json2))
    except json.JSONDecodeError:
        continue

# Compare same sample similarity
for json1, json2 in parsed_data:
    score = nested_json_tfidf_cosine_similarity(json1, json2)
    same_sample_scores.append(score)

# Compare random different sample similarity
num_samples = len(parsed_data)
for i in range(num_samples):
    _, json2 = parsed_data[i]
    j = random.choice([x for x in range(num_samples) if x != i])
    _, random_json2 = parsed_data[j]
    score = nested_json_tfidf_cosine_similarity(json2, random_json2)
    different_sample_scores.append(score)

# Save validation results
validation_output = {
    "same_sample_scores": same_sample_scores,
    "different_sample_scores": different_sample_scores
}

with open(output_file, "w", encoding="utf-8") as f:
    json.dump(validation_output, f, indent=4)

print(f"Saved validation results to {output_file}")
