#!/usr/bin/env python3
"""
Example script demonstrating how to use multiple sentence transformer models
for similarity calculation.
"""

import json
from process_comparison_data import process_comparison_data_with_similarities

def main():
    """Example usage of multi-model sentence transformer similarity"""
    
    # Example 1: Using default model (single model)
    print("=== Example 1: Using default model ===")
    input_file = "comparison_data.json"
    output_file = "comparison_data_single_model.json"
    
    try:
        process_comparison_data_with_similarities(input_file, output_file)
        print(f"✅ Single model processing completed. Output saved to: {output_file}")
    except Exception as e:
        print(f"❌ Error with single model: {e}")
    
    print("\n" + "="*60 + "\n")
    
    # Example 2: Using multiple models
    print("=== Example 2: Using multiple models ===")
    
    # Define multiple models to compare
    model_names = [
        "all-mpnet-base-v2",           # High quality, slower
        "all-MiniLM-L6-v2",            # Fast, good quality
        "paraphrase-MiniLM-L6-v2",     # Optimized for paraphrase detection
    ]
    
    output_file_multi = "comparison_data_multi_model.json"
    
    try:
        process_comparison_data_with_similarities(input_file, output_file_multi, model_names)
        print(f"✅ Multi-model processing completed. Output saved to: {output_file_multi}")
        
        # Show sample output structure
        with open(output_file_multi, "r", encoding="utf-8") as f:
            data = json.load(f)
        
        if data:
            print("\n=== Sample output structure with multiple models ===")
            sample = data[0]
            
            # Show all similarity keys
            similarity_keys = [key for key in sample.keys() if "similarity" in key]
            print("Available similarity metrics:")
            for key in similarity_keys:
                print(f"  - {key}: {sample[key]:.4f}")
                
    except Exception as e:
        print(f"❌ Error with multi-model: {e}")
    
    print("\n" + "="*60 + "\n")
    
    # Example 3: Using specialized models
    print("=== Example 3: Using specialized models ===")
    
    specialized_models = [
        "sentence-transformers/all-roberta-large-v1",  # RoBERTa-based, high quality
        "sentence-transformers/distilbert-base-nli-stsb-mean-tokens",  # DistilBERT-based
    ]
    
    output_file_specialized = "comparison_data_specialized_models.json"
    
    try:
        process_comparison_data_with_similarities(input_file, output_file_specialized, specialized_models)
        print(f"✅ Specialized model processing completed. Output saved to: {output_file_specialized}")
    except Exception as e:
        print(f"❌ Error with specialized models: {e}")
        print("Note: Some models might not be available or require additional downloads")

def compare_model_performance():
    """Compare performance across different models"""
    print("\n=== Model Performance Comparison ===")
    
    try:
        # Load multi-model results
        with open("comparison_data_multi_model.json", "r", encoding="utf-8") as f:
            data = json.load(f)
        
        if not data:
            print("No data available for comparison")
            return
        
        # Extract similarity scores for different types
        same_samples = [sample for sample in data if sample.get("type") == "same"]
        different_samples = [sample for sample in data if sample.get("type") == "different"]
        
        # Get all sentence transformer similarity keys
        similarity_keys = [key for key in data[0].keys() if key.startswith("sentence_transformer_similarity_")]
        
        print(f"\nAnalyzing {len(same_samples)} 'same' samples and {len(different_samples)} 'different' samples")
        print("\nAverage similarity scores by model:")
        print("-" * 50)
        
        for key in similarity_keys:
            model_name = key.replace("sentence_transformer_similarity_", "")
            
            # Calculate averages
            same_avg = sum(sample[key] for sample in same_samples) / len(same_samples) if same_samples else 0
            diff_avg = sum(sample[key] for sample in different_samples) / len(different_samples) if different_samples else 0
            separation = same_avg - diff_avg
            
            print(f"{model_name:30} | Same: {same_avg:.4f} | Different: {diff_avg:.4f} | Separation: {separation:.4f}")
        
        print("\nNote: Higher 'Separation' values indicate better discrimination between same/different pairs")
        
    except FileNotFoundError:
        print("Multi-model results file not found. Run the multi-model example first.")
    except Exception as e:
        print(f"Error comparing models: {e}")

if __name__ == "__main__":
    main()
    compare_model_performance()
