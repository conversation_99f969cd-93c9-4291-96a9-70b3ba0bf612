import json
import random
from typing import Any, Dict, List

from sentence_transformers import SentenceTransformer
from sklearn.feature_extraction import DictVectorizer

# Import similarity functions
from sklearn.feature_extraction.text import CountVectorizer, TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity


def load_jsonl_data(file_path: str) -> List[Dict[str, Any]]:
    """Load JSONL data from file"""
    data = []
    with open(file_path, "r", encoding="utf-8") as file:
        for line in file:
            line = line.strip()
            if line:
                data.append(json.loads(line))
    return data


def load_and_filter_data(file_path: str) -> tuple[List[Dict[str, Any]], List[int]]:
    """
    Load JSON data and filter out samples with empty generated_json
    Returns both filtered data and original indices
    """
    with open(file_path, "r", encoding="utf-8") as f:
        data = json.load(f)

    # Filter out samples with empty generated_json and keep track of original indices
    filtered_data = []
    original_indices = []

    for i, sample in enumerate(data):
        if sample.get("generated_json", "").strip() != "":
            filtered_data.append(sample)
            original_indices.append(i)

    print(f"Original samples: {len(data)}")
    print(f"Filtered samples (with generated_json): {len(filtered_data)}")

    return filtered_data, original_indices


# Similarity calculation functions
def flatten_json(data, parent_key="", sep="."):
    """Flatten nested JSON structure"""
    items = []
    for k, v in data.items():
        new_key = f"{parent_key}{sep}{k}" if parent_key else k
        if isinstance(v, dict):
            items.extend(flatten_json(v, new_key, sep=sep).items())
        elif isinstance(v, list):
            items.append((new_key, " ".join(map(str, v))))
        else:
            items.append((new_key, str(v)))
    return dict(items)


def json_to_string(data):
    """Convert flattened JSON to string representation"""
    return " ".join(f"{k}:{v}" for k, v in sorted(data.items()))


def count_vectorizer_similarity(json1, json2):
    """Calculate similarity using CountVectorizer"""
    flat1 = flatten_json(json1)
    flat2 = flatten_json(json2)

    str1 = json_to_string(flat1)
    str2 = json_to_string(flat2)

    vectorizer = CountVectorizer().fit([str1, str2])
    vectors = vectorizer.transform([str1, str2])

    return cosine_similarity(vectors[0], vectors[1])[0][0]


def dict_vectorizer_similarity(json1, json2):
    """Calculate similarity using DictVectorizer"""
    flat1 = flatten_json(json1)
    flat2 = flatten_json(json2)

    vectorizer = DictVectorizer().fit([flat1, flat2])
    vectors = vectorizer.transform([flat1, flat2])

    return cosine_similarity(vectors[0], vectors[1])[0][0]


def tfidf_similarity(json1, json2):
    """Calculate similarity using TfidfVectorizer with n8n stop words"""
    flat1 = flatten_json(json1)
    flat2 = flatten_json(json2)

    str1 = json_to_string(flat1)
    str2 = json_to_string(flat2)

    vectorizer = TfidfVectorizer().fit([str1, str2])
    vectors = vectorizer.transform([str1, str2])

    return cosine_similarity(vectors[0], vectors[1])[0][0]


def sentence_transformer_similarity(json1, json2, models):
    """Calculate similarity using multiple SentenceTransformer models"""
    flat1 = flatten_json(json1)
    flat2 = flatten_json(json2)

    str1 = json_to_string(flat1)
    str2 = json_to_string(flat2)

    similarities = {}

    # If models is a single model (backward compatibility)
    if not isinstance(models, dict):
        vecs = models.encode([str1, str2])
        return cosine_similarity([vecs[0]], [vecs[1]])[0][0]

    # Calculate similarity for each model
    for model_name, model in models.items():
        vecs = model.encode([str1, str2])
        similarities[model_name] = cosine_similarity([vecs[0]], [vecs[1]])[0][0]

    return similarities


def calculate_similarities_for_sample(sample, models):
    """Calculate all similarity scores for a single sample"""
    try:
        # Parse JSON strings
        json1 = json.loads(sample["original_json"])
        json2 = json.loads(sample["comparison_json"])

        # Calculate similarities using all methods
        count_sim = count_vectorizer_similarity(json1, json2)
        dict_sim = dict_vectorizer_similarity(json1, json2)
        tfidf_sim = tfidf_similarity(json1, json2)
        sbert_sim = sentence_transformer_similarity(json1, json2, models)

        result = {
            "count_vectorizer_similarity": float(count_sim),
            "dict_vectorizer_similarity": float(dict_sim),
            "tfidf_similarity": float(tfidf_sim),
        }

        # Handle multiple sentence transformer models
        if isinstance(sbert_sim, dict):
            # Multiple models - add each with model name suffix
            for model_name, similarity in sbert_sim.items():
                result[f"sentence_transformer_similarity_{model_name}"] = float(
                    similarity
                )
        else:
            # Single model (backward compatibility)
            result["sentence_transformer_similarity"] = float(sbert_sim)

        return result

    except (json.JSONDecodeError, Exception) as e:
        print(f"Error calculating similarities: {e}")
        # Create error result with default values
        error_result = {
            "count_vectorizer_similarity": 0.0,
            "dict_vectorizer_similarity": 0.0,
            "tfidf_similarity": 0.0,
        }

        # Handle multiple models in error case
        if isinstance(models, dict):
            for model_name in models.keys():
                error_result[f"sentence_transformer_similarity_{model_name}"] = 0.0
        else:
            error_result["sentence_transformer_similarity"] = 0.0

        return error_result


def process_comparison_data_with_similarities(
    input_file: str, output_file: str, model_names=None
):
    """Process comparison data and add similarity scores"""
    # Default models if none specified
    if model_names is None:
        model_names = ["all-mpnet-base-v2"]

    # Load the SentenceTransformer models
    print("Loading SentenceTransformer models...")
    models = {}
    for model_name in model_names:
        print(f"  Loading {model_name}...")
        # Create a clean model name for the key (remove special characters)
        clean_name = model_name.replace("/", "_").replace("-", "_")
        models[clean_name] = SentenceTransformer(model_name)

    print(f"Loaded {len(models)} models: {list(models.keys())}")

    # Load comparison data
    print(f"Loading comparison data from {input_file}...")
    with open(input_file, "r", encoding="utf-8") as f:
        comparison_data = json.load(f)

    print(f"Processing {len(comparison_data)} comparison samples...")

    # Process each sample and add similarity scores
    processed_data = []
    for i, sample in enumerate(comparison_data):
        if i % 10 == 0:
            print(f"Processing sample {i+1}/{len(comparison_data)}")

        # Create new sample with all original attributes
        processed_sample = sample.copy()

        # Add similarity scores
        similarities = calculate_similarities_for_sample(sample, models)
        processed_sample.update(similarities)

        processed_data.append(processed_sample)

    # Save processed data
    with open(output_file, "w", encoding="utf-8") as f:
        json.dump(processed_data, f, indent=2, ensure_ascii=False)

    print(f"Processed data with similarities saved to: {output_file}")
    return processed_data


def create_comparison_samples(
    filtered_data: List[Dict[str, Any]],
    prompts_data: List[Dict[str, Any]],
    original_indices: List[int],
) -> List[Dict[str, Any]]:
    """
    Create comparison samples with both same and different pairs using actual prompts from JSONL
    """
    comparison_samples = []

    # Create same samples (ground_truth_json vs generated_json from same sample)
    for i, sample in enumerate(filtered_data):
        original_index = original_indices[i]
        prompt = (
            prompts_data[original_index]["prompt"]
            if original_index < len(prompts_data)
            else f"Compare the ground truth workflow with the generated workflow for sample {i+1}"
        )

        same_comparison = {
            "prompt": prompt,
            "original_json": sample["ground_truth_json"],
            "comparison_json": sample["generated_json"],
            "type": "same",
        }
        comparison_samples.append(same_comparison)

    # Create different samples (generated_json from one sample vs generated_json from another)
    num_samples = len(filtered_data)
    for i in range(num_samples):
        # Select a random different sample
        j = random.choice([x for x in range(num_samples) if x != i])

        # Use the prompt from the first sample (i) for different comparisons
        original_index_i = original_indices[i]
        prompt = (
            prompts_data[original_index_i]["prompt"]
            if original_index_i < len(prompts_data)
            else f"Compare generated workflow from sample {i+1} with generated workflow from sample {j+1}"
        )

        different_comparison = {
            "prompt": prompt,
            "original_json": filtered_data[i]["ground_truth_json"],
            "comparison_json": filtered_data[j]["generated_json"],
            "type": "different",
        }
        comparison_samples.append(different_comparison)

    return comparison_samples


def main():
    """Main function with options for creating comparison data or processing with similarities"""
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "--similarities":
        # Process existing comparison data with similarities
        input_file = "comparison_data.json"
        output_file = "comparison_data_with_similarities.json"

        # Check if specific models are requested
        model_names = None
        if len(sys.argv) > 2 and sys.argv[2] == "--models":
            # Models specified as comma-separated list after --models
            if len(sys.argv) > 3:
                model_names = [model.strip() for model in sys.argv[3].split(",")]
                print(f"Using specified models: {model_names}")
            else:
                print(
                    "Error: --models flag requires a comma-separated list of model names"
                )
                print(
                    "Example: python process_comparison_data.py --similarities --models all-mpnet-base-v2,all-MiniLM-L6-v2"
                )
                return

        print("Processing comparison data with similarity calculations...")
        process_comparison_data_with_similarities(input_file, output_file, model_names)

        # Show sample output
        with open(output_file, "r", encoding="utf-8") as f:
            data = json.load(f)

        if data:
            print("\nSample output with similarities:")
            sample = data[0].copy()
            # Truncate long JSON strings for display
            if len(sample["original_json"]) > 100:
                sample["original_json"] = sample["original_json"][:100] + "..."
            if len(sample["comparison_json"]) > 100:
                sample["comparison_json"] = sample["comparison_json"][:100] + "..."
            print(json.dumps(sample, indent=2))

        return

    # Original functionality - create comparison data
    input_file = "/home/<USER>/Internship/sample similarity/gemini sample generted.json"
    prompts_file = "/home/<USER>/Internship/sample similarity/sample_dataset.jsonl"
    output_file = "comparison_data.json"

    # Load prompts data from JSONL
    print("Loading prompts from JSONL file...")
    prompts_data = load_jsonl_data(prompts_file)
    print(f"Loaded {len(prompts_data)} prompts from JSONL file")

    # Load and filter data
    filtered_data, original_indices = load_and_filter_data(input_file)

    if not filtered_data:
        print("No valid samples found with generated_json. Exiting.")
        return

    # Create comparison samples
    comparison_samples = create_comparison_samples(
        filtered_data, prompts_data, original_indices
    )

    # Save the processed data
    with open(output_file, "w", encoding="utf-8") as f:
        json.dump(comparison_samples, f, indent=2, ensure_ascii=False)

    print(f"\nProcessed data saved to: {output_file}")
    print(f"Total comparison samples created: {len(comparison_samples)}")
    print(
        f"Same type samples: {len([s for s in comparison_samples if s['type'] == 'same'])}"
    )
    print(
        f"Different type samples: {len([s for s in comparison_samples if s['type'] == 'different'])}"
    )

    # Show a sample of the output structure
    print("\nSample output structure:")
    if comparison_samples:
        sample_output = comparison_samples[0].copy()
        # Truncate long JSON strings for display
        if len(sample_output["original_json"]) > 100:
            sample_output["original_json"] = (
                sample_output["original_json"][:100] + "..."
            )
        if len(sample_output["comparison_json"]) > 100:
            sample_output["comparison_json"] = (
                sample_output["comparison_json"][:100] + "..."
            )

        print(json.dumps(sample_output, indent=2))

    print("\nTo calculate similarities for the comparison data, run:")
    print("python process_comparison_data.py --similarities")
    print("\nTo use multiple sentence transformer models, run:")
    print(
        "python process_comparison_data.py --similarities --models all-mpnet-base-v2,all-MiniLM-L6-v2,sentence-transformers/all-roberta-large-v1"
    )


if __name__ == "__main__":
    main()
