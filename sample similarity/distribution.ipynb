{"cells": [{"cell_type": "code", "execution_count": 8, "id": "db01f79f", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_7231/119482768.py:25: User<PERSON><PERSON>ning: No artists with labels found to put in legend.  Note that artists whose label start with an underscore are ignored when legend() is called with no argument.\n", "  plt.legend(title=\"Comparison Type\")\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAm8AAAHPCAYAAAAFwj37AAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjAsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvlHJYcgAAAAlwSFlzAAAPYQAAD2EBqD+naQAAhsFJREFUeJzs3Wd4HNX99vHvbJF21Yut4t5778bGBoPphPIAIQFDCCG0AA6dJH9KEiAEMIRiCDGhJJAEQi+G0Isxrti44S53W7233Z3zvFhLWG5Yq5VWK92f6/Ila3Z39ndmRtpbZ2bOsYwxBhERERGJCo5IFyAiIiIiR07hTURERCSKKLyJiIiIRBGFNxEREZEoovAmIiIiEkUU3kRERESiiMKbiIiISBRxRbqAcPrmm28wxuB2uyNdioiIiAgAPp8Py7IYOXJkWNbXpnrejDG0lzGHjTHU1ta2m/ZC+2wzqN3tqd3tsc3QPtvdHtsM7bPdddkknG1uUz1vdT1uQ4cOjXAlza+yspI1a9bQp08f4uLiIl1Oi2iPbQa1uz21uz22Gdpnu9tjm6F9truuzeE8K9imet5ERERE2jqFNxEREZEoovAmIiIiEkUU3kRERESiiMKbiIiISBRpU3ebioiIhEMgEMDn8zXb+mtqauq/Ohztpx+lLbbb7XbjdDpb9D0V3kRERPYyxrB7926Ki4ub9X1s28blcrFz5842E2KORFttd0pKCllZWViW1SLvp/AmIiKyV11wy8jIIC4urtk+jAOBADU1NcTGxrZ4r00ktbV2G2OorKwkNzcXgOzs7BZ5X4U3ERERgsGiLrilp6c3+3sBeDyeNhFijlRbbLfX6wUgNzeXjIyMFmlX2+mzFBERaYK6a9zay8j/Ej51x0xzXie5L4U3ERGRfbTUdUvSdrT0MaPwJiIiIhJFWkV4e/311znllFMYOnQop556KnPnzo10SSIiIiKtUsRvWHjjjTf4zW9+wy233MIxxxzD22+/zfXXX09WVhYjR46MdHkiIiINrFixgueff55FixZRWFhIx44dmThxIpdffjldu3aNdHlh8eqrr3Lbbbfx0Ucf0aVLl4jVsWDBAi666KIffN7zzz/P+PHjW6Ci1iGi4c0Yw1/+8hcuvvhiLr74YgCuvvpqli5dysKFCxXeRESkVXnhhRe45557GD9+PDfccAMZGRls3bqVOXPm8L///Y9nnnmGwYMHR7rMJjvmmGP4z3/+Q0ZGRkTrGDx4MP/5z3/qv1+1ahW///3vuf322xts5z59+kSivIiJaHjbtGkTO3bs4PTTT2+w/Omnn45QRSIiIge3ZMkS7r77bi644AJ++9vf1i8fP348xx13HGeffTa33XYbb775ZgSrDI+0tDTS0tIiXQYJCQmMGDGi/vu6GRr69OnTYHl7E9HwlpOTA0BlZSWXXnopq1evpkuXLlx55ZVMmzYtpHXWDZjX1lVVVTX42h60xzaD2t2e2t1cbQ7XnXDGmLCsZ3+tZV/X1NRg2zaBQKB+PLJ9zZkzh8TERK677roDHk9OTubmm29m48aNlJaWEh8fD8DcuXP5+9//zqZNm4iLi+O4447j17/+NUlJSQA89thjzJ07l1//+tc88sgjbN26lZ49e3LHHXdgWRb33HMP69ato2vXrtx2221MnDix/nWvv/46v/vd77j//vvZtWsXffv2ZebMmfXPAVi7di2PP/44S5YsoaysjLS0NKZPn84NN9yAx+MBYNCgQVx99dV89tln5OTk8POf/5zMzEx++9vf8sEHH9C5c2eKioq49957mT9/PmVlZfTs2ZNLLrmEH/3oR/XvlZOTw8MPP8ySJUuorKxk6NChXHvttYwaNQqAHTt2MH36dO677z4+/PBD5s2bh8vlYvr06dx666312+xwbNuu/7rvPjjvvPNwuVy8+OKLDZ7/y1/+ktraWp599lkuvvhiOnfuTI8ePfjHP/5BdXU148aN47bbbmtwanj9+vXMmjWLxYsXAzBhwgRuvvnmw54SDwQC2LZNVVVVfY11muO4jmh4Ky8vB+CWW27hV7/6FTfeeCPvv/8+V111Fc8880yDA/BI+Xw+1qxZE+5SW626ANyetMc2g9rdnoSzzW63myGDB+No4sChdiDAylWrmnUcq9awr10uV33vzr6MMcybN48pU6ZgWRbV1dUHPGfq1KlMnToVgOrqaubMmcPs2bM599xzufLKK9mxYwdPPPEE33zzDc899xwejwe/38+uXbu47777uPrqq/F6vdx3331cd911uN1uLr30UlJTU3n00Ue54YYbePfdd+tfV1hYyK233srll19Oly5d+Mc//sHll1/Os88+y6BBg8jLy2PGjBkMGTKEO++8k5iYGL744gteeOEFUlJSuPTSS+tr/+tf/8pVV11Fnz59yMzMrP8crampobq6mhtuuIGioiJ+85vfEB8fzzvvvMOtt95KWloaY8aMYdOmTVx88cV07dqVm2++GbfbzYsvvsjPfvYznnjiCUaPHl2/Xe+++27OOOMMHnzwQVatWsXjjz9OUlIS11xzzQ/un9ra2vqv++6DH/3oR/VBt1u3bgDk5eUxf/587rjjDqqrq7Ftm48++oiUlBRuuukmbNvmkUce4aKLLuKVV17B6/WyZcsWLrzwQnr06MGdd96JbdvMmTOHCy64gH//+9+H7I2sqanB7/ezadOmH2xDOEQ0vLndbgAuvfRSzjrrLAAGDhzI6tWrQw5vbre7XZz7rqqqIicnhx49etSP7tzWtcc2g9rdntrdHG22LAuH04lvzQJMZVlo64hLxD1wPH379m2W3rfWsq9ramrYuXMnsbGx9b1SdYqKiqipqaF79+4HPHYwJSUlzJkzh3POOYe77rqrfvnAgQO56KKLmDt3LmeddRYul4vq6mpuv/12jj76aAC2bt3KQw89xB/+8Af+3//7fwA4HA5mzpzJrl27GDhwYP3r7rjjDs444wwAjj76aE444QSef/55Hn74YbZu3cqAAQN45JFHSEhIAIIBc/HixSxbtqxBO4YNG8YVV1xR//2GDRsA6rfF0qVLueKKKzj55JMBmDx5Munp6Xi9XjweD3PmzMHtdvPcc8+RmJgIwPHHH88ZZ5zBI488wn/+8x9iY2PrX3vrrbdiWRZTp05l0aJFfPnll9x0000/uF1jYmLqv+5b/xlnnMGsWbN4//3360Pg//73PzweD6eccgoejweHw0FVVRUvv/xyfcDr168f55xzDnPnzuWnP/0pc+bMwePx8Mwzz9Rvs6OPPpoTTzyRF1544bA1ulwuunXrVt/OOnXHdzhFNLxlZWUBwY23rz59+vDpp5+GtE7LstrV6Nher7ddtRfaZ5tB7W5PmqPNztoqqKkI7cWu4EdFcwerSO9rh8OBw+HA6XQeMMWRa+82sG37iKY/WrFiBbW1tfzoRz9q8Pzx48fTuXNnFi5cWN9pATBmzJj659XdJDBq1Kj6ZXU9PhUVFTidzvo6Tz/99PrnxMfHM3XqVD777DOcTidTpkxhypQp+Hw+tm7dSk5ODmvXrqWwsJCUlJQGdfXr16/B93WTxte9z/jx43n88cdZt24dU6dOZcqUKdx22231z1+0aBHHHnssKSkp9cucTiennnoqjz/+ONXV1fXrHDZsGJZl1b9fVlYWO3bsOKLtun9ddVJSUjjhhBN4++23mTlzJhAczeKkk06qD5OWZTFy5Eh69uxZ/7qhQ4fStWtXli5dyowZM1iwYAHjx48nPj6+/g+V5ORkxowZw/z58w9ZY90+qQuzzS2i4W3QoEHEx8ezfPlyxowZU798325PERGRSEtJSSE+Pp6dO3ce8jmVlZXU1taSkpJCSUkJAB06dDjgeR06dKCsrGEvaF0vz75+KASkpaXVn8Gqk56eXv/etm0za9YsXnjhBSorK8nOzmbYsGEH9Awdqs59PfTQQzz55JPMnTuX9957D4fDwVFHHcWdd95J165dKSkpOWRbjTH1l0kdrF0OhyMsPbrnnHMOb775JosXLyYmJoYNGzY06PUEDnr3bHp6OqWlpQAUFxfz7rvv8u677x7wvNZwA0ediIY3j8fDL37xCx5//HEyMzMZNmwY77zzDvPmzePZZ5+NZGkiIiINTJ48mQULFlBTU3PQAPTqq69y99138+KLL5KcnAxAfn4+vXv3bvC8vLy8sIydVlxcjDGmwQ0p+fn5pKenA/DUU0/x7LPPcuedd3LiiSfW90Cdc845jX6vxMREbrrpJm666SY2bdrERx99xOzZs7nrrruYM2cOycnJ5OfnH/C6vLw8AFJTU8nNzQ2lmUds3LhxdOvWjffeew+320337t0bdAxBcJvtLz8/v77DKDExkaOOOopLLrnkgOfV9b62BhGfYeGqq67immuu4aGHHuKUU07hvffe49FHH21Xg+2JiEjr9/Of/5zi4mIeeuihAx4rKChgzpw5dO/enREjRjB8+HBiYmJ46623Gjxv8eLF7Ny5s/4OzKbw+Xx88cUX9d9XV1fz+eef118vvmTJEvr06cM555xTH9z27NnDunXrDrgj8nB27NjB1KlTee+99wDo1asXl112GUcddRS7d+8GYOzYsXzyyScNehQDgQDvvPMOQ4cOrb9WrTlZlsXZZ5/Nhx9+yIcfftjgtHSdb775hsLCwvrvV61axfbt2+u32bhx49iwYQMDBw5k6NChDB06lCFDhvDss8/ywQcfNHsbjlSriJGXXHLJQVOuiIhIazFixAiuu+46Hn74YTZu3MhZZ51Famoq69ev5+9//zsVFRU89dRTWJZFSkoKv/zlL3nsscdwu90cd9xxbN++nb/85S/06dOHM888Myw1/eY3v2HmzJmkp6fz9NNPU1lZyZVXXgkEry2bPXs2Tz31FCNGjGDLli389a9/pba2tlHDV3Tu3JmsrCz++Mc/Ul5eTrdu3Vi5ciWfffYZl19+OQC/+tWv+Pzzz7nooov45S9/SUxMDP/85z/Ztm0bc+bMCUtbj8TZZ5/No48+ijHmoNu4qqqKyy67jCuvvJKKigoeeugh+vXrx2mnnQYEO5TOP/98Lr/8cn7yk58QGxvLf/7zHz788EMeeeSRFmvHD2kV4U1ERCQaXHnllQwaNIgXXniBe++9l+LiYrKyspgyZQpXXHEFnTp1qn/uNddcQ4cOHfjnP//Jyy+/TEpKCieddBIzZ87E6/UedLiRxrrzzju55557KCwsZNSoUfzrX/+ie/fuAFx++eUUFRXx/PPP8/jjj5Odnc0ZZ5yBZVn89a9/paSkpP707g957LHHmDVrFn/5y18oKioiOzubX/3qV/zyl78EoG/fvrz44ovMmjWL3/zmN1iWxbBhw3j++ecPOHXZnDIzMxkwYACpqalkZ2cf8PiYMWOYMGFC/SDL06ZN4+abb67vGRwwYAAvvPACDz30EDfffDPGGPr168fjjz/Occcd12Lt+CGWaa5RFyNgxYoVQPDukbausrKSNWvWMHDgwHZzJ157bDOo3e2p3c3ZZnv5p1BREtqL45NxDD8mnOU00Fr2dXV1NZs3b6Znz57NfsdgIBCguroaj8dzRHdZ7u/RRx/lscceY+3atc1QXfNpart/yJ49e5g2bRqzZs3ixBNPbPDYjBkzAPjHP/4R9vc93LFTd3zHxMSELZ+o501ERESi2po1a/joo494//336dKlC8cff3ykS2pWEb9hQURERKQpampqeOaZZwgEAjz88MPN0qvXmqjnTUREJMpcc801RzSdVHsxYsQIlixZctjnNMfp0khRz5uIiIhIFFF4ExEREYkiCm8iIiIiUUThTURERCSKKLyJiIiIRBGFNxEREZEoovAmIiIiEkUU3kRERESiiMKbiIjIPtrQlN/SQlr6mFF4ExERAdxuNxCcSFykMeqOmbpjqLlpeiwRERHA6XSSkpJCbm4uAHFxcViW1SzvFQgEqKmpqX/f9qKttdsYQ2VlJbm5uaSkpLRYmxTeRERE9srKygKoD3DNxbZt/H4/LpcLh6P9nARrq+1OSUmpP3ZagsKbiIjIXpZlkZ2dTUZGBj6fr9nep6qqik2bNtGtWze8Xm+zvU9r0xbb7Xa7W7wXUeFNRERkP06ns1k/kG3bBiA2NhaPx9Ns79PatNd2h1vb6bMUERERaQcU3kRERESiiMKbiIiISBRReBMRERGJIgpvIiIiIlFE4U1EREQkiii8iYiIiEQRhTcRERGRKKLwJiIiIhJFFN5EREREoojCm4iIiEgUUXgTERERiSIKbyIiIiJRROFNREREJIoovImIiIhEEYU3ERERkSii8CYiIiISRRTeRERERKKIwpuIiIhIFFF4ExEREYkiCm8iIiIiUUThTURERCSKKLyJiIiIRBGFNxEREZEoovAmIiIiEkUU3kRERESiiMKbiIiISBRReBMRERGJIgpvIiIiIlFE4U1EREQkiii8iYiIiEQRhTcRERGRKOKKdAE7duxg2rRpByz/4x//yLnnnhuBikRERERar4iHt7Vr1xIbG8uHH36IZVn1yxMTEyNYlYiIiEjrFPHwtm7dOnr27ElGRkakSxERERFp9SJ+zdvatWvp06dPpMsQERERiQqtouetY8eO/PSnPyUnJ4fu3btz1VVXcfTRR4e0PmMMlZWVYa6y9amqqmrwtT1oj20GtTvUdu97GUZTGGPCsp4j0Rz72rIsvF4vfr8f4/OFtg6/n5i9dYW6PQ63P2pra/F6vdTW1v7gfmvJ/dGc9HMd/BqNP6eN1Rz72DIRbHFtbS0jR45k4MCB3HzzzcTFxfHmm2/y/PPP88wzzzBx4sRGrW/FihXU1tY2U7UiEi3cbjdDBg/G4XQ2aT12IMDKVavwhRh6WgOv18ugQYPI/ehlfMX5Ia3DndKBjOPOZfXq1SF9ELndbgYPHoyzifsjEAiwKsr3h3yvvR0XMTExDB06NCzrimjPW0xMDIsWLcLlchETEwPAkCFD2LhxI08//XSjwxsED4b2cBq2qqqKnJwcevTogdfrjXQ5LaI9thnU7lDabVkWDqcT35oFmMqykN7fikvEPXA8ffv2bbG/6ptjX9f1bKSkpGJcofVyWAkpAPTs2TOkbWFZFk6nk0W7N1PmOzD8+f0BysrKSExMxOU6+Ad5otvL2KyeLbo/mpN+rnsQFxd32OPiSETDcVHX5nCK+GnTuLi4A5b169ePL7/8MqT1WZZ10HW2VV6vt121F9pnm0HtDoWztgpqKkJ7Y5er/v1bWnPsa5fLBW53qC8Gmr4tqoyfCtt/wHJfwEd+RRlWXCxux8FrdBl/WGpobdrzz3XdvjzUcXEk2upx8UMiesPCd999x8iRI1m8eHGD5StXrmwXvWciIiIijRXR8NavXz/69u3LXXfdxeLFi9m4cSP33nsvy5Yt44orrohkaSIiIiKtUkRPmzocDp588kkeeOABZs6cSWlpKYMGDeKZZ56hf//+kSxNREREpFWK+DVvaWlp3HPPPZEuQ0RERCQqRHyQXhERERE5cgpvIiIiIlFE4U1EREQkiii8iYiIiEQRhTcRERGRKKLwJiIiIhJFFN5EREREoojCm4iIiEgUUXgTERERiSIKbyIiIiJRROFNREREJIoovImIiIhEEYU3ERERkSii8CYiIiISRRTeRERERKKIwpuIiIhIFFF4ExEREYkiCm8iIiIiUUThTURERCSKKLyJiIiIRBGFNxEREZEoovAmIiIiEkUU3kRERESiiMKbiIiISBRReBMRERGJIgpvIiIiIlFE4U1EREQkiii8iYiIiEQRhTcRERGRKKLwJiIiIhJFFN5EREREoojCm4iIiEgUUXgTERERiSIKbyIiIiJRROFNREREJIoovImIiIhEEYU3ERERkSii8CYiIiISRRTeRERERKKIwpuIiIhIFFF4ExEREYkiCm8iIiIiUUThTURERCSKKLyJiIiIRBGFNxEREZEoovAmIiIiEkUU3kRERESiiMKbiIiISBRReBMRERGJIgpvIiIiIlFE4U1EREQkirSq8LZ582ZGjhzJq6++GulSRERERFqlVhPefD4fN954I5WVlZEuRURERKTVajXh7dFHHyU+Pj7SZYiIiIi0aq0ivC1atIj//Oc/3HfffZEuRURERKRVc0W6gNLSUm6++WZ+97vfkZ2d3eT1GWPaxanXqqqqBl/bg/bYZlC7Q2m3ZVl4vV78fj/G5wvp/S2/n5i972+MCWkdjdUc+7o1bIt9a/D5/Ac8HggEcLtdBAIBwDroOvyO4Otacn80F8uyqK2txev1Ultbi2UdvM0/pKnbIdT3bUoN+x7j3x8XPnwhHpt+h6t+fa31uGiO390RD2933nknI0aM4PTTTw/L+nw+H2vWrAnLuqJBTk5OpEtoce2xzaB2N4bX62XQoEEUFxfhK84P6X3dfkMGwRupWjo4h3Nft4ZtkZSURN++fXG5XLjtAz923G4XHo/nsOtwuYKv2759O6WlpY2uobVwu90MHjwYr9dLSkpKyOsJBAKsWrUq5NBTV4fT6YxIDTk5OfXHZlFRMfkVZSHVYOIToWtkfk4jKaLh7fXXX2fx4sW89dZbYVun2+2mT58+YVtfa1VVVUVOTg49evTA6/VGupwW0R7bDGp3KO2u61FISUnFuELrXbASUgDo2bNni/a8hXtft4Zt4XAEr9AJFOUSKMk74HHb2NTW1hITE4PDOvjVPIHkjpDdl65du2LbdqNraC0sy8LpdPL1jg3sKMglMTERl6txASrR7WVsVk/69u0b8rFZV8ei3Zsp8zU+9IRaw77HeFxcHACpqSlYcbGNrgEgJTb4c9KSP6eNVdfmcIpoeHvllVcoKCjgmGOOabD8jjvu4Omnn+add95p9Doty6o/INoDr9fbrtoL7bPNoHaHwuVygdsd2hvv7emJRGBujn3dGraFI+DHETiwl8bYAQLVlVgOcDgOHmQcgeBp0x/qoYsWlXYt+RVlWHGxuB2N2y8uE9wW4Tg2q4yfCvvAU9nNXYPX661/rcvlxh1CDXWvbUod0Sqi4e2BBx6gurq6wbITTjiBa6+9llNOOSVCVYmIiIi0XhENb5mZmQddnp6eTufOnVu4GhEREZHWr1UMFSIiIiIiRybid5vub+3atZEuQURERKTVUs+biIiISBRReBMRERGJIgpvIiIiIlFE4U1EREQkiii8iYiIiEQRhTcRERGRKKLwJiIiIhJFFN5EREREoojCm4iIiEgUUXgTERERiSIKbyIiIiJRJKTw9vjjj7Nr165w1yIiIiIiPyCk8Pbcc89x3HHHcckll/DWW29RU1MT7rpERERE5CBCCm9ffvklDzzwAG63m1tvvZVJkyZx++23880334S7PhERERHZhyuUF8XExHDKKadwyimnkJuby5tvvsncuXN5+eWX6dGjB2effTZnn3026enp4a5XREREpF1r8g0LGRkZXHTRRVxxxRWMGTOGzZs3M2vWLKZOncr//d//UV5eHo46RURERIQQe97qLFy4kDfeeIP333+fyspKJkyYUB/cPvvsM37/+9+ze/du/va3v4WrXhEREZF2LaTw9tBDD/HWW2+xa9cusrOz+dnPfsbZZ59Np06d6p9zyimnsHbtWp5//vmwFSsiIiLS3oUU3p555hmOP/54/vCHP3DUUUdhWdZBnzd06FBmzpzZlPpEREREZB8hhbcvvviC5ORk8vLy6oNbSUkJu3btYsCAAfXPO/7448NTpYiIiIgAId6w4HA4uOSSS5gxY0b9suXLl3PmmWdy1VVXUVVVFbYCRUREROR7IYW3+++/n/Xr13P99dfXL5swYQKzZ89m5cqVPPLII2ErUERERES+F1J4+/jjj7nllls44YQT6pfFxMQwbdo0rr/+eubOnRu2AkVERETkeyGFt4qKCpKSkg76WHp6OkVFRU0qSkREREQOLqTwNnjwYF555ZWDPvbqq6/Sv3//JhUlIiIiIgcX0t2mV155JZdddhlnn30206dPJz09ncLCQj766CNWrVrFk08+Ge46RURERIQQw9ukSZN44okneOSRR3jkkUcwxmBZFgMHDmT27NlMmTIl3HWKiIiICE2YHmvq1KlMnTqVmpoaiouLSUxMJC4uLpy1iYiIiMh+mjS3aUlJCVVVVdi2TXFxMcXFxfWP7TtVloiIiIiER0jhLScnh1tvvZXly5cf8jlr1qwJuSgRERERObiQwtsf/vAHcnJy+NWvfkVWVhYOR0g3rYqIiIhII4UU3hYvXszdd9/NaaedFu56REREROQwQuoyS0hIIDk5Ody1iIiIiMgPCCm8nXHGGbzwwgsYY8Jdj4iIiIgcRkinTb1eL0uWLGH69OkMHToUj8fT4HHLsrjnnnvCUqCIiIiIfC+k8Pbaa6+RmJiIbdsHvePUsqwmFyYiIiIiBwopvH388cfhrkNEREREjkCTxviwbZvvvvuOzz//nPLy8gaD9IqIiIhI+IU8w8Ibb7zBgw8+SG5uLpZl8d///pdHH30Ut9vNgw8+SExMTDjrFBERERFC7Hl79913ueWWW5gwYQIPPfRQ/V2nJ5xwAp9//jmzZ88Oa5EiIiIiEhRSz9uTTz7J+eefz5133kkgEKhffvbZZ1NQUMBLL73EzJkzw1WjiIiIiOwVUs/b5s2bmT59+kEfGz58OHv27GlSUSIiIiJycCGFt/T0dDZu3HjQxzZu3Eh6enqTihIRERGRgwspvJ1yyik88sgjvPfee9TW1gLBsd1WrlzJ7NmzOemkk8JapIiIiIgEhXTN28yZM1m3bh0zZ87E4QjmvxkzZlBZWcmYMWO47rrrwlqkiIiIiASFFN5iYmKYM2cO8+bNY/78+ZSUlJCYmMi4ceOYOnWqZlgQERERaSYhj/MGMGnSJCZNmhSuWkRERETkB4QU3h577LEffM6vfvWrUFYtIiIiIocR9vCWkJBARkaGwpuIiIhIMwgpvH333XcHLKusrGTJkiXceeed/N///V+TCxMRERGRAzVpYvp9xcXFcfTRR3P11Vfz5z//OVyrFREREZF9hC281cnOzj7kAL4HU1BQwE033cSECRMYOXIkv/zlL9mwYUO4yxIRERFpE8IW3owx7Ny5k7/97W907tz5iF935ZVXsm3bNv72t7/x3//+F4/Hw89+9jOqqqrCVZqIiIhImxHSNW8DBgw45FhuxpgjPm1aVFREly5duPLKK+nbty8AV111FWeccQbr169n2LBhoZQnIiIi0maFFN6uvvrqg4a3hIQEjjnmGHr06HFE60lNTWXWrFn13+fn5/P000+TlZVFnz59QilNREREpE0LKbxdc8014a6D//u//+Oll14iJiaGJ554gri4uJDWY4yhsrIyzNW1PnWnldvT6eX22GZQu0Npt2VZeL1e/H4/xucL6f0tv58YoLq6GmNMSOuoc6Svb459Hc5tUVVVFdK2cDgceDwebGMTsAMHPG7bdoOvB2Ob4GPV1dWHfV5r9/3+CG4Hn8/f6HX4HcGP7qYcm5Zl4fF48Pt9+EI4LupqaOwxse8x/v22CK2GptTRkprjd3dI4W3nzp2Nen6nTp1+8DkXX3wxP/7xj/nXv/7F1VdfzYsvvsjgwYMbXZvP52PNmjWNfl20ysnJiXQJLa49thnU7sbwer0MGjSI4uIifMX5Ib1vrNNDujF4PJ6QXl/HDgRYuWpVoz6cwrmvw7Et3H5DBrB58+aQPohSU1Pp1asXtbW1h/3jurq6+pCPxcXVAsHPn6KiokbX0FrU7Y+ysjIAiouLG70OT0o6JgzHJkBJSSl5ZSWNfp2JT4SuoR8TOTk59duiqKiY/IqyRq8jHHVEq5DC27Rp0xo1f+mRhKm606R/+MMfWLZsGf/85z+59957G12b2+1uF6dcq6qqyMnJoUePHni93kiX0yLaY5tB7Q6l3XW/n1JSUjGu0OZadqR1xLIs/N8twA7xg8WKS8Q9cDx9+/Y9ol6B5tjX4dgWVkIKAD179gy55w2C82If7KyKbdtUV1fj8Xjqn7u/mJgYINgZkJWV1egaWou6/ZGYmEh+RRkpKSm43Y37KO6QkIplWSzavZkyX2iBJdObzOAOnUlOTsZ43I1+fUps8Phs7DGx7zFedyykpqZgxcU2uoam1NGS6tocTiGFt4cffpg77riDwYMH86Mf/YjMzEyKior4+OOPmTt3LldeeeUR3XFaUFDA/PnzOfnkk3E6nUDwh7x3797k5uaGUhqWZYV8yjUaeb3edtVeaJ9tBrU7FC6XC9yN/2AKvnjv76SaKhw1FSGuI/grtrFBrDn2ddO2RWjt2J/DcuB0OA/9uOPQjzusYKgLR29Ta+Dae3y53S7cjdwvLufeU4XGT4Xd+NOuADUmsLcOF2678ceFyxV8TajHhNfrrX+ty+XGHWI7mlpHtAopvL3++utMmzbtgJ6xU045hfT0dJYuXXpE02Pl5uZyww03kJ6ezsSJE4Hgac/Vq1czbdq0UEoTERERadNCGuft66+/5rTTTjvoY1OmTGHJkiVHtJ4BAwYwefJk7rrrLhYvXsy6deu45ZZbKC0t5Wc/+1kopYmIiIi0aSGFt9TUVJYtW3bQx+bNm0dmZuYRrceyLB5++GEmTJjAzJkzOffccykpKeGFF144opscRERERNqbkE6bnnPOOTzxxBNUVVUxbdo00tLSyM/P59133+Xf//43t99++xGvKzExkTvvvJM777wzlFJERERE2pWQwttVV11FWVkZzz77LE8//TQQHMfI6/Vy/fXXc/7554e1SBEREREJCim8WZbFrbfeylVXXcWyZcsoKSkhNTWVESNGkJCQEO4aRURERGSvkMJbnYSEBDIyMgAYMWIEfn9ot/qKiIiIyJEJOby98cYbPPjgg+Tl5WFZFi+//DKPPvoobrebBx98sH5ARREREREJn5DuNn333Xe55ZZbmDBhArNmzaqfZ+6EE07g888/Z/bs2WEtUkRERESCQup5e/LJJzn//PO58847CQS+n2T47LPPpqCggJdeeomZM2eGq0YRERER2Suk8LZ582ZuueWWgz42fPhwHn300SYVJSIiIkfGNjblvlrKfTVUBXxUB3z47AAB28bG4LAsnJaDGIcTj9ON1xVDosvTaucClR8WUnhLT09n48aNTJo06YDHNm7cSHp6epMLExERkQPVBvwsy9/Gkryt7KosodRXg6FxQWxJ/lbe2PotKTFeUmPj6OhJIMEd2uTw0vJCCm+nnHIKjzzyCBkZGUydOhUIDh+ycuVKZs+efcips0RERKTxagN+dlWWsruqlILqCuz9wprLcpAY4yHO6cbjchPjcOK0HFiWhW0MAWNTG/BTFfBR6aulzF9Dpb+WSn8tOytLAEhye+gSn0Ln+BRinU0ajEKaWUh7Z+bMmaxbt46ZM2ficATveZgxYwaVlZWMGTOG6667LqxFioiItDfGGHKry9laXkhuVXmD3rVMbyJJbg8J7lhSY+PwOt1YlnXE687yJpEZl8S7W1eytbyIwppKSn3VrC7ezZriPXSJT6ZXUgcS3Z7maJo0UUjhLSYmhjlz5jBv3jy+/vpriouLSUxMZNy4cUydOrVRB5CIiIh8z28H2FJexJayAioDvvrlSW4PneKTGZ7amWldBvD5rvWU1FaF9B5Oy0G3hDQGpWbTOT6F2oCfnZUlbK8opri2im0VxWyrKCbbm0T/lEydUm1lQgpvV1xxBRdddBGTJk066HVvIiIi0ji1doCcsgI2lxXgs4MjObgdTrrGp9AlPpWkmGAvWEpsXNjfO8bpokdiOj0S0ymqqWRjaT67q0rZVRU8Vds9IY3+KZm4Hc6wv7c0XkjhbdGiRVxyySXhrkVERKTdqQn42VyWT05ZIX4THDc13hVD76QOdI5LwekIaUjWkKXGxjGmYzdKa6v5rng3udXl5JQXsquqlMGp2WR7k1q0HjlQSOFt0qRJvPzyy4wYMYLYWHWlioiINJZtDBvLCthcUUhgb2hLdMfSNymD7LikiF+ClBTjYVxGD/Kry1lRuJMKfy1L87eR5U1iclbviNbW3oUU3mJjY5k7dy4ffPABXbp0OWBoEMuyeO6558JSoIiISFtiG0NOeSGrAsX4yoOhLTnGQ9+kDDK9iREPbfvr4ElgSnYfNpTksaE0j91Vpby7bSXdE9Ppl5wR6fLapZDC2+7duxk5cmT99/sP9KeB/0RERA60oSSXlzctJae8EACP08XAlCw6xSW3utC2L6floH9KJpneJL4p2EaFv5ZZ337E2T1HML3zgFZde1t0xOHtrbfe4uijjyYlJYV//OMfzVmTiIhIm1Luq+a/m5cxf88mIDguW4blYVDHznhioufyo5RYL0dn9WZdSS6bygp4ZfM3bCsvYkbfccRobLgWc8RXQd58881s3bq1wbInn3yS/Pz8sBclIiLSFhhjmL9nE7cvfqc+uE3O6s2pXQaS5fDitFr2ZoRwcDmcTMjoyfm9R+PAYmFeDrNWfES5rzrSpbUbR3zU7H8qNBAI8Je//IU9e/aEvSgREZFot7uylFkrPuLZdV9T4a+hc1wKNw+fzoy+4/E43ZEur0ksy+LYTv2ZOXQa8a4YNpcV8OflH5BfXR7p0tqFJkV+XdsmIiLSkG1s3tu2mj8sfZd1Jbm4HU7O7jGC3448id5JHSNdXlj1T8nk5uHTSYuNY09VGfct+x87KoojXVabF339tSIiIq3UnspS7l/+Ia/lLMNvbAanZnPn6FM5seugFh+vraVkxSVzy/AT6BKfQqmvmlnffsS28qJIl9Wm6epCEZEWZKoroawASgsw1eVQUwW11WDbOH21ZBQU4fDnYicmY8WnQGIqJKZjuWMiXbochm0Mn+5cx6s5y/DZATxOF+f1Gs1Rmb3axZ2YKbFxXD/0eP6y8mO2lBfy0IqPmDl0Gt0S0iJdWpvU5PDWHg5KEZFQmNpqzIalmJ0bMXlbIW87VJYe8vkuoBNAzgIM7DMNuQXJHSE9GyurJ1Z2L8juhRXjbe4myBHIry7n+XULWFsSvAZ8YEoWF/UdT5onPsKVtax4dwwzh07jkZWfsLmsgIdXfMKNw46nU3xypEtrcxoV3q6++mpiYhr+9XfFFVfgdje88NKyLD788MOmVyciEkWMMVBeBEV7oHgPVJZiH+yJ3gRITIe4JKzYOIjxgMPC7w9QXJBPSpwHp78GKoqhrDDYM1eSCyW5mE3Lg6HO4QwGuO6DsfqMhPTO+mO6hRljmLdnIy9tWkpNwE+Mw8n/6zmSKdl9cbTTfRHniuG6IdN4eOXH5JQV8PDKj7lp2HQ6ehMiXVqbcsTh7ayzzmrOOkREopapKoO8bZC/A2qrGj6Y3gmrU1/I7I7VsWvw+70TjO/PX1nJtjVrSBg4kJi44OTjxphgb13hLkzedti9CbNzI5Tmw471mB3rMV+9DmnZWP3GYg0+Ciu5bV0U3xqV1Vbz3PqvWVG4E4A+SR25uN8EMryJEa4s8rwuN9cOPoYHvv2QnZUlPLzyI24efgLJ6ikOmyMOb/fee29z1iEiElWMsaFwF+zJgdKC7x9wOCE1E1IyoVMfnGNPatL7WJYF8ckQn4zVdcD371+ch9m6CrPpW9iyKhjuvn4T8/Wb0G0gjuHHQu+RWG30IvlIWl20i2fWzqfUV43LcnBmj+Ec17k/jigcs625xLtjmTl0Gvcv/4C86nIeW/UpNww7PtJltRm6YUFEpBFMwB/sZdu1EWoqv38gNRM6dIXUTCyHM7jsED1s4WCldMRKOQaGHYOpqQqeTl09D7asga1rsLeugaQOWKOOxxo6pdnqaE/8doA3tnzL/7avAaBTXDK/GDCJzvEpkS2slUqO8XLdkGO5b/n/2FpexJzv5nFxjzGRLqtNUHgTETkCxrYhdwvsWAe+muBCVwxk9oCM7lixkTslZMV6sQZOgIETMKX5mG8/x3z7GZTmYz79N2bhu9CpD6RmYWkKo5DkVpUx57t5bNk7J+mUrD6c22uUpoT6AR29iVw1aCqzVnzEisKdvOZcwWB053RT6agTETkMYwwU7IStq7/vaYvxBsNQx66tLgxZSR2wJp+NGX8aZs18zKJ3oSQfNiwFdyymS3/I6IalU3xHxBjD17mb+dfGxdQE/MS5Yrio73hGduga6dKiRq+kDlza/yj+uuYL5uVtxnJnMjDSRUW51vVbR0SkFTH522HpB8E7RwHcsdC5XzD81J0abaUsdwzWsKmYwZMw3y3AfPYSVJfD5m+DNz30GKobG35Ald/HixsWsTAvB4C+SRn8fMBE0mLb1xAg4TCyQ1fO6DGc13OWM8+3hxFl+QyN6xbpsqKWwpuIyH6MHYDvvg7eDGAHwHIEe9o69Wl1PW0/xHK6sAZPIlBbDZu+DZ72rSqHNfMxaZ2gx2CNF3cQ2yuK+OuaL8mtKsOBxWndh3Jy10G6KaEJTuoyiC0l+XxTtINnNy3kt8np7W4svHCJrt9CIiLNzJQVwsZvoLoiuKBjV+jcH8sTF9nCmsjaOy6c6dgVtn8HuzdD4U4oycP0GAIdumicuL3m7d7IvzYuxmcHSI2J4xcDJtFHvZRNZlkW5/cYydbifAr8NTz13ZfcqDtQQ6LwJiLC3mvbdq6HbWsBA7FxOE64BFNZChUlkS4vbCyXG3oMxXTsBpuWBwcC3vhNcKiRnsMOOQZde1Ab8PPixsXM37MJgMGp2fy8/0QS3O13m4RbjMPF9NhOvOnbzuayAl7e9A2dNQNDo6n/V0TaPVNTBau/gm3fAQbSO8Mx52P1HRXp0pqNFZ8MQyZD1wFgWVC0G779FFOwM9KlRcTuylL+tOx/zN+zCQuLM7oP41eDj1FwawZJjhh+2nM0AJ/uWkdOWcEPvEL2p/AmIu2aKdwJ334anCze4YTeI6DPKCx3bKRLa3aW5cDq3A+GTIG4JPDXwvrFwflYA/5Il9diFudt4Z5l77Gjspgkt4dfD53GKd2GtNsprlrC4OQsTu46GIAFuTlU+msjXFF00WlTEWmXjDHB4T92bQwuiE8JhrZ2OAejFZ+MGTIleDPDjnWQvx0qSzH9xmK14QvKA7bNy5uX8snOdQD0S87gFwMmaRqnFnJ696GsL8llQ2ke3+RvZ2JmTwXmI6SeNxFpd4zfB2sXfB/csnvD4MntMrjVsRyO4PRbA48Cd0xwPtUVn2GKdke6tGZRWlvFrBUf1Qe3k7sOZubQaQpuLchpObik/0TcDidFtZVsKM2LdElRQ+FNRNoVU10Oq76A4tzgECB9RmN1H6w5QPeykjvA0KmQkAoBP6xdiNn4TXCGiTYip6yAe755nw2leXicbq4aNIUzewzHqWFAWlwHTwJjO3YHYH1JLkX7Tjknh6QjVUTaDVOSByu/CI5z5vYEe9s6dI50Wa2OFeOFQZMgq2dwQc5K7DcewdRWR7awMPhqzybuX/4BRbWVZHmTuG3EiQxP7xLpstq1nonpdIpLxgDfFGzDbwciXVKrp/AmIu2CydsKa74Gvy94fdvQKVgJKZEuq9WyHA6sHkOhz6jgjRybV2C/9GdMlA6bErBt/r1xMc+t+xq/sRme1plbR5xIVlxSpEsTYGhaJ7xON5V+H6uKdkW6nFZP4U1E2jyzayNsXEb9MCCDJ7Xr8cwaw+rQBUZNB28i5G7B/tfdUTecSGltNQ+v/Lj++rbTug3likFT8LrcEa5M6rgdTkbs7QHdVlHMzsro/COhpSi8iUibZYzBbFsDW1YFF2T3Ct5R2srnJW1trOSOOH7yG0jJhNIC7H/fi9m2NtJlHZGt5YXcs+w91pXk4nG6uGrQFE7vPlR3NbZC6Z54+iQFZ7JYUbiTmnY0XE1jKbyJSJtkjA1rF8KO9cEFXQdAt8GaAipEVkoGjp/cFrwzt6YS+9VZ2GsXRrqsw1qWvy14fVtNJZm6vi0q9EvOIMntwWcHdPr0MBTeRKTNMQE/Zu6c4JhlAD2GYnXup+DWRJY3Ecc5Nwavgwv4Me88hb3qy0iXdQBjDO9vX82Ta76g1g4wKDWb20acQFacpmFq7RyWxfD0zljAzsoSdleWRrqkVknhTUTaFBPwY7/zV8x3C4LTPvUZjVV316Q0meWOwXHalVhDpwAG8/4z2N9+Fumy6vntAP9Yv4BXNy/DAMdk9+VXg6fidcVEujQ5QskxXnoldQBgRdFOfLr79AAKbyLSZhg7EOxx27AUnC4YdqyGAmkGlsOBdfxFWCOOA8B8+Dz20g8jXBVU+Gp4ZOWnzNs7P+mPe43mJ33Gavy2KNQvKYN4Vww1AT+r2+hA0U2hI1pE2gRj25j3nsasWwQOJ47Tr1Jwa0aWZWEd+xOs0ScCYD79F/ai9yJWT2F1Bfcv/4C1JXuIdbq4evAUpnXuH7F6pGmcDgfD0oI/v9sqisivLo9wRa2LwpuIRD1jbMz/ngmeKnU4g6f1eg2PdFltnmVZWFPOxRp/OgDmi5exF7zT4nVsryjivuX/Y1dVKSkxXm4ePp2haQru0S7dE0/3hDQAvi3Ygb8NzfLRVApvIhLVjLExHzyPWf0VWA4cp/wSq8/ISJfVbliWhWPSmViTzgLAzHsVe+kHLfb+a4v3cP/yDymuraJTXDK3jDiBLvGpLfb+0rwGpmTicbqpDPhYW7In0uW0GgpvIhK1jDGYT/+DWfkFWBbWKZdh9RsT6bLaJcf407AmngGA+fTf2Cu/aPb3XJS3hUdWfkJ1wEffpAxuHDadtNj4Zn9faTkuh5NhaZ0A2FxWQEltVYQrah0U3kQkaplF72K+CV4ob534cxz9x0W4ovbNmnA61ugTADAfPIe9dlGzvdfHO9Yy57t5+I3NqPSuXDf0WOLduqO0LcrwJpK9dxqzFYU7McZEuKLIi3h4Ky4u5vbbb2fKlCmMGjWKn/zkJyxevDjSZYlIK2ev+ALz5asAWMecj2PQUZEtSPZeA3ce1tCpYAxm7t8wm5aH9T2MMby7dSX/2bQEgGOy+3HZwEm4NWtGmzYoJRun5aC4toptFUWRLifiIh7err/+epYvX86sWbP473//y+DBg7n00kvZuHFjpEsTkVbKbFyG+fA5AKyxp+AYNT3CFUkdy7KwjrsQa8B4sAPYb83GbP0uLOs2xvBqzjLe2PItEJyj9Pzeo3FoKJA2z+ty0z85A4A1xXuobedTZ0X0iN+yZQvz5s3jjjvuYMyYMfTq1Yvf/va3ZGZm8vbbb0eyNBFppcyO9dhvPwnGYA2ehDX57EiXJPuxHA6sE38OvUdAwI/95qOYvG1NWqcxhn9tXMz/tq8B4JyeIzm9+1DNmtGO9EhMJ9Edi88O8F07v3nBFck3T01N5amnnmLIkCH1yyzLwhhDSUlJSOs0xlBZWRmuEg8qXL8smnLevqqqqsHX9uBwbQ7HPmmt11G09L5uDcc3QE1NDV6vl9ra2vqarMJduF77C1bAh919CP7J50F19QGvtSwLj8eD3+/H+Hwhvb/DH8ABBAJ+7BDXYfn9xADV1dVHtD1qa2sPaDM0bVtaloXX623StmhsO+pNuxhXVTmOnRuwX30Y98hjCRxktHx77xAQ9iGGgrAxzK8sIqdkJxZwbvcRHJXeI+SfiaYem035Gfn+2AxuB5+v8T1I/r29Tn6/H1+I+7Sp6/A7gvGhqqqqUdtz399n3x+bviOuYWBSBgsLtrG1vIjs2ETi99bR6GPzIJrrM6A5fndHNLwlJSUxderUBsvmzp3L1q1bmTx5ckjr9Pl8rFmzJhzlHZTb7WbI4ME4nE27vsIOBFi5alXIP3h1cnJymvT6aLR/m8OxT8K1P5pTS+zr1nJ819WRmvr9kA+mogT73Segtgo69cH1o6twu2MPu56SkhJqi/JCqsHrTSYNKC0to7ogtHXEOj2kG4PH4znC9/SSkpLSYFlTt6XX62XQoEEUFxfhK84PaR2Nbcc+744581rs//wJCnYyeOlHbO86EJ/z4B891QcJ4jbwXaybfF8VDiwu6T+RcRk9Gt2GOoFAgFVNPDYHDx6Ms4k/IxUVFUDwuu/GSjYuyIKysjLySkK7/qup6zDxidAVNm/eHFI4ycnJqT82i4qKya8oO+LXplmxFJoavi3YQSaxmJCOzYaaely0tIiGt/0tWbKE3/zmNxx33HFMmzYtpHW43W769OkT5sq+Z1kWDqcT35oFmMojP9garCMuEffA8fTt2zfkpF9VVUVOTg49evTA6/WGtI5oc6g2N3WfhGN/NKeW3Net5fiuq6Nw4Yd48eMEHMs/xiorxHgTsXsMI/DtoYeisNIycfccSkpKMrYztBocCcG725KSEkmwQptb0ZHWEcuy8H+3APsIPpwCAT9lZWUkJibidLrCti0BUlJSMa7Qeowa244Daph4Bo5PXiShooQpO9exoO8obMf3V+3Ytk11dTUejwfHvssxfIsh3wIHFlcMOppav4+Pt60OqR2Jbi9js3o2eXs6nU4W7d5Mma/xoSXTm8zgDp1JSEggt6yElJQU3O7GfRQnJiQGvyYmEogJLUQ2dR0pscHfRT179mx0z1vd77O4uDgAUlNTsOIO/4fYvpICKXyZt5kqEyDP4cOyrJD3B4TnuDicujaHU6sJbx9++CE33ngjw4cPZ9asWSGvx7Ks+gOiOTlrq6CmIrQXu4KbPRwfxF6vt0Xa25ocqs0h75Mw7o/m1JL7urUc3/6yIhxOcOYsh7ICcLmx+o3FZfuh5jCnm3w1ADidLpxud2hv7nKGbR2OmiocR7A9jc+Hrzgfh8vC5XaHdVu6XC5ooXYcoEMnrLNmUvuvu+lYVsTILatZ1msY7Hf60eFw4Nx716htDCsIsBuwgOkpnRie3oXPd62nwg7tYnWXCb4uHNuzyvhDqqPGBP8QqOu5c7tduBu5X1x7ey5dLhduO7R92tR1uFzB14S6Lb1eb/1rXS437kZsS7fbzYCUTFYW7WJJwTbO8VWHvD8gvMdFS2kVt+j885//5JprrmHKlCn87W9/a3L3p4i0HY6d66BwV/CDvt84LG9CpEuSEFgZ3fhu+DHYlkXngl30377+kM+1jWEZAXZhsIDROOnu0X6X73VPSCPJ7aHWDvDmlhWRLqfFRTy8vfjii/zhD3/gggsu4OGHHyYmRoMsikhQbMF2HLs3Bb/pNQIrKT2yBUmTFKd3YkWPwQD02bWJzvk7DniObQzL9wtumRoKRPZjWRaDU7MB+HzXBopqmvdGxdYmoj8Rmzdv5p577mH69OlcfvnlFBQUkJeXR15eHmVloV1vIyJtg9n6HfHbVwW/6dwPq2PXyBYkYbG9Yxc2ZPcCYOjmlaSUF9c/ZjB8S4Cde4PbKAU3OYx0Tzy9EtMxGJbkb22V1yw3l4he8/b+++/j8/n44IMP+OCDhhMZn3XWWfzpT3+KUGUiEkmmtAD7nSewMNip2Ti69I90SRJGa7v0JaGqnKziXEavX8oXA8ZRAazBsAPqg1uWgpv8gAkde7K1vIg9VWXsqSoja+80Wm1dRMPbFVdcwRVXXBHJEkSklTF+H/ZbT0BVOX5vEvQYilMDsbYtlsWy3sM4avUCkqrKGLdhOU/2HcHWvbt5uIKbHKHEGA/Tuwxk7rZVrC7eRUdvAs52cOy0/RaKSFQxn7wIezZDbBxl3UeA5qxskwJOF4v7jaLGFcPC5FS2xAavdx6Cg87t4MNXwuekroPwOt1U+n1sLiuIdDktQj8hItJq2Cu+wKz4HLBwnHo5dmz7GganvamK9fLvgWN5rWtfACaVFNLdUliXxvE43YxI7wLA+pI8qgPRMdBuUyi8iUirYHbnYD7+JwDWUWdg9RjyA6+QaLfT2CzYOzTUCbtyuGDdN2QWte85KyU0PRPTSYnxEjA2a4vb/jGk8CYiEWeqyrDfehwCfug1HGv8qZEuSZpZvrFZTnDA2q4GBuydk3r4xm+JryqPZGkShfYdOmRbRTEltW173m+FNxGJKGPb2O88BWWFkJKB46RfYOmapzat1BiWEMAGsrAYhMXyzB4UJKTgtgOMXv8NzkBoo+VL+5UaG0enuGQA1hTvbtNDh+g3pIhElPnqNdi6GlwxOH50NZZH17m1ZVXGsBA/fiANixE4sbAwloPFvYdR7Y4lsbqC4ZtWQBv+8JXmMSAlEwcW+dUV5FW33R5chTcRiRizYSlm4bsAWCf8DKtDlwhXJM2p1rZZiJ8aIAEYg7PBMDA17liW9B2JbVlkF+2h167NEatVolOcK4YeiWlA2+59U3gTkYgwhbux33saAGvUdBwDxke4ImlOPjvAZxUFlAMeYBwu3AcZv684IYVV3QcCMGD7OjqU5LdsoRL1+iRn4HY4KfPVsK2iKNLlNAuFNxFpcaa2OniDQm11cOqro8+JdEnSjGxjeGbtfPICtbiAsbjwHmbg5a0du7KtQ2csYOTG5Xhq2vbF5xJeMQ4nfZM6ArC2JBe/bUe4ovBTeBORFmWMwfzvGSjYCfHJOE67AssZ0clepJm9VbKbJflbcRCcaD7ph2bMsCxW9hhEcVwSMX4fozYuBzvQIrVK29A9MY04p5uagJ9NZW2v91bhTURalFnyP8y6xeBw4jj9Kqz45EiXJM1ovu3jvdLguFvj4lLpcIR3EtsOJ0v7jMDndJFaXkyn7xY0Z5nSxjgtBwNSsgDYWJrf5gbuVXgTkRZjtn2H+eK/AFhTf4zVqU+EK5LmtM4EeMHUAnBK18H0imncncRVnjiW9xwKQMbGZZgN34S9Rmm7suOS6gfuXVeSG+lywkrhTURahCkrxH77STA21sAJWCOmRbokaUZ7jM1f7WoCwOi4FE7vPiy09aRlsimzOwD2+38ntrw4fEVKm2ZZFoP29r5tLS+izFcd4YrCR+FNRJqd8fuw33oCqsqgY1es4y/C+qHrniRqlRvD43Y1lUBPHFyU1g1HE/b3d137U5GSCTWV9J/3Bg4N4CtHKM0TT6Y3EYA1bWjqNYU3EWl25tN/w+5NEBsXvM7NHRvpkqSZ+I3hKbuaPAzpWFzh8BDjaNpHjXE4yBl9AnjiSSzcxYAVX4apWmkPBqZkYQG51WXkt5GBexXeRKRZ2au+xHz7KWDhOPkyrJSMSJckzeglU8t6bDzAVQ7PD99ZeoR8cYk4TroUgB4bl5O1fX1Y1ittX4I7lm4JewfuLWobA/cqvIlIszG5WzAf/hMAa+LpWL1Cu+5JosNnto8vjB8L+Lkjlk5hnqPW6jWc7QMnADB0yYfE6fo3OUL9kjNwWQ5KfNXsqCyJdDlNpvAmIs3CVJVjvzkbAj7oOQxrwumRLkma0ToT4KW9d5aeYbkZajXP2H1bhk2hsEMn3P5aRn79rq5/kyMS63TRp27g3uI9BEx0D9yr8CYiYWdsG/vdv0FpPiR3xHHyL7DC3AsjrUe+sXnKrsYGxlhOTrDczfdmDgffjDuZmlgvySV5DFz+efO9l7QpPRPT8ThdVAV8bCkrjHQ5TaLfpiISdmb+G7BlJbhicPzoaixPfKRLkmZSbQxP2tVUAN1wMMOKbfY7iWu8CSwfeyIA3TevIGuHrn+TH+Z0OOiXHLzmdn1pHr4onrVD4U1EwsrkbcMseBsAa/rFWB27RrgiaS62MTxn17ADQxIWVzhiiWmhIWDyM7uzsd9oAIYu+QhPRWmLvK9Ety7xqSS4YvHZATaU5kW6nJApvIlI2Jiqclg1DwBrxHE49l5cLm3Tu8bHMgK4gMsdsaS28KnxdYMnUpSWhdtXw4hF72G1wQnIJbwclsWAlEwANpcVUOWPzmmzFN5EJCxMwA/rFgVvUOjUB2vqeZEuSZrRUuPnHRP84PuJFUMvy9niNRiHk2XjTsLniiGtYBd913zd4jVI9Mn0JpIWG4dtTNROm6XwJiJNZoyBTcuDMyjEeHGcdgWWs3nuNpTI224CPGfXADDNcnGUoxlvUPgBVfHJrBx1HAC9v1tEeu62iNUi0cGyLAbunTZrW0URJbVVEa6o8RTeRKTpdm+Cgh1gWTDkaKyE1EhXJM2kzBiesGuoBQbi4GwrJtIlsatrP7b2GIwFDF/0PjE1lZEuSVq51Ng4srxJAHyTvz3C1TSewpuINIkpLYAtq4PfdBuMlZoZ2YKk2fiN4W92NYUYOmJxqcODs5XMUbt6+FTKEtPwVFcwbPEH0AZG0ZfmNSAlEwvYUVnM+ig7farwJiIhM7XVsH4xYCC9M2T1jHRJ0oxeqi6pn/rqSoeH+FYS3ABsl5tl408m4HCSsTuHHhu+iXRJ0srtO21WtN15qvAmIiExth28QcFXA3GJ0Gt4s4/vJZHz2c71fOGrrJ/6KrsVDrpcltyBNcOmADBgxTySivZEuCJp7QalZjEpsxfHZveLdCmN0vp++kQkOmxZBeVF4HRBv7G6QaENW1dbyb83LQaad+qrcNjaayi7O/XGYWxGLpiLy1cT6ZKkFXNaDnokpuNxRe6mm1AovIlIo5m8bbBnc/CbPqOwPAmRLUiaTb6xeap4J7YxjHF5m3fqq3CwLL4dfTxVcYnEV5Qw+JtPdP2btDkKbyLSKKaiODgsCEDnflipWRGtR5pPtTE8YVdTYWy6JaQxw5sSFafG/TEelo09Cduy6LxtLZ23rol0SSJhpfAmIkfM+Gpg7SIwNqRkQJf+kS5JmoltDM/aNezEkORwctWgKS029VU4FHXoxPpBwRk+Bn/zKfFlRRGuSCR8FN5E5IgY2w7eWVpbBZ546DM6KnphJDRvGR/L66a+Su5EamxcpEtqtI39x5DfsQuugI8RC+ZiBfyRLkkkLBTeROTIbF0FpQXgcEK/cVhRdoGvHLlFtp/39k59daEVQ68Yb4QrCpHlYPnYE6mJ8ZJckkePZZ9EuiKRsFB4E5EfZPK2we59blCIS4xsQdJstpgA/zDBOzSnW27GR3Dqq3Co8Sbw7ZjpAHRatwSzcVlkCxIJA4U3ETksU17c8AaFtOyI1iPNp8TYPGnX4AOG4OTM1n5n6RHKy+7J5j4jAbDf/zumrDDCFYk0jcKbiBySqa2GdQuDNyikZuoGhTbMZwxP2jUUY8jC4ueOWBxt6JrGtUOOojwtC6orsN99CmMHIl2SSMgU3kTkoL6/QaEaPAnQe5RuUGijjDG8YGrJwSaO4NRX3ja2r22ni7VHnQExHtixHjP/zUiXJBIyhTcRObgtK6GsMDiDQv+xukGhDfvA+Fhg/DiAyxweMlrh1FfhUJ2YinX8RQCYBe9gtq6OcEUioWmbP6Ei0iRmx3rYkxP8pvcoLK9uUGirlhk/r++9s/RcK4YBljPCFTUvx4DxWEOOBgz23DmYipJIlyTSaApvItKA2boa1i4IftOlP1aaZlBoq7aYAH+3azDAFMvF1FY8Z2k4Wcf+BNI7QUUJ9ntPY4wd6ZJEGkXhTUTqmcLd2G89EZwLMr0zdO4X6ZKkmRQam9l77ywdhJPzrJh2c02j5Y7FceoV4IqBLaswi96LdEkijaLwJiIAmKpy7Nf/AjWVkNwReo9oNx/m7U2VMTxuV1OKoTMWv3DE4mxn+9rq0Bnr2J8CYOa9htm5IcIViRw5hTcRwQT82G/NhuJcSOoAw6ZiOdr2tU/tVcAY5tTNWYrFVW3wztIjZQ2ZjNV/PBgb+52/YqrKI12SyBFReBNp54wxmA+fh+1rIcaD48xrsKJ1OiQ5LGMM/zG1rCaAG7jKEUtaG72z9EhYloV1/AxIyYCyQuz/PYsxJtJlifyg9vtTKyIAmIXvYFbNA8vCceoVWB26RLokaSZzjY8vjB8L+Lkjlu5t/M7SI2HFeoPXvzldsPEbzLKPI12SyA9SeBNpx+yVX2LmvQaAdcxPsHoOjWxB0my+rK3grX2GBBnRTu4sPRJWZneso88FwHz+EmbPlghXJHJ4Cm8i7ZTZ9C3mg+cAsMaejGPkcRGuSJrLsvxtvFgdHM/sJMvNsVE+2XxzsEYeB71HQMCP/c6TmNqqSJckckgKbyLtkNm1CfvtJ8DYWAMnYk3+f5EuSZrJ+pJc5qz9CgMcZbn4URuZbD7cLMvCccIlkJgGxbmYD/+h69+k1VJ4E2lnTNGe4JAg/lroPhjrhJ9pSJA2akdFMbNXf4bPDjDUFctP29FYbqGwvAk4Tr0cLAfmuwWYFZ9HuiSRg1J4E2lHTEUJ9qsPQVU5ZHbHcfqVWE5d+9QW5VaV8cjKT6j0++id1IFfeFPb3VhuobA69cGafDYA5pMXMbtzIluQyEG0qvA2e/ZsZsyYEekyRNokU1WG/d8HoSQPkjviOPM6DQnSRhVUV/DQio8orq2iU1wyVw+aSkw7HhKksawxJ0HvkcHr396erfHfpNVpNT/Nzz77LI888kikyxBpk0x1JfYrs6BgB8Qn4/h/12PFJ0e6LGkGRTWVzFrxIYU1lWR6E5k5dBrx7thIlxVVLMvCcdLPg+O/lRYEJ7DX/KfSikQ8vO3Zs4df/OIX/OUvf6Fnz56RLkekzTG1VdivPQS5W8GbiOOcG7FSMiJdljSDktoqZq34iPzqCjp4Evj10ONIVu9qSKzYOBynXwVON+SswCx4J9IlidSL+MUuq1atIjk5mTfffJPHH3+cHTt2NGl9xhgqKyvDVN2BLMvC6/Xi9/sxPl9o6/D7iQGqq6tDvpuptrYWt9tNVVVkb2cP18XPR7Id6tq6f5ubuk/q9kdVVVWrvLvsUO0+Ir5a3O8+gWPXJkxsHL5Tr8J4U+AQPyN12zLg9mJi/SHVa7m9OICampqQt6dlWcTGBnuL/P7G71OHP4ADCAT82CH+nNavI8aL8Ye4LVyxjVqH7fLjTjHY3gT8Tlf9tjySY7PcV8Nj674kt7qM1BgvV/Y5ithA8LVN3qd17QhxezoCwW1pG5uAHTjgcdu2G3w9GJvgY36/H1+I+9TvCH7kHfGxmdgRa+r5uD7+B/ZXb+BL6wLdBxEbG4vf7wupDn8guA8CgeB28Pkav0/q1tGkbdHEddRty8Z+jtXW1uL1eqmtrcXhcODxeELelhCmbbG3Lc31GdAcn9MRD2/Tpk1j2rRpYVufz+djzZo1YVvf/rxeL4MGDaK4uAhfcX5I64h1ekg3Bo/H06Q6khITWblqVcgHbFO53W6GDB6Mw9m0UdrtQKBR7cjJyWnwfVP3idtvyAA2b94c8TB8OPu3+4dYtp+eK+cSW7SNgDOGDYNPoSqvDPIO/fORmJhI3759cQ8a36RajTH14asp63B64ine3fgBU73eZNKA0tIyqgvyQnr/+ORMko3BPbDp26Ix69i/n8wYw7Zt2ygrKzvkayqNn3ert1Foaom3XJzoyGLPpi3sIbz7tLzGT2V+47dnQnoXkgn+fj7cH9fV1dWHfKzu90NlZSV5haH97o1L69j4Y3PEMdh5W2DF57g+fBbHhbdDbCxlZeXklRQ1uoZk44Is6rdDcXFxyOsoKysLqYZwrMOTko4J4XPM6/WSkpLSYFlJSSl5ZSWNrgHCsy1MfCJ0bf2fAfuKeHgLN7fbTZ8+fZpt/XU9TSkpqRhXaL1OjrSOWJaF/7sF2BWH/oV8OHZsHJ4hR9GtWzdiYmJCWkdTWZaFw+nEt2YBpjK0dlhxibgHjqdv374/+BdPVVUVOTk59OjRA6/3+4+4pu4TKyEFgJ49e7banreDtfuwfDW435+Do2gbxhVD4NSr6ZHd6wdf5nA4sCyLxesXU1pWEFK9WRk9GdSlH0u2rKS0JLTglJjckTHdh+CM9ZCSkoLL1bixyRwJSQAkJSWSYB3Y03NE60hKCdu2WLxpOaXFu3/w+ca2qamtJTYmBsvhICkxnTF9x9CtW7dD9koV1VbyxLqvKDS1JLpi+VX/o8nwJHzfjjDs06SULMb0Gk5CSipxtY2/eN8RFw8Efz/HxcUd8Lht21RXV+PxeHA4Dn41j9sdPAbi4uLo6OzQ6BoA0hJSg9tiy0rKGnFsWh06MyIxjcSyQipefYj4C+8gMTGRQEzj/3BNTEgEgu2gpIiUlBTc7sZ9FNetI9QawrGODnu35aLdmynzHXng8fsDlJWVkZiYSOfENAZ36ExycjLGE9r4g+HYFimxwd+rzfUZUPc7PJzaXHizLOugvxzCzeVygTvEwS5dwQPMUVOFo6YipFXUdbTHxsYe+Qd6M3HWVkGI7cAVPAQb0wav13vQfRzyPgmhhkg4VLv3Z2oqsd/6K+xYD+5YnGdci6vbgEa9V0V1BeWVpSHVWbX3F3l5ZSllpaH1kDj2ucDe5XLXf3Afsb0/Y06nC2eoP6d7e5TDsS0qao5sHQE7QGVlJXFxcTgdTpzuYK/GoXo39lSW8ui6LymqqSQtNo6ZQ6eR6U066HOb0g6nN/gB6XQ6Q9uee7elw3LgdBz6A9bhOPTjjr2XaLtcLtx2aPvUVbdPQzg2F/UawuSV8/EW7sJ89hKuQeNCqsO1d2ge595a3G5Xo4/vunU0bVs0bR11r68yfirsIz/16wv4yK8ow4qLpcYEmlTDvnU0aR17/zhs7Z8B+4r4DQsiEj6mqhz7vw8Eg1usN3hXaSODm0SH7RVF3P/thxTtvav0puHTDxncpOmqYuNY3js4969Z/jEdc1ZFuCJpzxTeRNoIU16M/dJ9sGcLeBNwnHsTVqfmu4RAImdTaT4PfvshZb5qusancuOw6aTFxke6rDYvNyWD3X1HA9B74VySinIjXJG0VwpvIm2AKckPBreCncFx3M67BSuje6TLkmawLH8bD634qH7mhOuHHUdSTOg3P0nj7O4/FnoMxRnwM3r+W8RUN9/oBiKHovAmEuXMrk3Y/7obinMhqQOOH9+Gld4p0mVJmBljeH/7ap5c8wW1doBBqdlcN2Qaca7I3LDUblkOHKf8kqrENLxV5Yxa8A7WQYY/EWlOreqGhT/96U+RLkEkqph1i7HnzoGADzp2xXHmtViJaZEuS8LMbwd4ccNi5u3ZCMDU7L78uPdonJryKiIsTxxrpvw/hr7/HGn5Oxm07DNWjQrfkFciP0Q/+SJRyBiDvfBd7LefCAa3nsNw/PgWBbc2qMJXw19WfsK8PRuxsPhxr9H8pPcYBbcIq0pKZ9m4kzBA980r6LppRaRLknZEP/0iUcYE/JgPnsN8+QoA1ojjcJzxK00y3wYVBXz8adn7rCvJJdbp4urBU5jWuX/YZjaRpsnL7snaIUcBMHjZp6TmN22GIJEjpfAmEkVMWSH2y/djVn4BloV1zE9wTPsp1mHGzpLoYzBsMQHeL8slt7qctNg4bhl+AkPTOke6NNnPpn5j2NmlHw5jM2r+O8SVF0e6JGkHWtU1byJyaNbW1dgf/wOqyyHGi+OUy7B6DY90WRJmfmA5ht0ER3ofmtaJn/WbSIK7adONSTOxLL4dfTxxFcWkFOUyZt6bfHXsefh1B7A0I/W8ibR2doCszQuIefeJYHDL6I7jwtsV3NqgUgxLvTHstsACRnqSuGrQVAW3Vs52uVky8UdUeRNIKC9i9Ne6A1Wal8KbSCtmyopwv/04WVuXAGANPxbH+bdhpWREuDIJJ9sY1psA8zFUOxx4DEzEyUBPIg5d3xYVarzxLJ50Bj5XDOl52xm69CNohXMlS9ug06YirZAxBrPyC8znL+GoqSLgdGMfewGeYUdHujQJs2Jj8y0BygAsSPcHGO504dXdpFGnLLkD34w/mTFfvUmXLWuoSEhh44BxkS5L2iCFN5FWxpTkYX/wPGxdDYCd0Z113SfRq8/oCFcm4RQwhnXYbMIGIAYYaCySanzExIU2wbZEXn5WD1YPP4Yhyz6h/6r5VMansKtrv0iXJW2MwptIK2GMjVn2SXAIEF8NON1Yk87CN2ASNWvXRro8CRNjgjcjfEeAuomVOmExGCdObDTZUvTb2nsY8eXF9NzwDcMW/4/aWC8FGV0jXZa0IQpvIq2A2boG+/OXIXdLcEHnfjhO+BlWaiZU6uO8rSg2NquxKdp7J6kHGIKTzL2nSAO6RKrNWDNsMp6qMrJ3bGD0/Lf4eso5lKbqWlUJD4U3kQgyBTuDoW3zt8EFMR6syf8Pa/gxWLrmqc0oD/j4xvjZuTe0OYBeOOiNA5duSGibLAfLx56Iu7aaDnnbGTvvdeYfcx7EpUS6MmkDFN5EIsCUFWEWvIVZ8XnwjjSHE2vYVKwJP8KKS4x0eRImxb4anl+3gPlFO/Ze2QZdsOiHE69CW5tnO10snXga4z9/heTiPMZ98RqbT/5FpMuSNkDhTaQFmfztmMXvY75bAHXjQPUeiePoc7DSsiJbnIRNiTFsIMDuPRvql6VjMRAnyQpt7YrfHcuiSWcw8dOXia8oodfHL2B++rtIlyVRTuFNpJkZY2DbWuzF70HOPpNXd+6LY9JZWF36R644CRvbGPZg2IpNPt9fvDYsrTOZgQAxpQURrE4iqdYTz8LJZ3HUpy/hLdqD/fojOMadHOmyJIopvIk0E1NagPnua8zq+VC4K7jQsqDPKBxjTsLK7hXZAiUsyo1hGzbbsandu8wieAfpxMxeTO83ns/WfEVJJIuUiKtKSGbh5DM56vNXcO5Yz5Avq9nQc2yky5IopfAmEkamqhyzcRlmzXzYthbqemBcMViDj8IadULwDlKJatXGsBubXRgK9+lliwW64KAbDuIsizS35reU75WldGTTcRfS9+MXSM3bxvTaWr7peAa4Na6fNI7Cm0gTGNuG3ZsxW1ZiclbC7s0Np8Tp0h9r0ESsvmOwYr2RK1SarGqfwFZEwzE9MrDoioMMLE1nJYdV2bELjrNm4vvvg3Qp2YNr0XssO+p0bKc+juXI6Whpw4yvBqrKg5OZV1dAbQ3GXxscANZXC/7avV9rwO8HYweDxwFfDTgc4HDu83XvP6cTk7sdAj5wuvYu2/+rExyu+u+tKP5wM5WlsDsHsyf4j50bgtt2X+mdsQaMxxo4HiupQ0TqlKartgOsL9jO4spidhgf++1lUrDIxiIbh+4clUaxOvdlxeSzGPLFK2TlbmXkgrksnXAKxuGMdGkSJRTeoowxJhi6amswG77BUbgHu7oMyoowlSV7w1pF8GvAF+lyD8o4XeB0B8NcjIfAxmVYsR6IiYMYD8R6IcYLsV6svV+J8WLZ4KqtBH8txnibJQQaY0N1JVQUQ3EepiQ3+LU4Dwp3QlnhgS+K9UK3QVg9hmL1GIyVmBb2uqT5lRibzdhsMjYbTYCc7Svqh/eok4ZFFhZZCmzSRCUZ3fhf/8mcuPZLMndtYuSCuSwbd5J64OSI6ChphYwx4KtuGMTqes9qqsDYOAF76fu4gMMOyu5wgjcBPPHBYOSKAXcsljsm+H9XLLhjgmHKYYHl2PvP2vvPEbz62jbBoS32/xcIYHK3Qm0lBOqW+Q/+tU7AH/wHUFUGJXmHbMO+y2OAIQDzwXY46wNeMPDF7e0BM8Ga2Vs/fN8WrODjtr23fru+rsCyj4Lbuao82ON4SBakZWFl9oDMHlhZPSCrJ5b+Yo4qJSZ4g8F2Y7MNm83GbnDtWp2OngSSbUNSbTUdsHArsEkY7UzJZOG4kxm3cC5ZOzcy+qu3WDrxNAIuXQMnh6fwFmHGXwsVpVBZAhUlwTBTVd4w7BzsdTEerNRs7LgknCkdIDEN4lOwvAngSQgGNm88uD3NfprSXv5psPbD1WvM90Eu4A/2Cgb8wQv5s3tBbVUwmNZWQW011FRh9l1W931tNRYE11W9N9Q2h9g4SOmIlZwBKR0hOSN4o0FG12BvoLR6fjtAbsBPnvGTawx52OTuDW1lB3l+3R2iPS0nPXHQL7s3Gb1H8tmKzyjx5bZ0+dJO5GZ0Y/FRP2L0/LfomLuVsV++xuJJZ+B3x0a6NGnFFN5akPHVQHkxFO0m8N0C2L72wOul6lngidsbxOKDXz0J9acQbW8iMaOPp7aqCre39YcJy7KCp0n3PyUQn4xjyOQjWkdlZSVrVq9mYJ+eeJ3WfsGuGrNxGVSWfn/DgDGA2e+rtd91ew7wJuIYMB7iEvf2UiZg6S/fqOAzhnwMudjk7RPQ8vI2UbhnPeYQfboWwZsMulgOuuCgh+WkBw48+/6h49KHp7SMgsxuLDz6bMbOe4O0gl2M//wVFk0+k9rYuEiXJq2UwlszMcYEg1lZ4ff/DtVLFBsHcUkQnxz86k2A2Hgsh+a2PIBlBa9/i4uDfWaRsgC7uuIHewAPKj4Zq/ugsJUo4VWLIc/YBwa0vXd9HjSe2cFT37FYdMQiA4uOloOOWHS2HHTCQYxOgUorUpyezddT/h/jvnyN5OI8Jnz2XxZOPotqTZcnB6HwFibGmGCvT0ne92HNX3vgEz0JkJ6N1XskpqwQnC4sV0zLFyzSitjGUAWUYyjH4CvLZ/G3H7KnOp8Shw32oW++8QAdcZBhWXQkGNA6pmSQ1W8sCau/xqosbbF2iDRFWUpHvp56LuO+eJWEsiImfvYyi476EeXJumtdGlJ4awJTUxUMa3X/9g9rlgMSUoLXoyWmQUJa8EaBDp1x9BtzRNeKibQltjGUA6UYKvYGtXIMe281+V5NefDfXh4gY7+AlmE56IiDRDjwus6YOBwxXmz1rkmUqUhMZf4x5zLui9dIKC9m4qcv882EU8jP7B7p0qQVUXhrJFNagFn0Hmb94mBP274cTkhKh6S6GwiSdReitFt+Y1NkbEowlGIoBcowBwy/UccBJADxWKR7kxjbdRDenLV0qS4jxeWO6vEBRRqjOi6J+cf+mFHz3yY9fwdj5r3BqpHT2NZzSKRLk1ZC4a2RzJevYr77+vsFCamQ3DH4LyFV16lJu2RMsPesCMPGoh28XbidXZUlB70ezQUkYZGAVR/WErDw8n0PWnJ8KhMye1K8bTNeLAU3aXd8MR4WTT6TIUs/osvW7xi69CPiyotZO2RSpEuTVkDhrZGssSdBcjqmqhxi4nRXorRLZu/pz3xsCvbeOFB/0UBFcf3zYgkGtSQskvd+jeMgpzlF5AC208W3Y06gMiGFfqu/pve6JcSXF5N39LmRLk0iTOGtkayOXbE6dtX1atLuVBtDHqY+sNXs97gDSMaiW0Iak7oNZtfuTdQW7YlEqSJth2WxYeB4KuOTGbrkQ7J2biRl7t8wZ14X6cokghTeROSgjAlep5aLzR4M+58EdRCcLip9778kLJyWReeULIand6E4dysHud9aREKws9sAKuOTGbngXbylBdj/upsOY0+kJLtnpEuTCFB4E5F6PjvAd8W7WVhZzHb8VO/3ePLecdM6YJGyN6yJSMsoTs9m3nE/YfziD0ncvZn+X71JTO8RfDdssia1b2cU3kTaOb8dYE3xbhblbWF5wXaq6+adBZxAB6zgMB1YDWcgEJEWVxsbx6bjLmTE+qWYhe/Qc+MyUop2s3zsiVQmpES6PGkhCm8i7VDA2Kwt3sPivC18U7Cdyn3GKEyJ8dIBB6m11aSrd02k9XE4cEw+m5WxHvp8/RaphbuZ/OGLrBl2dHA4Ef3MtnkKbyLthDGGjaX5LMjdzNL8bZT7v7/lIMntYUzHbozp2J2eiR34YuXnmoxdpJUr7NKXL467gOGL/0d6/g6GfvMxmbs28e3o46n1xEe6PGlGCm8ibVx+dTkLcjfz9Z7N5O4zv26iO5aR6V0Z07E7fZM74rA0RqFItKmOT2LBlP9Hz/Xf0G/VV2TszuHoD15g1ahj2d25b6TLk2ai8CbSBlX7fSwt2Mb8PZtYV/J9D1qsw8WoDl0Zl9GD/imZOBXYRKKfZbG53yjyM7sxfNH7JJXkM+rrd9mT3ZNVw4+hOj4p0hVKmCm8ibQRtrFZW5zL17mbWJq/jVo7AIAF9E/JZGJGL0Z06ILHqYGlRdqisuQOfHXsj+mzZiG91i0hc9dm0nO3sWHgeDb3Hak7UtsQhTeRKLe7spT5uZtYsCeHotrK+uWZ3kQmZPRiQkYP0nT9i0i7YDtdrBtyFDu79WfwN5+Qnr+DASvn0Xnrd6wccSxFHTtHukQJA4U3kShU4athcd5W5uduYnNZQf3yOJebMR26MzGzFz0T0zUNlUg7VZ6UzoIp/4/OW9cw4NsvSSwtYOLn/2V3di/WDTkK4lIiXaI0gcKbSJQIGJvlBdv5es9mvi3cgd/YADiwGJyWzcSMXgxL74xbp0ZEBMCy2NF9ELnZvei38iu65qwka9cmMndtprDPCMyxF0S6QgmRwptIK2aMYXtlMV/V5vLit5sp32c8ti7xKUzI6Mm4jB4kx3gjWKWItGa+GA+rRk0jp88I+q/6iqydG0nf8A325pV07zuK73oP1dAiUUbhTaQVKqmtYkFuDvP3bGJnZUn98kS3h3EZ3ZmY0YuuCakRrFBEok1FUhpLJ55GSsEuhq3+moTcrXT5bgHZ6xazvccgNvUdTVVCcqTLlCOg8CbSSpT7qlmav43FeVtZV7Knfhp4p+WguyOeaT0HMyqzB06HhvcQkdAVp2ez8YSfMbK8mOIv/ktSwU66b1pBt00r2dW1Lxv7jaEspWOky5TDUHgTiaBKfy3LCrazOG8La4p2Y9dHNuiV2IGJmT0ZFN+RLes3MjA5S8FNRMLDsrB6DWeFx4tz5wZ6rV1Mxp4tdNq2jk7b1lGUlsXWXkPZ1aUftlNRobXRHhFpYWW11aws2sk3+dtYVbSr/sYDgG4JqYzp0J3RHbvRwZMAQGVl5aFWJSLSNJZFYccuFHbsQmJxHr3XLiZrxwZSC3eTWribQcs/Z3u3gWzrNYTypPRIVyt7KbyJNDNjDHuqSllesIPlhTvYVJqP2aeHrVNcMmM6dmdMx25kejUSuohERllKR5aNP5mY6gq65qym6+aVxFWW0nPjMnpuXEZpcgd2denHzi79NNRIhCm8iTQDnx1gU2k+3xbu4NuC7Q3mFAXoGp/KsPTOjO7Qjc7xKZEpUkTkIGo98WwcMJaN/cfQYc8Wum1eScauzSSV5JNUkk//VV9R0aEz9uBJeBJSKIn1gMaUbFEKbyJhYBvD9ooi1hTv5rui3awvzcO3d3oqAJfloH9KJsPTujA0vRNpsbotX0RaOcsiP6sH+Vk9cNdWk7ljA522rSM9bzvx+Tswn73EaKAiPpncrJ7kZfWgsGNnXSPXArSFRULgswNsKStkQ2keG/f+q9hnDDaAJLeHQanZDE/vzKCUbDwuzSkqItHJF+Nhe88hbO85hJjqCgbmbqfznq3Y278jvqKk/tRqwOGkKD2bog6dKezQmaK0LGz97gs7hTeRH2Abm92VpWwpL2RreSFbygvZUlbY4EYDgFini37JGQxMyWJAShad4pI1PZWItDm1nnjyB4yn29Hn8vXWlbi3r6fj7s1k7M7BU11Bh7ztdMjbDoBtOShJzaAkNZOS1AwKEtLI3+93pzSewpvIXrYxFNZUsLuylF2VJeza+3V7RRG1+5wCrZPo9tAnqSO9kzrQJ6kj3RLSNJSHiLQrAXcshZ17s6dzbzCG+LIi0vJ31P/zVpXX37lax+dwUpbcAZPeCbtLf1IwVMclUOOJ17VzRyji4c22bR577DFefvllSktLGT16NHfccQfdu3ePdGnSBvmMoQhDIYZCY1OAIb+qiF1L57KnqvSgIQ2CvWpd41PpnpBGt8Q0eiV2oKMnQT1rIiJ1LIuKpDQqktLY1msoGIO3spTUgl0kF+WSVJxLclEu7oCPtKI9ULQHs+EbBgODAZ8rhqr4JCrjkqiKT6IqLonK+KS9y5IJuGMi3cJWI+Lhbfbs2fz73//m3nvvJTMzk/vvv5/LLruMt99+m5gY7Sj5YbYxVPlrKQn4KDMByjCUGRP8iqHc7A1rGEr3GaKjns8HviogOJtBpjeR7LhksuKSyPYm0SUhlUxvIg5LvWoiIkfMsqiKT6YqPpmd3QYA4Kutwbc1hx7OAF191WRWVVCZuwVPWSFufy3uvXe0HozPFUOtJ46a2Dhq9n6NTUzD7tiNtJpyHA4HfncsPncsvphYAk53m+3Ji2h4q62t5e9//zs33XQTU6dOBeChhx7i6KOP5oMPPuDUU0+NZHnSCMYYbCAA+31tuLzu//XL/TX4i3bhswPU2oHg14C/4fe2H18gQHlNNXnVRXzwXR41JkBVwEeV30dNwHewSHZIMUAaFmk4SLMsOsR4ye49kmxvEh28CTgV0kREmofloDguiR0dO+BO7kinjt34Ztd6yqrK8FaUEldRgreylLiK0gZfY2qrg+GuvJb48uIGqzTAwIO8lW058Llj8btj8Ltj8bvcBJwuAi43AZcbvzP41RGXhEnJxvImtMQWCAvLGNOYz72w+vbbbzn33HN577336NmzZ/3yn/zkJ/Tv358777yzUetbunQpxhjc7ua7s6XG9lPl9wUHWQ11y9X9IdCI1x/0qUf6B0UY9rA5yP9aGwsLB8E/tBx7/+/A2vs9OLHqv2+w6SwLXDFE8EfhBwUCAZxOZ4u8l2VZ1PhrMXZoFxU7nU7cTje1/lrsENdhORzEumIwfh/YduP/eHY4wOkGfy2Eul8dTnC6mtSOULaFMab+dHz9dmjisdnUfVpXB4Hg/mi0I9iW+7b7YOq2ZU3AH/L2cDocuB3OsBzfodZRX0PAT8AOYFkW1hH/Mj9wHU3eFmFoR2NebzD1+9rlcDZiHQbL2FjGgNn7f9vgwOAAbDuwz2OGRn9WeeIxMd7GveYIGWPw+/1YlsWoUaPCss6I9rzt3h28gDE7O7vB8oyMDHbt2tXo9dX/wmvGblKP043Hqdue26LWfP2ay9WyP6qxrqZfshAThnVYTR1ioJW0o6nrCMexGY59itMNTfgbIhzbMjYMY4iFY1s0tY5YpwvCsY4mCks7IlwDBP9Ab6rm+gSwLKv+X7hENLxVVQWvM9r/2rbY2FhKSkoavb6RI0eGpS4RERGR1iqiF/d4PB4geO3bvmpqavB6m6f7UkRERCSaRTS81Z0uzc3NbbA8NzeXrKysSJQkIiIi0qpFNLwNGDCAhIQEFixYUL+stLSU1atXM2bMmAhWJiIiItI6RfSat5iYGC688EIeeOAB0tLS6Ny5M/fffz9ZWVlMnz49kqWJiIiItEoRH6T32muvxe/387vf/Y7q6mrGjh3L008/rQF6RURERA4iouO8iYiIiEjjaCh5ERERkSii8CYiIiISRRTeRERERKKIwpuIiIhIFFF4ExEREYkiCm8iIiIiUUThrZWybZtHHnmEo48+muHDh/Pzn/+cLVu2HPL569ev55e//CXjx49n4sSJXHvttezcubMFK266xrZ55cqVXHzxxYwcOZIJEyZw++23U1pa2oIVh0dj272vt956i/79+7N9+/ZmrjL8Gtvu1157jf79+x/w70i3VWvQ2Db7fD4efPBBjj76aEaMGMGFF17ImjVrWrDi8GhMux999NGD7uf+/ftz2223tXDloWvsvs7Ly+P6669n/PjxjB8/nuuuu47du3e3YMXh0dh2b9u2jSuuuIJx48YxadIk/vjHP1JVVdWCFYfX7NmzmTFjxmGfU1RUxA033MDYsWMZO3Ys//d//0dlZWXj3shIq/Too4+aiRMnmk8//dSsWbPG/PznPzfTp083NTU1Bzy3sLDQTJo0ycycOdOsW7fOrFixwlx44YXm5JNPNtXV1RGoPjSNafOePXvMmDFjzO9+9zuzefNms2TJEnPqqaeaK664IgKVN01j2r2v7du3m9GjR5t+/fqZbdu2tVC14dPYdt97773mwgsvNLm5uQ3++f3+Fq48dI1t829+8xszYcIE88knn5gNGzaYq6++2kyaNMmUlpa2cOVN05h2l5eXH7CPZ8+ebYYNG2bWrFkTgepD09h9fcEFF5jzzz/frFq1yqxatcqcd9555qyzzmrhqpuuMe0uLS01kyZNMueff75Zvny5+fbbb82Pf/xjc8kll0Sg8qZ75plnTP/+/c2FF1542OddeOGF5txzzzUrV640X331lTn22GPNzTff3Kj3UnhrhWpqaszIkSPNiy++WL+spKTEDBs2zLz99tsHPP+ll14yo0aNahDUdu3aZfr162e++uqrFqm5qRrb5qVLl5pf//rXxufz1S979tlnzfDhw1ui3LBpbLvrBAIB85Of/MRcdNFFURneQmn3JZdcYv74xz+2VIlh19g2b9261fTr18988sknDZ5/7LHHRs3PtTGhH+N1tmzZYoYPH97g9a1dY9tcUlJi+vXrZz766KP6ZR9++KHp16+fKSwsbJGaw6Gx7X7uuefM8OHDTUFBQf2ynTt3mv79+5tFixa1SM3hsHv3bnPppZeaESNGmJNOOumw4W3p0qWmX79+ZsOGDfXLvvjiC9O/f3+ze/fuI35PnTZthb777jsqKiqYMGFC/bKkpCQGDRrEokWLDnj+xIkTefzxx4mNjT3gsZKSkmatNVwa2+aRI0cya9YsXK7gDG8bNmzgtddeY9KkSS1Wczg0tt11nnzySXw+H5dffnlLlBl2obR77dq19OnTp6VKDLvGtvnLL78kKSmJKVOmNHj+xx9/zMSJE1uk5nAI9Riv86c//Ym+ffvy4x//uDnLDKvGtjk2Npa4uDhef/11ysvLKS8v54033qBHjx4kJye3ZOlN0th2b968mV69epGWlla/LDs7m9TUVBYuXNgiNYfDqlWrSE5O5s0332T48OGHfe7ixYvp2LEjvXv3rl82btw4LMtiyZIlR/yeEZ/bVA5Ud51DdnZ2g+UZGRns2rXrgOd36dKFLl26NFj217/+ldjYWMaOHdt8hYZRY9u8rxNPPJGcnBw6d+7M7Nmzm63G5hBKu7/99lv+/ve/89///pc9e/Y0e43NobHtLiwsJD8/n0WLFvGPf/yD4uJihg8fzo033kjPnj1bpOamamybc3Jy6Nq1K//73/946qmn2LNnD4MGDeLWW29t8Iu/tWvKz/aKFSv46KOPeO6553A4oqevobFtjo2N5e677+b3v/89Y8aMwbIsOnbsyD//+c823e6OHTuSl5dHIBDA6XQCUF5eTklJCQUFBc1fcJhMmzaNadOmHdFz9+zZc8D2iYmJISUl5Qd/HvYVPUdFO1J3sWZMTEyD5bGxsdTU1Pzg659//nlefPFFrr/+etLT05ulxnBrSpsfeOAB/vnPf9KxY0cuuugiKioqmq3OcGtsuysrK7nxxhu58cYb6dGjR0uU2Cwa2+5169YB4HQ6ue+++3jooYeorKzkpz/9Kfn5+c1fcBg0ts3l5eVs3bqV2bNnc/311/PEE0/gcrn46U9/GlUfbE352X722WcZPnx4g56caNDYNhtjWLt2LSNHjuSFF17gueeeo3Pnzlx99dWUl5e3SM3h0Nh2n3rqqZSUlHDPPfdQUVFBaWkpd9xxB5ZlUVtb2yI1t7SqqqoDtg8c+ed7HYW3Vsjj8QAccPDW1NTg9XoP+TpjDA8//DB33303l19+OT/72c+as8ywCrXNAEOHDmXs2LE8+uij7Nixgw8++KDZ6gy3xrb7j3/8Iz169OD8889vkfqaS2PbPWHCBBYuXMh9993H4MGDGTt2LI8//ji2bfPqq6+2SM1N1dg2u91uysrKeOihh5g8eTLDhg3joYceAoJ33kaLUH+2Kysr+eCDD6LqdGmdxrb5nXfe4cUXX+T+++9n9OjRjBs3jieffJIdO3bwyiuvtEjN4dDYdnfv3p1HH32UDz/8kNGjRzNlyhQ6derEkCFDSEhIaJGaW5rH4zloMK2pqSEuLu6I16Pw1grVdanm5uY2WJ6bm0tWVtZBX+Pz+bjpppt48sknufnmm7n++uubvc5wamybN27cyGeffdZgWUZGBsnJyVF1KrGx7X7llVeYP38+I0eOZOTIkVx22WUAnHbaadx+++3NX3CYhHKM73/tT1xcHF26dIma/d3YNmdlZeFyuRqcIvV4PHTt2jWqhoYJZV8DfPHFF9i2zfTp05u1vubQ2DYvWbKEnj17NggsycnJ9OzZk5ycnGatNZxC2ddTp07ls88+44svvuDrr7/mhhtuYNu2bVF9ZuFwsrKyDtg+tbW1FBcXk5mZecTrUXhrhQYMGEBCQgILFiyoX1ZaWsrq1asZM2bMQV9z880389577/Hggw9y6aWXtlSpYdPYNn/xxRdcd911DU4pbN26laKioqi6Hqix7f7f//7H22+/zeuvv87rr7/OH//4RwCeeuoprrvuuharu6ka2+4XX3yR8ePHU11dXb+svLycnJycqLmJobFtHjNmDH6/nxUrVtQvq66uZtu2bXTv3r1Fag6HUH6fQTDQDB48mKSkpJYoM6wa2+bs7Gy2bNnS4LRZVVUV27dvb9P7esmSJVx44YXU1tbSsWNHPB4PCxcupKioiKOOOqolS28xY8eOZffu3Q3GvqvbXqNGjTryFTXh7lhpRrNmzTLjxo0zH374Yf1YOSeccIKpqakxfr/f5ObmmqqqKmOMMa+88orp16+fmTNnzgHjI9U9Jxo0ps2FhYVm8uTJ5oorrjDr1q0zixYtMmeccYY555xzomrcL2Ma1+79ff3111E5VIgxjWv3zp07zdixY80111xj1q1bZ7799lvzs5/9zBx//PFt9hg3xpif/exn5uSTTzaLFi0y69evN9dcc42ZOHFig6EVokEox/iMGTPM7373uwhV3HSNafOePXvMuHHjzBVXXGHWrFlj1qxZYy6//HIzefJkU1JSEuGWNE5j2l1UVGTGjRtn7rjjDrN161Yzb948M2nSJHPnnXdGuBWhu+WWWxoMFbJ/m23bNueff74566yzzPLly838+fPNsccea2699dZGvY/CWyvl9/vNn//8ZzNhwgQzYsQIc9lll9V/QG/bts3069fPvPLKK8aY4PhX/fr1O+i/uudEg8a02RhjNm3aZH75y1+a0aNHm3Hjxpnbbrst6n7RGdP4du8rmsNbY9u9evVq8/Of/9yMHj3ajBo1ylxzzTVm586dkSo/JI1tc1lZmbnjjjvM+PHjzfDhw80ll1xi1q9fH6nyQxbKMX7yySebBx54IBLlhkVj27xhwwZz+eWXm3HjxpkJEyaYX/3qV+3i53rZsmXmxz/+sRk+fLg55phjzKOPPhp1f4Dva//wdrA25+fnm2uuucaMGDHCjB8/3txxxx2NHlDfMsaYcHcLioiIiEjz0DVvIiIiIlFE4U1EREQkiii8iYiIiEQRhTcRERGRKKLwJiIiIhJFFN5EREREoojCm4iIiEgUUXgTEWklNOymiBwJhTeRNmbFihXcdNNNHHPMMQwbNozjjjuO3/3ud2zbti3s77VgwQL69+/fYC7D5uT3+3n22Wc566yzGDFiBCNHjuSss85izpw51NbWhr2uRx99lP79+9d/P2PGDGbMmNGkdQJs376d/v378+qrrwLB+R9vueUWFi9e3OR179ixg9/+9rdMnTqVIUOGMGHCBC6//HK+/vrrJq9bRFoHzbAg0oa88MIL3HPPPYwfP56zzjqLjIwMtm7dypw5cygqKuKZZ55h8ODBYXu/8vJyNmzYQJ8+fUhISAjbeg/ltttu4/333+eyyy5j6NChGGNYvHgxzzzzDJMnT2b27NlhrWv37t3s3r2bESNGANQHt3/84x9NakdtbS2rV6+mW7dupKWlsWDBAi666CKef/55xo8fH/J68/Ly6vf7RRddRKdOnSgsLOTll19m3rx5PPLII5xwwglNql1EIk/hTaSNWLJkCTNmzOCCCy7gt7/9bYPHCgsLOfvss0lKSuLNN9+MUIVNs3PnTqZNm8Zdd93Fj3/84waPPffcc9xzzz28/PLLDBs2rNlqCFd421+4wtvjjz/OX//6V+bNm0diYmL98kAgwLnnnktNTQ3vvPNOOEoWkQjSaVORNuLpp58mMTGR66+//oDH0tLSuPXWWznhhBMoLy+vX/7uu+9y9tlnM3LkSCZNmsTtt99OSUlJ/eM1NTXcddddTJkyhSFDhnDSSSfx97//vf7x/U9PPvroo0yfPp1PP/2U008/nSFDhnDiiSfy2muvNainuLiY22+/naOOOoqhQ4dy3nnnMX/+/MO2Lz8/H2PMQa8LO/3007n++utJSko6ZF0nnXQSH374IaeddhpDhw7ljDPO4JtvvmHZsmWce+65DBs2jNNOO61BHfufNt1fYWEhd911F8ceeyxDhgxh3LhxXH311Wzfvr3+OTNmzODGG2/k2muvZdSoUfzyl79scNq0LrgBXHTRRcyYMYMXXniB/v37s3nz5gbv98477zBgwIAG699/G1mWhW3bDZY7nU5uuOEGzjvvvAbL582bxwUXXMDIkSOZPHnyAfs/JyeHa6+9lkmTJjFixAhmzJjBkiVL6h+va8czzzzDySefzLhx4+pPBa9bt47LL7+cUaNGMWrUKK6++upmOXUv0h4pvIm0AcYYvvzySyZOnIjX6z3oc0466SR+9atf1Z9GnD17Nr/+9a8ZPnw4jzzyCFdffTXvv/8+M2bMoLq6GoC7776bzz77jFtuuYWnn36a4447jvvuu6/+A/pg8vLy+P3vf89FF13EU089RZcuXbj11lvZuHEjEAyEF198MR999BG//vWveeyxx8jKyuIXv/jFYQPcgAEDyM7O5t577+Wuu+7i888/rw+iaWlpXH755fTo0eOQr9+9ezf33nsvV1xxBQ8//DAlJSVce+21XH/99Zx33nnMmjUL27b59a9/Xd/+wzHGcPnllzNv3jxuuOEGnn76aa666iq++uorbr/99gbPnTt3Lm63m8cff7w+qNUZPHhw/fNvv/127rjjDk4//XRiY2N54403Gjz3tddeY9y4cXTp0uWgNR1zzDFUV1dz3nnn8fTTT7N69WoCgQAAkyZN4uKLL65/7meffcYvfvELUlJSeOihh7jpppv4+OOPufbaawHYsGEDZ599Ntu2beN3v/sdDzzwAJZlcfHFF7Nw4cIG7/vQQw9x6aWX8sc//pEJEyawefNmzj//fAoKCvjTn/7E3XffzbZt2/jJT35CQUHBD25bETk8V6QLEJGmKyoqoqam5pAf6vsrKSnhiSee4Nxzz+WOO+6oX96vXz8uuOACXn31VX7605+ycOFCjjrqKE499VQAxo8fT1xcHKmpqYdcd1VVFXfffTcTJ04EoEePHhx77LF89tln9O7dmzfeeIPvvvuOl156ieHDhwMwZcoUZsyYwQMPPMArr7xy0PXGxMTw1FNPcfPNN/Piiy/y4osv4nA4GDx4MCeddBIXXnghHo/nsHXdcccdTJkyBYCNGzfy4IMPcvfdd3POOecAwdOL1157LZs3b2bgwIGH3Ya5ubl4vV5uueUWxowZU799tm/fzr///e8Gz3U4HPzhD38gLi4OoEHPWUJCAn369AGgT58+9f+fPn06b775Jtdddx2WZZGbm8tXX33FPffcc8iapk6dyu23386sWbP485//XL/+iRMncv755zN58uT65z7yyCMMGDCAxx9/vH6Zx+Nh1qxZ7Nmzh8ceewy3283zzz9ffwr2mGOO4bTTTuP+++/n5Zdfrn/dCSecUL8NAW644QY8Hg/PPvts/R8LEydO5Pjjj2fOnDnccssth922InJ46nkTaQMcjuCPcl0vyw9ZtmwZtbW1nH766Q2Wjxkzhs6dO9efbhw/fjwvv/wyl112GS+++CI7duzg6quv5thjjz3s+usu8AfIysoCoLKyEoD58+fTsWNHBg8ejN/vx+/3EwgEOPbYY1m5cmWD03b769evH6+//jr//e9/mTlzJuPHj2f9+vXcf//9nHnmmRQWFh62rlGjRtX/v0OHDgfUmpKSAgTv/vwhmZmZPP/884wZM4adO3cyf/58/vnPf7J06VJ8Pl+D53bp0qU+uB2pc845hx07dtTfgfrGG2/g8Xg48cQTD/u6Cy64gC+//JLHHnuMCy64gOzsbD744AMuvfRS/vSnPwFQXV3NqlWrOP744xu89sQTT+T9998nMzOThQsXcuyxxza4ds7lcnHqqaeyYsUKKioq6pf369evwXq+/vprxo8fj8fjqd/HCQkJjBkzhq+++qpR20FEDqSeN5E2ICUlhfj4eHbu3HnI51RWVlJbW0tKSkp9QKoLMPvq0KEDZWVlAPz2t78lKyuLN998k7vuuguAkSNHcvvttzNo0KBDvte+p27rgmXdtWrFxcXk5eUd8q7XvLw8kpOTD9dchg4dytChQ7nyyiupqqrimWee4S9/+Qt/+9vfDturc7A7Tw/XW/dD3nzzTWbNmsWuXbtISUlhwIABB13fwbbzD5kwYQJdunTh9ddfZ+zYsbz++uucfPLJhzwtvi+v18v06dOZPn06AFu2bOG3v/0tzzzzDGeffTbJyckYY0hPTz/kOkpKSg55fBhjGlw7uf/ziouLeffdd3n33XcPeH1aWtoP1i8ih6fwJtJGTJ48mQULFlBTU0NsbOwBj7/66qvcfffdvPjii/XhKD8/n969ezd4Xl5eHl27dgWCpyqvvPJKrrzySnbu3Mknn3zC7NmzueGGG5g7d25IdSYmJtKjRw8eeOCBgz5+qFO/9913H5988gnvvfdeg+Ver5errrqK999/nw0bNoRUUygWL17MLbfcwoUXXsill15a38P45z//ucFF/aGyLIuzzjqL559/ngsuuIANGzbw+9///pDPDwQCTJ8+nTPPPLP+urU63bt357e//S1nnnkmGzZsYOrUqfz/9u4nJMotjOP4d5iapDAdFEldWKDilJokKrhIJhcVtbCxhVEJCo1Oln8QXYi0cOMiTCuFrMlE3gkiosAEob+YKFHiNihRKUqcMkkECdIWF1/wqqO3ey858ftsz3nPPO/7LuaZc85zxmKxLJup/P79O4ODg6SmphIWFsbnz5+XfY7f7wfAbrczOTm5YiyhoaFkZ2dTVFS0rG3TJn3tiPxbWjYV+UMUFxczPT1Nc3PzsrYvX77g9XqJi4sjLS2NvXv3YrPZ6O7uXtLv9evXfPz4kX379jE3N8fBgwfN6tKYmBhOnjzJkSNHmJiY+OU4MzMz+fTpExEREeYMWkpKCoODg3i9XqxW64rX7dq1i9HR0RVnc2ZnZ5mcnFy2fPd/Gh4eZn5+nvLycjNx+/Hjh7ks+PeKz0BWu+f8/HxmZmZobGxk586dpKenBxwjKiqKe/fu8fXr12Xti5WriYmJbNu2DYfDwZMnT5b06e/vx+12MzExQUZGBs+ePTNnYRfvr6enh5SUFGw226qxZGZm8u7dOxwOh/l+k5OT6ezs5NGjRwGfhYisTT+BRP4QaWlpVFRU0NLSwsjICMeOHcNut/P27Vs6OjqYnZ3l+vXrWCwWwsPDcbvd5qb03NxcPnz4wOXLl4mPj8flchESEsKePXvMPotHV9y/f3/NfVeBuFwuDMOgqKiI0tJSoqOjGRgY4MaNG5w6dYrNmzeveF1eXh7d3d3U1tby8uVLcnJy2L59O2NjY3R1dRESEkJxcfEvx/VPLZ4n19DQQH5+Pt++fcMwDN68eQP8tUy93gOCF/eVPX/+nLCwMJKSkgCIjo4mOzub/v5+qqqq1hynvr6e06dP43K5KCwsxOFwMD8/z6tXr+js7KSgoMAsiCgvL8fj8VBZWYnL5WJqaoqmpiacTicOh4Nz587R19dHYWEhbrcbm82GYRi8f/8er9cbMI6zZ89SUFBASUkJJ06cYMuWLdy5c4fHjx9z5cqVdT0TEVmdkjeRP4jH42H37t34fD4aGxuZnp5mx44d7N+/n9LSUmJiYsy+58+fJzIyEsMwuHv3LuHh4Rw6dIjKykpzX1VDQwMtLS10dHTg9/uJiIjg+PHjVFRU/HKMW7duxefz0dTUxMWLF5mZmSE2Npbq6uqAyZfNZuPmzZt0dXXR29tLT08Pc3NzREVFceDAATweT8A9XP+1rKwsLly4wK1bt+jt7SUyMpKsrCxaW1spKytjaGiInJycdY2VkJDA0aNH8fl8vHjxgocPH5ptTqeTgYEB8vLy1hwnOTmZBw8e0N7ejmEY+P1+rFYr8fHx1NXVLakIdTqdtLe3c/XqVcrKyrDb7Rw+fNh8twkJCdy+fZtLly5RV1eHxWIhNTXVLNIIJCkpCZ/PR3NzM7W1tSwsLJCYmEhbWxu5ubnreiYisjr9w4KIyAZ25swZrFYr165d+92hiMgGoZk3EZENqK2tjdHRUfr6+jAM43eHIyIbiJI3EZEN6OnTp4yPj1NTU0NGRsbvDkdENhAtm4qIiIgEER0VIiIiIhJElLyJiIiIBBElbyIiIiJBRMmbiIiISBBR8iYiIiISRJS8iYiIiAQRJW8iIiIiQUTJm4iIiEgQ+QkF2Kp2uNQXXwAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import json\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "import pandas as pd\n", "\n", "# Load result\n", "with open(\"count_validation_results.json\", \"r\") as f:\n", "    result = json.load(f)\n", "\n", "same_scores = result[\"same_sample_scores\"]\n", "diff_scores = result[\"different_sample_scores\"]\n", "\n", "# Convert to DataFrame\n", "plot_data = pd.DataFrame(\n", "    {\"score\": same_scores + diff_scores,\n", "     \"type\": [\"Same Sample\"] * len(same_scores) + [\"Different Sample\"] * len(diff_scores)}\n", ")\n", "\n", "# Plot\n", "sns.set(style=\"whitegrid\")\n", "sns.histplot(data=plot_data, x=\"score\", hue=\"type\", bins=30, kde=True, palette=\"Set2\")\n", "\n", "plt.xlabel(\"Cosine Similarity Score\")\n", "plt.ylabel(\"Frequency\")\n", "plt.legend(title=\"Comparison Type\")\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 12, "id": "50a653fe", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_7231/3617512840.py:25: User<PERSON><PERSON>ning: No artists with labels found to put in legend.  Note that artists whose label start with an underscore are ignored when legend() is called with no argument.\n", "  plt.legend(title=\"Comparison Type\")\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import json\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "import pandas as pd\n", "\n", "# Load result\n", "with open(\"tfidf_validation_results.json\", \"r\") as f:\n", "    result = json.load(f)\n", "\n", "same_scores = result[\"same_sample_scores\"]\n", "diff_scores = result[\"different_sample_scores\"]\n", "\n", "# Convert to DataFrame\n", "plot_data = pd.DataFrame(\n", "    {\"score\": same_scores + diff_scores,\n", "     \"type\": [\"Same Sample\"] * len(same_scores) + [\"Different Sample\"] * len(diff_scores)}\n", ")\n", "\n", "# Plot\n", "sns.set(style=\"whitegrid\")\n", "sns.histplot(data=plot_data, x=\"score\", hue=\"type\", bins=30, kde=True, palette=\"Set2\")\n", "\n", "plt.xlabel(\"Cosine Similarity Score\")\n", "plt.ylabel(\"Frequency\")\n", "plt.legend(title=\"Comparison Type\")\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 24, "id": "b9ca9406", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_9081/632856345.py:25: User<PERSON>arning: No artists with labels found to put in legend.  Note that artists whose label start with an underscore are ignored when legend() is called with no argument.\n", "  plt.legend(title=\"Comparison Type\")\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import json\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "import pandas as pd\n", "\n", "# Load result\n", "with open(\"dict_validation_results.json\", \"r\") as f:\n", "    result = json.load(f)\n", "\n", "same_scores = result[\"same_sample_scores\"]\n", "diff_scores = result[\"different_sample_scores\"]\n", "\n", "# Convert to DataFrame\n", "plot_data = pd.DataFrame(\n", "    {\"score\": same_scores + diff_scores,\n", "     \"type\": [\"Same Sample\"] * len(same_scores) + [\"Different Sample\"] * len(diff_scores)}\n", ")\n", "\n", "# Plot\n", "sns.set(style=\"whitegrid\")\n", "sns.histplot(data=plot_data, x=\"score\", hue=\"type\", bins=30, kde=True, palette=\"Set2\")\n", "\n", "plt.xlabel(\"Cosine Similarity Score\")\n", "plt.ylabel(\"Frequency\")\n", "plt.legend(title=\"Comparison Type\")\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 19, "id": "c4c2853f", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_7231/144027704.py:25: User<PERSON><PERSON>ning: No artists with labels found to put in legend.  Note that artists whose label start with an underscore are ignored when legend() is called with no argument.\n", "  plt.legend(title=\"Comparison Type\")\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import json\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "import pandas as pd\n", "\n", "# Load result\n", "with open(\"sbert_validation_results.json\", \"r\") as f:\n", "    result = json.load(f)\n", "\n", "same_scores = result[\"same_sample_scores\"]\n", "diff_scores = result[\"different_sample_scores\"]\n", "\n", "# Convert to DataFrame\n", "plot_data = pd.DataFrame(\n", "    {\"score\": same_scores + diff_scores,\n", "     \"type\": [\"Same Sample\"] * len(same_scores) + [\"Different Sample\"] * len(diff_scores)}\n", ")\n", "\n", "# Plot\n", "sns.set(style=\"whitegrid\")\n", "sns.histplot(data=plot_data, x=\"score\", hue=\"type\", bins=30, kde=True, palette=\"Set2\")\n", "\n", "plt.xlabel(\"Cosine Similarity Score\")\n", "plt.ylabel(\"Frequency\")\n", "plt.legend(title=\"Comparison Type\")\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 20, "id": "938c589a", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_7231/2201943031.py:25: User<PERSON><PERSON>ning: No artists with labels found to put in legend.  Note that artists whose label start with an underscore are ignored when legend() is called with no argument.\n", "  plt.legend(title=\"Comparison Type\")\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import json\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "import pandas as pd\n", "\n", "# Load result\n", "with open(\"all-distilroberta-v1_validation_results.json\", \"r\") as f:\n", "    result = json.load(f)\n", "\n", "same_scores = result[\"same_sample_scores\"]\n", "diff_scores = result[\"different_sample_scores\"]\n", "\n", "# Convert to DataFrame\n", "plot_data = pd.DataFrame(\n", "    {\"score\": same_scores + diff_scores,\n", "     \"type\": [\"Same Sample\"] * len(same_scores) + [\"Different Sample\"] * len(diff_scores)}\n", ")\n", "\n", "# Plot\n", "sns.set(style=\"whitegrid\")\n", "sns.histplot(data=plot_data, x=\"score\", hue=\"type\", bins=30, kde=True, palette=\"Set2\")\n", "\n", "plt.xlabel(\"Cosine Similarity Score\")\n", "plt.ylabel(\"Frequency\")\n", "plt.legend(title=\"Comparison Type\")\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 21, "id": "c5fcca98", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_7231/3457208723.py:25: UserWarning: No artists with labels found to put in legend.  Note that artists whose label start with an underscore are ignored when legend() is called with no argument.\n", "  plt.legend(title=\"Comparison Type\")\n"]}, {"data": {"image/png": "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****************************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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import json\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "import pandas as pd\n", "\n", "# Load result\n", "with open(\"all-MiniLM-L6-v2_validation_results.json\", \"r\") as f:\n", "    result = json.load(f)\n", "\n", "same_scores = result[\"same_sample_scores\"]\n", "diff_scores = result[\"different_sample_scores\"]\n", "\n", "# Convert to DataFrame\n", "plot_data = pd.DataFrame(\n", "    {\"score\": same_scores + diff_scores,\n", "     \"type\": [\"Same Sample\"] * len(same_scores) + [\"Different Sample\"] * len(diff_scores)}\n", ")\n", "\n", "# Plot\n", "sns.set(style=\"whitegrid\")\n", "sns.histplot(data=plot_data, x=\"score\", hue=\"type\", bins=30, kde=True, palette=\"Set2\")\n", "\n", "plt.xlabel(\"Cosine Similarity Score\")\n", "plt.ylabel(\"Frequency\")\n", "plt.legend(title=\"Comparison Type\")\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 22, "id": "52a1a95c", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_7231/2155350413.py:25: User<PERSON><PERSON>ning: No artists with labels found to put in legend.  Note that artists whose label start with an underscore are ignored when legend() is called with no argument.\n", "  plt.legend(title=\"Comparison Type\")\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import json\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "import pandas as pd\n", "\n", "# Load result\n", "with open(\"all-MiniLM-L12-v2_validation_results.json\", \"r\") as f:\n", "    result = json.load(f)\n", "\n", "same_scores = result[\"same_sample_scores\"]\n", "diff_scores = result[\"different_sample_scores\"]\n", "\n", "# Convert to DataFrame\n", "plot_data = pd.DataFrame(\n", "    {\"score\": same_scores + diff_scores,\n", "     \"type\": [\"Same Sample\"] * len(same_scores) + [\"Different Sample\"] * len(diff_scores)}\n", ")\n", "\n", "# Plot\n", "sns.set(style=\"whitegrid\")\n", "sns.histplot(data=plot_data, x=\"score\", hue=\"type\", bins=30, kde=True, palette=\"Set2\")\n", "\n", "plt.xlabel(\"Cosine Similarity Score\")\n", "plt.ylabel(\"Frequency\")\n", "plt.legend(title=\"Comparison Type\")\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 1, "id": "9c983b31", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_7834/2646472170.py:25: User<PERSON><PERSON>ning: No artists with labels found to put in legend.  Note that artists whose label start with an underscore are ignored when legend() is called with no argument.\n", "  plt.legend(title=\"Comparison Type\")\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import json\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "import pandas as pd\n", "\n", "# Load result\n", "with open(\"all-mpnet-base-v2_validation_results.json\", \"r\") as f:\n", "    result = json.load(f)\n", "\n", "same_scores = result[\"same_sample_scores\"]\n", "diff_scores = result[\"different_sample_scores\"]\n", "\n", "# Convert to DataFrame\n", "plot_data = pd.DataFrame(\n", "    {\"score\": same_scores + diff_scores,\n", "     \"type\": [\"Same Sample\"] * len(same_scores) + [\"Different Sample\"] * len(diff_scores)}\n", ")\n", "\n", "# Plot\n", "sns.set(style=\"whitegrid\")\n", "sns.histplot(data=plot_data, x=\"score\", hue=\"type\", bins=30, kde=True, palette=\"Set2\")\n", "\n", "plt.xlabel(\"Cosine Similarity Score\")\n", "plt.ylabel(\"Frequency\")\n", "plt.legend(title=\"Comparison Type\")\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "1ae274c8", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}