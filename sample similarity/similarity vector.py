import json

from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity


def flatten_json(data, parent_key="", sep="."):
    """Recursively flattens a nested JSON object."""
    items = []
    for k, v in data.items():
        new_key = f"{parent_key}{sep}{k}" if parent_key else k
        if isinstance(v, dict):
            items.extend(flatten_json(v, new_key, sep=sep).items())
        elif isinstance(v, list):
            items.append((new_key, " ".join(map(str, v))))
        else:
            items.append((new_key, str(v)))
    return dict(items)


def json_to_string(data):
    """Convert flattened JSON to a string for TF-IDF vectorization."""
    return " ".join(f"{k}:{v}" for k, v in sorted(data.items()))


def nested_json_tfidf_cosine_similarity(json1, json2):
    flat1 = flatten_json(json1)
    flat2 = flatten_json(json2)

    str1 = json_to_string(flat1)
    str2 = json_to_string(flat2)

    vectorizer = TfidfVectorizer().fit([str1, str2])
    vectors = vectorizer.transform([str1, str2])

    return cosine_similarity(vectors[0], vectors[1])[0][0]


# --------------------
# Main script
# --------------------
input_file = "sample_similarity_jsons.json"  # Input JSONL file
output_file = "similarity_results.json"  # Output JSON file
attribute_1 = "ground_truth_json"  # Replace with the key name of the first JSON string
attribute_2 = "generated_json"  # Replace with the key name of the second JSON string

results = []
with open(input_file, "r", encoding="utf-8") as f:
    data = json.load(f)  # Load the list of dictionaries

for record in data:
    if not record.get(attribute_2):  # Skip if generated_json is empty
        continue
    # Parse the JSON strings
    json1 = json.loads(record[attribute_1])
    json2 = json.loads(record[attribute_2])

    # Compute similarity
    similarity_score = nested_json_tfidf_cosine_similarity(json1, json2)

    # Append to results
    results.append(similarity_score)


# Save results
with open(output_file, "w", encoding="utf-8") as f:
    json.dump(results, f, indent=4)

print(f"Saved {len(results)} similarity scores to {output_file}")
