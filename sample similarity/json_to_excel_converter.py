#!/usr/bin/env python3
"""
Simple JSON to Excel Converter
Usage: python json_to_excel_converter.py <input_json_file> [output_excel_file]
"""

import json
import os
import sys
import pandas as pd


def convert_json_to_excel(json_file_path, output_file_path=None):
    """Convert JSON data to Excel format with multiple sheets"""
    
    # Load the JSON data
    try:
        with open(json_file_path, 'r', encoding='utf-8') as file:
            data = json.load(file)
    except Exception as e:
        print(f"Error loading JSON file: {e}")
        return False
    
    # Create output filename if not provided
    if output_file_path is None:
        base_name = os.path.splitext(os.path.basename(json_file_path))[0]
        output_file_path = f"{base_name}.xlsx"
    
    try:
        # Convert to DataFrame
        df = pd.DataFrame(data)
        
        # Create Excel writer object
        with pd.ExcelWriter(output_file_path, engine="openpyxl") as writer:
            
            # Write main data to 'Data' sheet
            df.to_excel(writer, sheet_name="Data", index=False)
            
            # If the data has similarity metrics, create a summary sheet
            similarity_columns = [col for col in df.columns if 'similarity' in col.lower()]
            if similarity_columns:
                summary_data = {
                    'Metric': ['Total Records'],
                    'Value': [len(df)]
                }
                
                # Add type counts if 'type' column exists
                if 'type' in df.columns:
                    type_counts = df['type'].value_counts()
                    for type_name, count in type_counts.items():
                        summary_data['Metric'].append(f'{type_name.title()} Type Count')
                        summary_data['Value'].append(count)
                
                # Add similarity averages
                for col in similarity_columns:
                    if df[col].dtype in ['float64', 'int64']:
                        summary_data['Metric'].append(f'Average {col.replace("_", " ").title()}')
                        summary_data['Value'].append(df[col].mean())
                
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name="Summary", index=False)
                
                # Create separate sheets by type if 'type' column exists
                if 'type' in df.columns:
                    for type_name in df['type'].unique():
                        type_df = df[df['type'] == type_name]
                        if not type_df.empty:
                            sheet_name = f"{type_name.title()} Type"
                            type_df.to_excel(writer, sheet_name=sheet_name, index=False)
        
        print(f"✅ Excel file created successfully: {output_file_path}")
        print(f"📊 Total records processed: {len(df)}")
        
        if 'type' in df.columns:
            type_counts = df['type'].value_counts()
            for type_name, count in type_counts.items():
                print(f"   - {type_name.title()} type: {count} records")
        
        return True
        
    except Exception as e:
        print(f"Error creating Excel file: {e}")
        return False


def main():
    if len(sys.argv) < 2:
        print("Usage: python json_to_excel_converter.py <input_json_file> [output_excel_file]")
        print("Example: python json_to_excel_converter.py data.json")
        print("Example: python json_to_excel_converter.py data.json output.xlsx")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else None
    
    if not os.path.exists(input_file):
        print(f"❌ Error: File not found: {input_file}")
        sys.exit(1)
    
    success = convert_json_to_excel(input_file, output_file)
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
