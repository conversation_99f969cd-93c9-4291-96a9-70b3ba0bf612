import json
import os

import pandas as pd


def load_json_data(file_path):
    """Load JSON data from file"""
    try:
        with open(file_path, "r", encoding="utf-8") as file:
            data = json.load(file)
        return data
    except Exception as e:
        print(f"Error loading JSON file: {e}")
        return None


def convert_json_to_excel(json_file_path, output_file_path=None):
    """Convert JSON data to Excel format"""

    # Load the JSON data
    data = load_json_data(json_file_path)
    if data is None:
        return

    # Create output filename if not provided
    if output_file_path is None:
        base_name = os.path.splitext(os.path.basename(json_file_path))[0]
        output_file_path = f"{base_name}.xlsx"

    # Convert to DataFrame
    df = pd.DataFrame(data)

    # Create Excel writer object
    with pd.ExcelWriter(output_file_path, engine="openpyxl") as writer:

        # Write main data to 'Data' sheet
        df.to_excel(writer, sheet_name="Data", index=False)

        # Create a summary sheet with statistics
        summary_data = {
            "Metric": [
                "Total Records",
                "Same Type Count",
                "Different Type Count",
                "Average Count Vectorizer Similarity",
                "Average Dict Vectorizer Similarity",
                "Average TF-IDF Similarity",
                "Average Sentence Transformer (DistilRoBERTa) Similarity",
                "Average Sentence Transformer (MiniLM-L6) Similarity",
                "Average Sentence Transformer (MiniLM-L12) Similarity",
                "Average Sentence Transformer (MPNet) Similarity",
            ],
            "Value": [
                len(df),
                len(df[df["type"] == "same"]),
                len(df[df["type"] == "different"]),
                df["count_vectorizer_similarity"].mean(),
                df["dict_vectorizer_similarity"].mean(),
                df["tfidf_similarity"].mean(),
                df["sentence_transformer_similarity_all_distilroberta_v1"].mean(),
                df["sentence_transformer_similarity_all_MiniLM_L6_v2"].mean(),
                df["sentence_transformer_similarity_all_MiniLM_L12_v2"].mean(),
                df["sentence_transformer_similarity_all_mpnet_base_v2"].mean(),
            ],
        }

        summary_df = pd.DataFrame(summary_data)
        summary_df.to_excel(writer, sheet_name="Summary", index=False)

        # Create separate sheets for 'same' and 'different' types
        same_df = df[df["type"] == "same"]
        different_df = df[df["type"] == "different"]

        if not same_df.empty:
            same_df.to_excel(writer, sheet_name="Same Type", index=False)

        if not different_df.empty:
            different_df.to_excel(writer, sheet_name="Different Type", index=False)

    print(f"Excel file created successfully: {output_file_path}")
    print(f"Total records processed: {len(df)}")
    print(f"Records with 'same' type: {len(same_df)}")
    print(f"Records with 'different' type: {len(different_df)}")


if __name__ == "__main__":
    # Define the input file path
    input_file = "comparison_data_with_similarities.json"

    # Check if file exists
    if os.path.exists(input_file):
        # Convert to Excel
        convert_json_to_excel(input_file)
    else:
        print(f"File not found: {input_file}")
        print("Please make sure the JSON file exists in the current directory.")
