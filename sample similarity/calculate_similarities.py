#!/usr/bin/env python3
"""
<PERSON><PERSON>t to calculate similarity scores using all four validation functions
for the comparison_data.json file and save all attributes along with similarities.
"""

import json
from typing import Dict, Any

# Import similarity functions
from sklearn.feature_extraction.text import CountVectorizer, TfidfVectorizer
from sklearn.feature_extraction import DictVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from sentence_transformers import SentenceTransformer


def flatten_json(data, parent_key="", sep="."):
    """Flatten nested JSON structure"""
    items = []
    for k, v in data.items():
        new_key = f"{parent_key}{sep}{k}" if parent_key else k
        if isinstance(v, dict):
            items.extend(flatten_json(v, new_key, sep=sep).items())
        elif isinstance(v, list):
            items.append((new_key, " ".join(map(str, v))))
        else:
            items.append((new_key, str(v)))
    return dict(items)


def json_to_string(data):
    """Convert flattened JSON to string representation"""
    return " ".join(f"{k}:{v}" for k, v in sorted(data.items()))


def count_vectorizer_similarity(json1, json2):
    """Calculate similarity using CountVectorizer (from count_validation_function.py)"""
    flat1 = flatten_json(json1)
    flat2 = flatten_json(json2)
    
    str1 = json_to_string(flat1)
    str2 = json_to_string(flat2)
    
    vectorizer = CountVectorizer().fit([str1, str2])
    vectors = vectorizer.transform([str1, str2])
    
    return cosine_similarity(vectors[0], vectors[1])[0][0]


def dict_vectorizer_similarity(json1, json2):
    """Calculate similarity using DictVectorizer (from dict_validation_function.py)"""
    flat1 = flatten_json(json1)
    flat2 = flatten_json(json2)
    
    vectorizer = DictVectorizer().fit([flat1, flat2])
    vectors = vectorizer.transform([flat1, flat2])
    
    return cosine_similarity(vectors[0], vectors[1])[0][0]


def tfidf_similarity(json1, json2):
    """Calculate similarity using TfidfVectorizer with n8n stop words (from tfidf_validation_function.py)"""
    flat1 = flatten_json(json1)
    flat2 = flatten_json(json2)
    
    str1 = json_to_string(flat1)
    str2 = json_to_string(flat2)
    
    # Define stop words for n8n workflow JSON data
    n8n_stop_words = [
        "active", "name", "nodes", "connections", "position", "parameters",
        "type", "typeVersion", "index", "node", "main"
    ]
    
    vectorizer = TfidfVectorizer(stop_words=n8n_stop_words).fit([str1, str2])
    vectors = vectorizer.transform([str1, str2])
    
    return cosine_similarity(vectors[0], vectors[1])[0][0]


def sentence_transformer_similarity(json1, json2, model):
    """Calculate similarity using SentenceTransformer (from sentence_transformer_validation_function.py)"""
    flat1 = flatten_json(json1)
    flat2 = flatten_json(json2)
    
    str1 = json_to_string(flat1)
    str2 = json_to_string(flat2)
    
    vecs = model.encode([str1, str2])
    return cosine_similarity([vecs[0]], [vecs[1]])[0][0]


def calculate_all_similarities(sample: Dict[str, Any], model) -> Dict[str, float]:
    """Calculate all similarity scores for a single sample"""
    try:
        # Parse JSON strings
        json1 = json.loads(sample["original_json"])
        json2 = json.loads(sample["comparison_json"])
        
        # Calculate similarities using all methods
        count_sim = count_vectorizer_similarity(json1, json2)
        dict_sim = dict_vectorizer_similarity(json1, json2)
        tfidf_sim = tfidf_similarity(json1, json2)
        sbert_sim = sentence_transformer_similarity(json1, json2, model)
        
        return {
            "count_vectorizer_similarity": float(count_sim),
            "dict_vectorizer_similarity": float(dict_sim),
            "tfidf_similarity": float(tfidf_sim),
            "sentence_transformer_similarity": float(sbert_sim)
        }
    except (json.JSONDecodeError, Exception) as e:
        print(f"Error calculating similarities: {e}")
        return {
            "count_vectorizer_similarity": 0.0,
            "dict_vectorizer_similarity": 0.0,
            "tfidf_similarity": 0.0,
            "sentence_transformer_similarity": 0.0
        }


def main():
    """Main function to process comparison data and add similarity scores"""
    # File paths
    input_file = "comparison_data.json"
    output_file = "comparison_data_with_similarities.json"
    
    # Load the SentenceTransformer model
    print("Loading SentenceTransformer model (all-mpnet-base-v2)...")
    model = SentenceTransformer("all-mpnet-base-v2")
    
    # Load comparison data
    print(f"Loading comparison data from {input_file}...")
    try:
        with open(input_file, "r", encoding="utf-8") as f:
            comparison_data = json.load(f)
    except FileNotFoundError:
        print(f"Error: {input_file} not found. Please make sure the file exists.")
        return
    except json.JSONDecodeError:
        print(f"Error: Invalid JSON in {input_file}")
        return
    
    print(f"Processing {len(comparison_data)} comparison samples...")
    
    # Process each sample and add similarity scores
    processed_data = []
    for i, sample in enumerate(comparison_data):
        if i % 10 == 0:
            print(f"Processing sample {i+1}/{len(comparison_data)}")
        
        # Create new sample with all original attributes
        processed_sample = sample.copy()
        
        # Add similarity scores
        similarities = calculate_all_similarities(sample, model)
        processed_sample.update(similarities)
        
        processed_data.append(processed_sample)
    
    # Save processed data
    with open(output_file, "w", encoding="utf-8") as f:
        json.dump(processed_data, f, indent=2, ensure_ascii=False)
    
    print(f"\nProcessed data with similarities saved to: {output_file}")
    print(f"Total samples processed: {len(processed_data)}")
    
    # Show statistics
    same_samples = [s for s in processed_data if s.get("type") == "same"]
    different_samples = [s for s in processed_data if s.get("type") == "different"]
    
    print(f"Same type samples: {len(same_samples)}")
    print(f"Different type samples: {len(different_samples)}")
    
    # Show sample output
    if processed_data:
        print("\nSample output structure:")
        sample = processed_data[0].copy()
        # Truncate long JSON strings for display
        if len(sample.get("original_json", "")) > 100:
            sample["original_json"] = sample["original_json"][:100] + "..."
        if len(sample.get("comparison_json", "")) > 100:
            sample["comparison_json"] = sample["comparison_json"][:100] + "..."
        print(json.dumps(sample, indent=2))
    
    print(f"\nAll attributes from original data preserved along with similarity scores:")
    print("- count_vectorizer_similarity")
    print("- dict_vectorizer_similarity") 
    print("- tfidf_similarity")
    print("- sentence_transformer_similarity")


if __name__ == "__main__":
    main()
