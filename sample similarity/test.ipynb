{"cells": [{"cell_type": "code", "execution_count": 16, "id": "3e0f5d62", "metadata": {}, "outputs": [], "source": ["import json\n", "import seaborn as sns"]}, {"cell_type": "code", "execution_count": 18, "id": "f507f6e4", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: ylabel='Count'>"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["scores = json.load(open(\"similarity_results.json\", \"r\"))\n", "sns.histplot(scores, bins=10, kde=True)\n"]}, {"cell_type": "code", "execution_count": null, "id": "fca7cb25", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}