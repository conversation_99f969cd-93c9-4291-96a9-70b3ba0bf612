import json
import random
from sentence_transformers import SentenceTransformer
from sklearn.metrics.pairwise import cosine_similarity

## need scipy, pyarrow, sympy, dill
# Load model
model_name = "all-mpnet-base-v2"
model = SentenceTransformer(model_name)


def flatten_json(data, parent_key="", sep="."):
    items = []
    for k, v in data.items():
        new_key = f"{parent_key}{sep}{k}" if parent_key else k
        if isinstance(v, dict):
            items.extend(flatten_json(v, new_key, sep=sep).items())
        elif isinstance(v, list):
            items.append((new_key, " ".join(map(str, v))))
        else:
            items.append((new_key, str(v)))
    return dict(items)


def json_to_string(data):
    return " ".join(f"{k}:{v}" for k, v in sorted(data.items()))


def nested_json_sbert_similarity(json1, json2):
    flat1 = flatten_json(json1)
    flat2 = flatten_json(json2)
    str1 = json_to_string(flat1)
    str2 = json_to_string(flat2)
    vecs = model.encode([str1, str2])
    return cosine_similarity([vecs[0]], [vecs[1]])[0][0]


# ---- Main Logic Same As Original ----
if __name__ == "__main__":
    input_file = "sample_similarity_jsons.json"
    output_file = f"{model_name}_validation_results.json"

    attribute_1 = "ground_truth_json"
    attribute_2 = "generated_json"

    same_sample_scores = []
    different_sample_scores = []

    with open(input_file, "r", encoding="utf-8") as f:
        data = json.load(f)

    parsed_data = []
    for record in data:
        if not record.get(attribute_2):
            continue
        try:
            json1 = json.loads(record[attribute_1])
            json2 = json.loads(record[attribute_2])
            parsed_data.append((json1, json2))
        except json.JSONDecodeError:
            continue

    for json1, json2 in parsed_data:
        score = nested_json_sbert_similarity(json1, json2)
        same_sample_scores.append(score)

    num_samples = len(parsed_data)
    for i in range(num_samples):
        json1, _ = parsed_data[i]
        j = random.choice([x for x in range(num_samples) if x != i])
        _, random_json2 = parsed_data[j]
        score = nested_json_sbert_similarity(json1, random_json2)
        different_sample_scores.append(score)

    # Before saving
    validation_output = {
        "same_sample_scores": [float(x) for x in same_sample_scores],
        "different_sample_scores": [float(x) for x in different_sample_scores],
    }


    with open(output_file, "w", encoding="utf-8") as f:
        json.dump(validation_output, f, indent=4)

    print(f"Saved validation results to {output_file}")
