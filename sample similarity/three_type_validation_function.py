import json
import random

import torch
from sentence_transformers import SentenceTransformer
from sklearn.metrics.pairwise import cosine_similarity
from transformers import AutoModel, AutoTokenizer

# -----------------------
# Load all models
# -----------------------
sbert_model = SentenceTransformer("all-MiniLM-L6-v2")
# autoencoder_model = SentenceTransformer(
#     "tsdae-msmarco-MiniLM-L6-dot-v1"
# )
json_transformer_tokenizer = AutoTokenizer.from_pretrained("Salesforce/codet5-small")
json_transformer_model = AutoModel.from_pretrained("Salesforce/codet5-small")


# -----------------------
# JSON Processing
# -----------------------
def flatten_json(data, parent_key="", sep="."):
    items = []
    for k, v in data.items():
        new_key = f"{parent_key}{sep}{k}" if parent_key else k
        if isinstance(v, dict):
            items.extend(flatten_json(v, new_key, sep=sep).items())
        elif isinstance(v, list):
            items.append((new_key, " ".join(map(str, v))))
        else:
            items.append((new_key, str(v)))
    return dict(items)


def json_to_string(data):
    return " ".join(f"{k}:{v}" for k, v in sorted(data.items()))


# -----------------------
# Similarity Functions
# -----------------------
def similarity_sbert(json1, json2):
    flat1 = json_to_string(flatten_json(json1))
    flat2 = json_to_string(flatten_json(json2))
    vecs = sbert_model.encode([flat1, flat2])
    return cosine_similarity([vecs[0]], [vecs[1]])[0][0]


# def similarity_autoencoder(json1, json2):
#     flat1 = json_to_string(flatten_json(json1))
#     flat2 = json_to_string(flatten_json(json2))
#     vecs = autoencoder_model.encode([flat1, flat2])
#     return cosine_similarity([vecs[0]], [vecs[1]])[0][0]


def similarity_json_transformer(json1, json2):
    json_str1 = json.dumps(json1)
    json_str2 = json.dumps(json2)
    inputs = json_transformer_tokenizer(
        [json_str1, json_str2], return_tensors="pt", padding=True, truncation=True
    )
    with torch.no_grad():
        output = json_transformer_model(**inputs).last_hidden_state
    emb1 = output[0][0]
    emb2 = output[1][0]
    return torch.nn.functional.cosine_similarity(emb1, emb2, dim=0).item()


# -----------------------
# Main Execution
# -----------------------
input_file = "sample_similarity_jsons.json"
output_file = "combined_similarity_results.json"

attribute_1 = "ground_truth_json"
attribute_2 = "generated_json"

# Store scores for all three methods
results = {
    "sbert_similarity": {"same_sample_scores": [], "different_sample_scores": []},
    # "autoencoder_similarity": {"same_sample_scores": [], "different_sample_scores": []},
    "json_transformer_similarity": {
        "same_sample_scores": [],
        "different_sample_scores": [],
    },
}

# Read and parse JSON data
with open(input_file, "r", encoding="utf-8") as f:
    data = json.load(f)

parsed_data = []
for record in data:
    if not record.get(attribute_2):
        continue
    try:
        json1 = json.loads(record[attribute_1])
        json2 = json.loads(record[attribute_2])
        parsed_data.append((json1, json2))
    except json.JSONDecodeError:
        continue

# Same sample comparisons
for json1, json2 in parsed_data:
    results["sbert_similarity"]["same_sample_scores"].append(
        similarity_sbert(json1, json2)
    )
    # results["autoencoder_similarity"]["same_sample_scores"].append(
    #     similarity_autoencoder(json1, json2)
    # )
    results["json_transformer_similarity"]["same_sample_scores"].append(
        similarity_json_transformer(json1, json2)
    )

# Different sample comparisons
num_samples = len(parsed_data)
for i in range(num_samples):
    _, json2 = parsed_data[i]
    j = random.choice([x for x in range(num_samples) if x != i])
    _, json2_rand = parsed_data[j]

    results["sbert_similarity"]["different_sample_scores"].append(
        similarity_sbert(json2, json2_rand)
    )
    # results["autoencoder_similarity"]["different_sample_scores"].append(
    #     similarity_autoencoder(json2, json2_rand)
    # )
    results["json_transformer_similarity"]["different_sample_scores"].append(
        similarity_json_transformer(json2, json2_rand)
    )

# Convert to float and save
for method in results:
    results[method]["same_sample_scores"] = [
        float(x) for x in results[method]["same_sample_scores"]
    ]
    results[method]["different_sample_scores"] = [
        float(x) for x in results[method]["different_sample_scores"]
    ]

with open(output_file, "w", encoding="utf-8") as f:
    json.dump(results, f, indent=4)

print(f"Saved combined validation results to {output_file}")
