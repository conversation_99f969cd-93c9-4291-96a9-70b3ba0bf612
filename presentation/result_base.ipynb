{"cells": [{"cell_type": "code", "execution_count": 35, "id": "cf9d20bd", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import seaborn as sns"]}, {"cell_type": "code", "execution_count": 36, "id": "2a29a094", "metadata": {}, "outputs": [], "source": ["df = pd.read_json(\"qwen3-14B-base.json\")"]}, {"cell_type": "code", "execution_count": 38, "id": "f9419ccf", "metadata": {}, "outputs": [], "source": ["df[\"percent_node_with_valid_type\"] = df[\"num_nodes_with_valid_type\"] / df[\"num_nodes\"] * 100\n", "df[\"percent_node_with_valid_version\"] = df[\"num_nodes_with_valid_version\"] / df[\"num_nodes\"] * 100\n", "df[\"percent_node_with_valid_structure\"] = df[\"num_nodes_with_valid_structure\"] / df[\"num_nodes\"] * 100\n", "df[\"percent_connections_with_valid_structure\"] = df[\"num_connections_with_valid_structure\"] / df[\"num_connections\"] * 100\n", "df[\"percent_connections_with_valid_target_node\"] = df[\"num_connections_with_valid_target_node\"] / df[\"num_connections\"] * 100\n", "df[\"percent_node_with_parameters\"] = df[\"num_nodes_with_parameters\"] / df[\"num_nodes\"] * 100"]}, {"cell_type": "markdown", "id": "5ad3f647", "metadata": {}, "source": ["# Base valid n8n json"]}, {"cell_type": "code", "execution_count": 44, "id": "a18db11e", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_type', ylabel='Count'>"]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_type\")"]}, {"cell_type": "markdown", "id": "ad00bb31", "metadata": {}, "source": ["# SFT valid n8n json"]}, {"cell_type": "code", "execution_count": null, "id": "85c0704a", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_type', ylabel='Count'>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAkAAAAGxCAYAAACKvAkXAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjAsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvlHJYcgAAAAlwSFlzAAAPYQAAD2EBqD+naQAAN7dJREFUeJzt3Xl8VNX9//H3AGGIkAQDkgVIwr43IDtlCVKCwQWLyKJCKIJS2YQimCJflm+/BqwKRag8rELcEFpZFYoESoIIUraIUqBBA0kxKQ1CwmYC5Pz+8MeUMXuYZCbc1/PxuI8H995zznzu4ZK8uffOjM0YYwQAAGAhVdxdAAAAQEUjAAEAAMshAAEAAMshAAEAAMshAAEAAMshAAEAAMshAAEAAMshAAEAAMup5u4CPFFeXp6+++47+fj4yGazubscAABQAsYYXbx4UcHBwapSpehrPASgAnz33Xdq2LChu8sAAABlkJaWpgYNGhTZhgBUAB8fH0k/TqCvr6+bqwEAACWRnZ2thg0bOn6PF4UAVICbt718fX0JQAAAVDIleXyFh6ABAIDlEIAAAIDlEIAAAIDlEIAAAIDlEIAAAIDlEIAAAIDlEIAAAIDlEIAAAIDlEIAAAIDlEIAAAIDluDUAxcbGqnPnzvLx8VG9evX0yCOP6MSJE05tjDGaO3eugoOD5e3trYiICB09erTYsdeuXavWrVvLbrerdevWWr9+fXkdBgAAqGTcGoASExM1YcIEffHFF4qPj9f169cVGRmpy5cvO9q8/PLLeu2117R06VLt379fgYGB6t+/vy5evFjouHv37tWwYcM0cuRIffnllxo5cqSGDh2qffv2VcRhAQAAD2czxhh3F3HTf/7zH9WrV0+JiYnq3bu3jDEKDg7Wc889p5kzZ0qScnJyFBAQoIULF+qZZ54pcJxhw4YpOztbf/3rXx3b7r//ft1999368MMPi60jOztbfn5+ysrK4stQAQCoJErz+9ujngHKysqSJPn7+0uSUlJSlJGRocjISEcbu92uPn36aM+ePYWOs3fvXqc+kjRgwIAi+wAAAOuo5u4CbjLGaNq0aerZs6fatm0rScrIyJAkBQQEOLUNCAjQ6dOnCx0rIyOjwD43x/upnJwc5eTkONazs7PLdAwAAHiC1NRUZWZmuruMItWtW1chISFue32PCUATJ07UkSNHtHv37nz7bDab07oxJt+22+kTGxurefPmlbJiAAA8T2pqqlq2bKWrV6+4u5QieXvfpePHj7ktBHlEAJo0aZI2bdqkXbt2qUGDBo7tgYGBkn68ohMUFOTYfvbs2XxXeG4VGBiY72pPUX1iYmI0bdo0x3p2drYaNmxYpmMBAMCdMjMzdfXqFXUdM0e+QWHuLqdA2emntG/FPGVmZlozABljNGnSJK1fv14JCQlq1KiR0/5GjRopMDBQ8fHx6tChgyQpNzdXiYmJWrhwYaHjdu/eXfHx8Zo6dapj27Zt29SjR48C29vtdtntdhccEQAAnsE3KEz+IS3cXYbHcmsAmjBhglatWqWNGzfKx8fHcdXGz89P3t7estlseu655/TSSy+pWbNmatasmV566SXdddddevzxxx3jjBo1SvXr11dsbKwkacqUKerdu7cWLlyoQYMGaePGjdq+fXuBt9cAAID1uDUAvfHGG5KkiIgIp+0rV67U6NGjJUkzZszQ1atX9eyzz+r8+fPq2rWrtm3bJh8fH0f71NRUVany3ze09ejRQ6tXr9aLL76o2bNnq0mTJlqzZo26du1a7scEAAA8n9tvgRXHZrNp7ty5mjt3bqFtEhIS8m0bMmSIhgwZchvVAQCAO5VHfQ4QAABARSAAAQAAyyEAAQAAyyEAAQAAyyEAAQAAyyEAAQAAyyEAAQAAyyEAAQAAyyEAAQAAyyEAAQAAyyEAAQAAyyEAAQAAyyEAAQAAyyEAAQAAyyEAAQAAyyEAAQAAyyEAAQAAyyEAAQAAyyEAAQAAyyEAAQAAyyEAAQAAyyEAAQAAyyEAAQAAyyEAAQAAyyEAAQAAyyEAAQAAyyEAAQAAyyEAAQAAyyEAAQAAyyEAAQAAyyEAAQAAyyEAAQAAyyEAAQAAy3FrANq1a5ceeughBQcHy2azacOGDU77bTZbgcvvf//7QseMi4srsM8PP/xQzkcDAAAqC7cGoMuXLys8PFxLly4tcH96errTsmLFCtlsNj366KNFjuvr65uvb40aNcrjEAAAQCVUzZ0vHhUVpaioqEL3BwYGOq1v3LhRffv2VePGjYsc12az5esLAABwU6V5Bujf//63Nm/erKeeeqrYtpcuXVJoaKgaNGigBx98UIcPH66ACgEAQGVRaQLQO++8Ix8fHw0ePLjIdi1btlRcXJw2bdqkDz/8UDVq1NDPf/5zJScnF9onJydH2dnZTgsAALhzVZoAtGLFCj3xxBPFPsvTrVs3PfnkkwoPD1evXr305z//Wc2bN9frr79eaJ/Y2Fj5+fk5loYNG7q6fAAA4EEqRQD67LPPdOLECY0dO7bUfatUqaLOnTsXeQUoJiZGWVlZjiUtLe12ygUAAB7OrQ9Bl9Tbb7+tjh07Kjw8vNR9jTFKSkpSu3btCm1jt9tlt9tvp0QAAFCJuDUAXbp0SSdPnnSsp6SkKCkpSf7+/goJCZEkZWdn6y9/+YteffXVAscYNWqU6tevr9jYWEnSvHnz1K1bNzVr1kzZ2dlasmSJkpKStGzZsvI/IAAAUCm4NQAdOHBAffv2daxPmzZNkhQdHa24uDhJ0urVq2WM0YgRIwocIzU1VVWq/PdO3oULF/T0008rIyNDfn5+6tChg3bt2qUuXbqU34EAAIBKxa0BKCIiQsaYIts8/fTTevrppwvdn5CQ4LS+aNEiLVq0yBXlAQCAO1SleAgaAADAlQhAAADAcghAAADAcghAAADAcghAAADAcghAAADAcghAAADAcghAAADAcghAAADAcghAAADAcghAAADAcghAAADAcghAAADAcghAAADAcghAAADAcghAAADAcghAAADAcghAAADAcghAAADAcghAAADAcghAAADAcghAAADAcghAAADAcghAAADAcghAAADAcghAAADAcghAAADAcghAAADAcghAAADAcghAAADAcghAAADAcghAAADAcghAAADActwagHbt2qWHHnpIwcHBstls2rBhg9P+0aNHy2azOS3dunUrdty1a9eqdevWstvtat26tdavX19ORwAAACojtwagy5cvKzw8XEuXLi20zf3336/09HTHsmXLliLH3Lt3r4YNG6aRI0fqyy+/1MiRIzV06FDt27fP1eUDAIBKqpo7XzwqKkpRUVFFtrHb7QoMDCzxmIsXL1b//v0VExMjSYqJiVFiYqIWL16sDz/88LbqBQAAdwaPfwYoISFB9erVU/PmzTVu3DidPXu2yPZ79+5VZGSk07YBAwZoz5495VkmAACoRNx6Bag4UVFReuyxxxQaGqqUlBTNnj1b9913nw4ePCi73V5gn4yMDAUEBDhtCwgIUEZGRqGvk5OTo5ycHMd6dna2aw4AAAB4JI8OQMOGDXP8uW3bturUqZNCQ0O1efNmDR48uNB+NpvNad0Yk2/brWJjYzVv3rzbLxgAAFQKHn8L7FZBQUEKDQ1VcnJyoW0CAwPzXe05e/ZsvqtCt4qJiVFWVpZjSUtLc1nNAADA81SqAHTu3DmlpaUpKCio0Dbdu3dXfHy807Zt27apR48ehfax2+3y9fV1WgAAwJ3LrbfALl26pJMnTzrWU1JSlJSUJH9/f/n7+2vu3Ll69NFHFRQUpFOnTum3v/2t6tatq1/+8peOPqNGjVL9+vUVGxsrSZoyZYp69+6thQsXatCgQdq4caO2b9+u3bt3V/jxAQAAz+TWAHTgwAH17dvXsT5t2jRJUnR0tN544w199dVXevfdd3XhwgUFBQWpb9++WrNmjXx8fBx9UlNTVaXKfy9k9ejRQ6tXr9aLL76o2bNnq0mTJlqzZo26du1acQcGAAA8mlsDUEREhIwxhe7/9NNPix0jISEh37YhQ4ZoyJAht1MaAAC4g1WqZ4AAAABcgQAEAAAshwAEAAAshwAEAAAshwAEAAAshwAEAAAshwAEAAAshwAEAAAshwAEAAAshwAEAAAshwAEAAAshwAEAAAshwAEAAAshwAEAAAshwAEAAAshwAEAAAshwAEAAAshwAEAAAshwAEAAAshwAEAAAshwAEAAAshwAEAAAshwAEAAAshwAEAAAshwAEAAAshwAEAAAshwAEAAAshwAEAAAshwAEAAAshwAEAAAshwAEAAAshwAEAAAshwAEAAAsx60BaNeuXXrooYcUHBwsm82mDRs2OPZdu3ZNM2fOVLt27VSzZk0FBwdr1KhR+u6774ocMy4uTjabLd/yww8/lPPRAACAysKtAejy5csKDw/X0qVL8+27cuWKDh06pNmzZ+vQoUNat26d/vnPf+rhhx8udlxfX1+lp6c7LTVq1CiPQwAAAJVQNXe+eFRUlKKiogrc5+fnp/j4eKdtr7/+urp06aLU1FSFhIQUOq7NZlNgYKBLawUAAHeOSvUMUFZWlmw2m2rXrl1ku0uXLik0NFQNGjTQgw8+qMOHD1dMgQAAoFKoNAHohx9+0AsvvKDHH39cvr6+hbZr2bKl4uLitGnTJn344YeqUaOGfv7znys5ObnQPjk5OcrOznZaAADAnatSBKBr165p+PDhysvL0x//+Mci23br1k1PPvmkwsPD1atXL/35z39W8+bN9frrrxfaJzY2Vn5+fo6lYcOGrj4EAADgQTw+AF27dk1Dhw5VSkqK4uPji7z6U5AqVaqoc+fORV4BiomJUVZWlmNJS0u73bIBAIAHc+tD0MW5GX6Sk5O1c+dO1alTp9RjGGOUlJSkdu3aFdrGbrfLbrffTqkAAKAScWsAunTpkk6ePOlYT0lJUVJSkvz9/RUcHKwhQ4bo0KFD+uSTT3Tjxg1lZGRIkvz9/VW9enVJ0qhRo1S/fn3FxsZKkubNm6du3bqpWbNmys7O1pIlS5SUlKRly5ZV/AECAACP5NYAdODAAfXt29exPm3aNElSdHS05s6dq02bNkmS2rdv79Rv586dioiIkCSlpqaqSpX/3sm7cOGCnn76aWVkZMjPz08dOnTQrl271KVLl/I9GAAAUGm4NQBFRETIGFPo/qL23ZSQkOC0vmjRIi1atOh2SwMAAHcwj38IGgAAwNUIQAAAwHIIQAAAwHIIQAAAwHIIQAAAwHIIQAAAwHIIQAAAwHIIQAAAwHIIQAAAwHIIQAAAwHLKFIAaN26sc+fO5dt+4cIFNW7c+LaLAgAAKE9lCkCnTp3SjRs38m3PycnRmTNnbrsoAACA8lSqL0O9+e3skvTpp5/Kz8/PsX7jxg3t2LFDYWFhLisOAACgPJQqAD3yyCOSJJvNpujoaKd9Xl5eCgsL06uvvuqy4gAAAMpDqQJQXl6eJKlRo0bav3+/6tatWy5FAQAAlKdSBaCbUlJSXF0HAABAhSlTAJKkHTt2aMeOHTp79qzjytBNK1asuO3CAAAAykuZAtC8efM0f/58derUSUFBQbLZbK6uCwAAoNyUKQAtX75ccXFxGjlypKvrAQAAKHdl+hyg3Nxc9ejRw9W1AAAAVIgyBaCxY8dq1apVrq4FAACgQpTpFtgPP/ygN998U9u3b9fPfvYzeXl5Oe1/7bXXXFIcAABAeShTADpy5Ijat28vSfr666+d9vFANAAA8HRlCkA7d+50dR0AAAAVpkzPAAEAAFRmZboC1Ldv3yJvdf3tb38rc0EAAADlrUwB6ObzPzddu3ZNSUlJ+vrrr/N9SSoAAICnKVMAWrRoUYHb586dq0uXLt1WQQAAAOXNpc8APfnkk3wPGAAA8HguDUB79+5VjRo1XDkkAACAy5XpFtjgwYOd1o0xSk9P14EDBzR79myXFAYAAFBeyhSA/Pz8nNarVKmiFi1aaP78+YqMjHRJYQAAAOWlTAFo5cqVrq4DAACgwtzWM0AHDx7U+++/rw8++ECHDx8udf9du3bpoYceUnBwsGw2mzZs2OC03xijuXPnKjg4WN7e3oqIiNDRo0eLHXft2rVq3bq17Ha7WrdurfXr15e6NgAAcOcqUwA6e/as7rvvPnXu3FmTJ0/WxIkT1bFjR/Xr10//+c9/SjzO5cuXFR4erqVLlxa4/+WXX9Zrr72mpUuXav/+/QoMDFT//v118eLFQsfcu3evhg0bppEjR+rLL7/UyJEjNXToUO3bt6/UxwkAAO5MZQpAkyZNUnZ2to4eParvv/9e58+f19dff63s7GxNnjy5xONERUXpd7/7Xb6HqqUfr/4sXrxYs2bN0uDBg9W2bVu98847unLlilatWlXomIsXL1b//v0VExOjli1bKiYmRv369dPixYvLcqgAAOAOVKYAtHXrVr3xxhtq1aqVY1vr1q21bNky/fWvf3VJYSkpKcrIyHB6qNput6tPnz7as2dPof327t2b70HsAQMGFNkHAABYS5kegs7Ly5OXl1e+7V5eXsrLy7vtoiQpIyNDkhQQEOC0PSAgQKdPny6yX0F9bo5XkJycHOXk5DjWs7Ozy1IyAACoJMp0Bei+++7TlClT9N133zm2nTlzRlOnTlW/fv1cVpykfF+6aowp8otYy9InNjZWfn5+jqVhw4ZlLxgAAHi8MgWgpUuX6uLFiwoLC1OTJk3UtGlTNWrUSBcvXtTrr7/uksICAwMlKd+Vm7Nnz+a7wvPTfqXtExMTo6ysLMeSlpZ2G5UDAABPV6ZbYA0bNtShQ4cUHx+v48ePyxij1q1b6xe/+IXLCmvUqJECAwMVHx+vDh06SJJyc3OVmJiohQsXFtqve/fuio+P19SpUx3btm3bph49ehTax263y263u6x2AADg2UoVgP72t79p4sSJ+uKLL+Tr66v+/furf//+kqSsrCy1adNGy5cvV69evUo03qVLl3Ty5EnHekpKipKSkuTv76+QkBA999xzeumll9SsWTM1a9ZML730ku666y49/vjjjj6jRo1S/fr1FRsbK0maMmWKevfurYULF2rQoEHauHGjtm/frt27d5fmUAEAwB2sVAFo8eLFGjdunHx9ffPt8/Pz0zPPPKPXXnutxAHowIED6tu3r2N92rRpkqTo6GjFxcVpxowZunr1qp599lmdP39eXbt21bZt2+Tj4+Pok5qaqipV/nsnr0ePHlq9erVefPFFzZ49W02aNNGaNWvUtWvX0hwqAAC4g9mMMaakjUNDQ7V161ant7/f6vjx44qMjFRqaqrLCnSH7Oxs+fn5KSsrq8CwBwCApzp06JA6duyo/rNWyj+khbvLKdD3qScU/3+/0sGDB3Xvvfe6bNzS/P4u1UPQ//73vwt8+/tN1apVK9UnQQMAALhDqQJQ/fr19dVXXxW6/8iRIwoKCrrtogAAAMpTqQLQwIED9T//8z/64Ycf8u27evWq5syZowcffNBlxQEAAJSHUj0E/eKLL2rdunVq3ry5Jk6cqBYtWshms+nYsWNatmyZbty4oVmzZpVXrQAAAC5RqgAUEBCgPXv26Ne//rViYmJ08/lpm82mAQMG6I9//GORHzgIAADgCUr9QYihoaHasmWLzp8/r5MnT8oYo2bNmunuu+8uj/oAAABcrkyfBC1Jd999tzp37uzKWgAAACpEmb4LDAAAoDIjAAEAAMshAAEAAMshAAEAAMshAAEAAMshAAEAAMshAAEAAMshAAEAAMshAAEAAMshAAEAAMshAAEAAMshAAEAAMshAAEAAMshAAEAAMshAAEAAMshAAEAAMshAAEAAMshAAEAAMshAAEAAMshAAEAAMshAAEAAMshAAEAAMshAAEAAMshAAEAAMshAAEAAMshAAEAAMvx+AAUFhYmm82Wb5kwYUKB7RMSEgpsf/z48QquHAAAeKpq7i6gOPv379eNGzcc619//bX69++vxx57rMh+J06ckK+vr2P9nnvuKbcaAQBA5eLxAeinwWXBggVq0qSJ+vTpU2S/evXqqXbt2uVYGQAAqKw8/hbYrXJzc/X+++9rzJgxstlsRbbt0KGDgoKC1K9fP+3cubOCKgQAAJWBx18ButWGDRt04cIFjR49utA2QUFBevPNN9WxY0fl5OTovffeU79+/ZSQkKDevXsX2CcnJ0c5OTmO9ezsbFeXDgAAPEilCkBvv/22oqKiFBwcXGibFi1aqEWLFo717t27Ky0tTa+88kqhASg2Nlbz5s1zeb0AAMAzVZpbYKdPn9b27ds1duzYUvft1q2bkpOTC90fExOjrKwsx5KWlnY7pQIAAA9Xaa4ArVy5UvXq1dMDDzxQ6r6HDx9WUFBQofvtdrvsdvvtlAcAACqRShGA8vLytHLlSkVHR6taNeeSY2JidObMGb377ruSpMWLFyssLExt2rRxPDS9du1arV271h2lAwAAD1QpAtD27duVmpqqMWPG5NuXnp6u1NRUx3pubq6mT5+uM2fOyNvbW23atNHmzZs1cODAiiwZAAB4sEoRgCIjI2WMKXBfXFyc0/qMGTM0Y8aMCqgKAABUVpXmIWgAAABXIQABAADLIQABAADLIQABAADLIQABAADLIQABAADLIQABAADLIQABAADLIQABAADLIQABAADLIQABAADLIQABAADLIQABAADLIQABAADLIQABAADLIQABAADLIQABAADLIQABAADLIQABAADLIQABAADLIQABAADLIQABAADLIQABAADLIQABAADLIQABAADLIQABAADLIQABAADLIQABAADLIQABAADLIQABAADLIQABAADLIQABAADLIQABAADL8egANHfuXNlsNqclMDCwyD6JiYnq2LGjatSoocaNG2v58uUVVC0AAKgsqrm7gOK0adNG27dvd6xXrVq10LYpKSkaOHCgxo0bp/fff1+ff/65nn32Wd1zzz169NFHK6JcAABQCXh8AKpWrVqxV31uWr58uUJCQrR48WJJUqtWrXTgwAG98sorBCAAAODg0bfAJCk5OVnBwcFq1KiRhg8frm+//bbQtnv37lVkZKTTtgEDBujAgQO6du1aeZcKAAAqCY8OQF27dtW7776rTz/9VH/605+UkZGhHj166Ny5cwW2z8jIUEBAgNO2gIAAXb9+XZmZmYW+Tk5OjrKzs50WAABw5/LoABQVFaVHH31U7dq10y9+8Qtt3rxZkvTOO+8U2sdmszmtG2MK3H6r2NhY+fn5OZaGDRu6oHoAAOCpPDoA/VTNmjXVrl07JScnF7g/MDBQGRkZTtvOnj2ratWqqU6dOoWOGxMTo6ysLMeSlpbm0roBAIBn8fiHoG+Vk5OjY8eOqVevXgXu7969uz7++GOnbdu2bVOnTp3k5eVV6Lh2u112u92ltQIAAM/l0VeApk+frsTERKWkpGjfvn0aMmSIsrOzFR0dLenHKzejRo1ytB8/frxOnz6tadOm6dixY1qxYoXefvttTZ8+3V2HAAAAPJBHXwH617/+pREjRigzM1P33HOPunXrpi+++EKhoaGSpPT0dKWmpjraN2rUSFu2bNHUqVO1bNkyBQcHa8mSJbwFHgAAOPHoALR69eoi98fFxeXb1qdPHx06dKicKgIAAHcCj74FBgAAUB4IQAAAwHIIQAAAwHIIQAAAwHIIQAAAwHIIQAAAwHIIQAAAwHIIQAAAwHIIQAAAwHIIQAAAwHIIQAAAwHIIQAAAwHIIQAAAwHIIQAAAwHIIQAAAwHIIQAAAwHIIQAAAwHIIQAAAwHIIQAAAwHIIQAAAwHIIQAAAwHIIQAAAwHIIQAAAwHIIQAAAwHIIQAAAwHIIQAAAwHIIQAAAwHIIQAAAwHIIQAAAwHIIQAAAwHIIQAAAwHIIQAAAwHIIQAAAwHI8OgDFxsaqc+fO8vHxUb169fTII4/oxIkTRfZJSEiQzWbLtxw/fryCqgYAAJ7OowNQYmKiJkyYoC+++ELx8fG6fv26IiMjdfny5WL7njhxQunp6Y6lWbNmFVAxAACoDKq5u4CibN261Wl95cqVqlevng4ePKjevXsX2bdevXqqXbt2OVYHAAAqK4++AvRTWVlZkiR/f/9i23bo0EFBQUHq16+fdu7cWd6lAQCASsSjrwDdyhijadOmqWfPnmrbtm2h7YKCgvTmm2+qY8eOysnJ0Xvvvad+/fopISGh0KtGOTk5ysnJcaxnZ2e7vH4AAOA5Kk0Amjhxoo4cOaLdu3cX2a5FixZq0aKFY7179+5KS0vTK6+8UmgAio2N1bx581xaLwAA8FyV4hbYpEmTtGnTJu3cuVMNGjQodf9u3bopOTm50P0xMTHKyspyLGlpabdTLgAA8HAefQXIGKNJkyZp/fr1SkhIUKNGjco0zuHDhxUUFFTofrvdLrvdXtYyAQBAJePRAWjChAlatWqVNm7cKB8fH2VkZEiS/Pz85O3tLenHqzdnzpzRu+++K0lavHixwsLC1KZNG+Xm5ur999/X2rVrtXbtWrcdBwAA8CweHYDeeOMNSVJERITT9pUrV2r06NGSpPT0dKWmpjr25ebmavr06Tpz5oy8vb3Vpk0bbd68WQMHDqyosgEAgIfz6ABkjCm2TVxcnNP6jBkzNGPGjHKqCAAA3AkqxUPQAAAAruTRV4CAoqSmpiozM9PdZRSpbt26CgkJcXcZRWIeAVgRAQiVUmpqqlq2bKWrV6+4u5QieXvfpePHj3nsL2/mEYBVEYBQKWVmZurq1SvqOmaOfIPC3F1OgbLTT2nfinnKzMz02F/czCMAqyIAoVLzDQqTf0iL4huiSMwjAKvhIWgAAGA5BCAAAGA5BCAAAGA5BCAAAGA5BCAAAGA5BCAAAGA5BCAAAGA5BCAAAGA5BCAAAGA5BCAAAGA5BCAAAGA5BCAAAGA5BCAAAGA5BCAAAGA51dxdgBWlpqYqMzPT3WUUqW7dugoJCXF3GQBcqDL87MnJyZHdbnd3GUXi5+OdgQBUwVJTU9WyZStdvXrF3aUUydv7Lh0/fox/5MAdorL87JHNJhnj7iqKxM/HOwMBqIJlZmbq6tUr6jpmjnyDwtxdToGy009p34p5yszM5B84cIeoDD970r/aq683van2j8/UPY1aurucAvHz8c5BAHIT36Aw+Ye0cHcZACzGk3/2ZKefkiTVqhfisTXizsFD0AAAwHIIQAAAwHIIQAAAwHIIQAAAwHIIQAAAwHIIQAAAwHIIQAAAwHIIQAAAwHIIQAAAwHIIQAAAwHIqRQD64x//qEaNGqlGjRrq2LGjPvvssyLbJyYmqmPHjqpRo4YaN26s5cuXV1ClAACgMvD4ALRmzRo999xzmjVrlg4fPqxevXopKipKqampBbZPSUnRwIED1atXLx0+fFi//e1vNXnyZK1du7aCKwcAAJ7K4wPQa6+9pqeeekpjx45Vq1attHjxYjVs2FBvvPFGge2XL1+ukJAQLV68WK1atdLYsWM1ZswYvfLKKxVcOQAA8FQeHYByc3N18OBBRUZGOm2PjIzUnj17Cuyzd+/efO0HDBigAwcO6Nq1a+VWKwAAqDyqubuAomRmZurGjRsKCAhw2h4QEKCMjIwC+2RkZBTY/vr168rMzFRQUFC+Pjk5OcrJyXGsZ2VlSZKys7Nv9xDyuXTpkiTp+9MndD3nqsvHd4XsjB9vLx48eNBRr6c5ceKEJObxdjGPrlOlShXl5eW5u4xCVYq/6/TTkqSsM8nyqmZzczUFqwznY6X4u/7/83jp0iWX/q69OZYxpvjGxoOdOXPGSDJ79uxx2v673/3OtGjRosA+zZo1My+99JLTtt27dxtJJj09vcA+c+bMMZJYWFhYWFhY7oAlLS2t2Izh0VeA6tatq6pVq+a72nP27Nl8V3luCgwMLLB9tWrVVKdOnQL7xMTEaNq0aY71vLw8ff/996pTp45sNtf+LyQ7O1sNGzZUWlqafH19XTo2/ot5rhjMc8VgnisOc10xymuejTG6ePGigoODi23r0QGoevXq6tixo+Lj4/XLX/7SsT0+Pl6DBg0qsE/37t318ccfO23btm2bOnXqJC8vrwL72O122e12p221a9e+veKL4evryz+uCsA8VwzmuWIwzxWHua4Y5THPfn5+JWrn0Q9BS9K0adP01ltvacWKFTp27JimTp2q1NRUjR8/XtKPV29GjRrlaD9+/HidPn1a06ZN07Fjx7RixQq9/fbbmj59ursOAQAAeBiPvgIkScOGDdO5c+c0f/58paenq23bttqyZYtCQ0MlSenp6U6fCdSoUSNt2bJFU6dO1bJlyxQcHKwlS5bo0UcfddchAAAAD+PxAUiSnn32WT377LMF7ouLi8u3rU+fPjp06FA5V1U2drtdc+bMyXfLDa7FPFcM5rliMM8Vh7muGJ4wzzZjSvJeMQAAgDuHxz8DBAAA4GoEIAAAYDkEIAAAYDkEoHIQFhYmm82Wb5kwYYIkafTo0fn2devWzc1VVz7Xr1/Xiy++qEaNGsnb21uNGzfW/Pnznb6OwBijuXPnKjg4WN7e3oqIiNDRo0fdWHXlU5J55px2jYsXL+q5555TaGiovL291aNHD+3fv9+xn/PZdYqba87p0tu1a5ceeughBQcHy2azacOGDU77S3L+5uTkaNKkSapbt65q1qyphx9+WP/617/Kp+BiPysapXb27FmTnp7uWOLj440ks3PnTmOMMdHR0eb+++93anPu3Dn3Fl0J/e53vzN16tQxn3zyiUlJSTF/+ctfTK1atczixYsdbRYsWGB8fHzM2rVrzVdffWWGDRtmgoKCTHZ2thsrr1xKMs+c064xdOhQ07p1a5OYmGiSk5PNnDlzjK+vr/nXv/5ljOF8dqXi5ppzuvS2bNliZs2aZdauXWskmfXr1zvtL8n5O378eFO/fn0THx9vDh06ZPr27WvCw8PN9evXXV4vAagCTJkyxTRp0sTk5eUZY378hzVo0CD3FnUHeOCBB8yYMWOctg0ePNg8+eSTxhhj8vLyTGBgoFmwYIFj/w8//GD8/PzM8uXLK7TWyqy4eTaGc9oVrly5YqpWrWo++eQTp+3h4eFm1qxZnM8uVNxcG8M5fbt+GoBKcv5euHDBeHl5mdWrVzvanDlzxlSpUsVs3brV5TVyC6yc5ebm6v3339eYMWOcvlcsISFB9erVU/PmzTVu3DidPXvWjVVWTj179tSOHTv0z3/+U5L05Zdfavfu3Ro4cKAkKSUlRRkZGYqMjHT0sdvt6tOnj/bs2eOWmiuj4ub5Js7p23P9+nXduHFDNWrUcNru7e2t3bt3cz67UHFzfRPntOuU5Pw9ePCgrl275tQmODhYbdu2LZdzvFJ8EGJltmHDBl24cEGjR492bIuKitJjjz2m0NBQpaSkaPbs2brvvvt08OBBPnyrFGbOnKmsrCy1bNlSVatW1Y0bN/R///d/GjFihCQ5vhT3p1+cGxAQoNOnT1d4vZVVcfMscU67go+Pj7p3767//d//VatWrRQQEKAPP/xQ+/btU7NmzTifXai4uZY4p12tJOdvRkaGqlevrrvvvjtfm59+ybkrEIDK2dtvv62oqCinb6YdNmyY489t27ZVp06dFBoaqs2bN2vw4MHuKLNSWrNmjd5//32tWrVKbdq0UVJSkp577jkFBwcrOjra0e7WK2/Sjw/i/XQbCleSeeacdo333ntPY8aMUf369VW1alXde++9evzxx50+2Z7z2TWKm2vO6fJRlvO3vM5xboGVo9OnT2v79u0aO3Zske2CgoIUGhqq5OTkCqrszvD888/rhRde0PDhw9WuXTuNHDlSU6dOVWxsrCQpMDBQkvL9z+Hs2bP5/heCwhU3zwXhnC6bJk2aKDExUZcuXVJaWpr+/ve/69q1a2rUqBHns4sVNdcF4Zy+PSU5fwMDA5Wbm6vz588X2saVCEDlaOXKlapXr54eeOCBItudO3dOaWlpCgoKqqDK7gxXrlxRlSrOp3DVqlUdb8+++UsjPj7esT83N1eJiYnq0aNHhdZamRU3zwXhnL49NWvWVFBQkM6fP69PP/1UgwYN4nwuJwXNdUE4p29PSc7fjh07ysvLy6lNenq6vv766/I5x13+WDWMMcbcuHHDhISEmJkzZzptv3jxovnNb35j9uzZY1JSUszOnTtN9+7dTf369XkraylFR0eb+vXrO96evW7dOlO3bl0zY8YMR5sFCxYYPz8/s27dOvPVV1+ZESNG8LbhUipunjmnXWfr1q3mr3/9q/n222/Ntm3bTHh4uOnSpYvJzc01xnA+u1JRc805XTYXL140hw8fNocPHzaSzGuvvWYOHz5sTp8+bYwp2fk7fvx406BBA7N9+3Zz6NAhc9999/E2+Mrm008/NZLMiRMnnLZfuXLFREZGmnvuucd4eXmZkJAQEx0dbVJTU91UaeWVnZ1tpkyZYkJCQkyNGjVM48aNzaxZs0xOTo6jTV5enpkzZ44JDAw0drvd9O7d23z11VdurLryKW6eOaddZ82aNaZx48amevXqJjAw0EyYMMFcuHDBsZ/z2XWKmmvO6bLZuXOnkZRviY6ONsaU7Py9evWqmThxovH39zfe3t7mwQcfLLd559vgAQCA5fAMEAAAsBwCEAAAsBwCEAAAsBwCEAAAsBwCEAAAsBwCEAAAsBwCEAAAsBwCEAAAsBwCEACPlZCQIJvNpgsXLlT4a8+dO1ft27cvss2pU6dks9mUlJRUITUV5Kd1jh49Wo888kiRfSIiIvTcc8+Va12ApyMAAcjHZrNpw4YN7i7DraZPn64dO3Y41ksSLDzBH/7wB8XFxblsvLCwMC1evNhl4wGeopq7CwBQMjdu3JDNZsv3zewoH7Vq1VKtWrXcXUap+fn5ubsEoFLgJylQTiIiIjRx4kRNnDhRtWvXVp06dfTiiy/q5tfv5ebmasaMGapfv75q1qyprl27KiEhwdE/Li5OtWvX1ieffKLWrVvLbrfr9OnTysnJ0YwZM9SwYUPZ7XY1a9ZMb7/9tqPfP/7xDw0cOFC1atVSQECARo4cqczMTKe6Jk+erBkzZsjf31+BgYGaO3euY39YWJgk6Ze//KVsNptjvSg3b8O89957CgsLk5+fn4YPH66LFy862uTk5Gjy5MmqV6+eatSooZ49e2r//v1O42zZskXNmzeXt7e3+vbtq1OnTuV7rT179qh3797y9vZWw4YNNXnyZF2+fLnYGl9//XW1a9fOsb5hwwbZbDYtW7bMsW3AgAGKiYlxOqabf37nnXe0ceNG2Ww22Ww2p7+rb7/9Vn379tVdd92l8PBw7d27t9h6srKy5O3tra1btzptX7dunWrWrKlLly5JkmbOnKnmzZvrrrvuUuPGjTV79mxdu3at0HF/eqXq8uXLGjVqlGrVqqWgoCC9+uqrxdZ2U0REhE6fPq2pU6c6jvvy5cvy9fXVRx995NT2448/Vs2aNXXx4kXHrcHVq1erR48eqlGjhtq0aeM0Z1Lx5ypQnghAQDl65513VK1aNe3bt09LlizRokWL9NZbb0mSfvWrX+nzzz/X6tWrdeTIET322GO6//77lZyc7Oh/5coVxcbG6q233tLRo0dVr149jRo1SqtXr9aSJUt07NgxLV++3HGlIj09XX369FH79u114MABbd26Vf/+9781dOjQfHXVrFlT+/bt08svv6z58+crPj5ekhyhZOXKlUpPT88XUgrzzTffaMOGDfrkk0/0ySefKDExUQsWLHDsnzFjhtauXat33nlHhw4dUtOmTTVgwAB9//33kqS0tDQNHjxYAwcOVFJSksaOHasXXnjB6TW++uorDRgwQIMHD9aRI0e0Zs0a7d69WxMnTiy2voiICB09etTxCzYxMVF169ZVYmKiJOn69evas2eP+vTpk6/v9OnTNXToUN1///1KT09Xenq6evTo4dg/a9YsTZ8+XUlJSWrevLlGjBih69evF1mPn5+fHnjgAX3wwQdO21etWqVBgwY5/k59fHwUFxenf/zjH/rDH/6gP/3pT1q0aFGxx3vT888/r507d2r9+vXatm2bEhISdPDgwRL1XbdunRo0aKD58+c7jrtmzZoaPny4Vq5c6dR25cqVGjJkiHx8fJxe+ze/+Y0OHz6sHj166OGHH9a5c+cklfxcBcpNuXzHPADTp08f06pVK5OXl+fYNnPmTNOqVStz8uRJY7PZzJkzZ5z69OvXz8TExBhjjFm5cqWRZJKSkhz7T5w4YSSZ+Pj4Al9z9uzZJjIy0mlbWlqakWROnDjhqKtnz55ObTp37mxmzpzpWJdk1q9fX+JjnTNnjrnrrrtMdna2Y9vzzz9vunbtaowx5tKlS8bLy8t88MEHjv25ubkmODjYvPzyy8YYY2JiYgqcL0nm/PnzxhhjRo4caZ5++mmn1/7ss89MlSpVzNWrV4usMS8vz9StW9d89NFHxhhj2rdvb2JjY029evWMMcbs2bPHVKtWzVy8eNFxTOHh4Y7+0dHRZtCgQU5jpqSkGEnmrbfecmw7evSokWSOHTtWZD3GGLNu3TpTq1Ytc/nyZWOMMVlZWaZGjRpm8+bNhfZ5+eWXTceOHR3rRdV58eJFU716dbN69WrH/nPnzhlvb28zZcqUYuszxpjQ0FCzaNEip2379u0zVatWdZy///nPf4yXl5dJSEgwxvx3XhYsWODoc+3aNdOgQQOzcOFCY0zJzlWgPHEFCChH3bp1k81mc6x3795dycnJOnDggIwxat68ueNZk1q1aikxMVHffPONo3316tX1s5/9zLGelJSkqlWrFniVQpIOHjyonTt3Oo3ZsmVLSXIa99YxJSkoKEhnz569rWMNCwtz+t//rWN+8803unbtmn7+85879nt5ealLly46duyYJOnYsWMFztdPjy8uLs7p+AYMGKC8vDylpKQUWZ/NZlPv3r2VkJCgCxcu6OjRoxo/frxu3LihY8eOKSEhQffee2+Znvu5dT6DgoIkqUTz+cADD6hatWratGmTJGnt2rXy8fFRZGSko81HH32knj17KjAwULVq1dLs2bOVmppaorq++eYb5ebmOs2jv7+/WrRoUaL+henSpYvatGmjd999V5L03nvvKSQkRL1793Zqd+vrVqtWTZ06dXL8fZf0XAXKCw9BA25StWpVHTx4UFWrVnXafusvYG9vb6dA4O3tXeSYeXl5euihh7Rw4cJ8+27+YpZ+DB+3stlsysvLK1X9P1XUmOb/P/d067Hc3H5z2802RcnLy9MzzzyjyZMn59sXEhJSbP+IiAi9+eab+uyzzxQeHq7atWurd+/eSkxMVEJCgiIiIoodoyC3HvvN4ynJfFavXl1DhgzRqlWrNHz4cK1atUrDhg1TtWo//mj+4osvNHz4cM2bN08DBgyQn5+fVq9eXeLneEoyp2U1duxYLV26VC+88IJWrlypX/3qV/n+fgty6/yU5FwFygtXgIBy9MUXX+Rbb9asmTp06KAbN27o7Nmzatq0qdMSGBhY6Hjt2rVTXl6e47mVn7r33nt19OhRhYWF5Ru3Zs2aJa7by8tLN27cKHH74jRt2lTVq1fX7t27HduuXbumAwcOqFWrVpKk1q1bFzhft7p5fD89tpvjF+fmc0AfffSRI+z06dNH27dvL/T5n5uqV6/u0jm56YknntDWrVt19OhR7dy5U0888YRj3+eff67Q0FDNmjVLnTp1UrNmzXT69OkSj920aVN5eXk5zeP58+f1z3/+s8RjFHbcTz75pFJTU7VkyRIdPXpU0dHR+drc+rrXr1/XwYMHHVd5XHWuAmVFAALKUVpamqZNm6YTJ07oww8/1Ouvv64pU6aoefPmeuKJJzRq1CitW7dOKSkp2r9/vxYuXKgtW7YUOl5YWJiio6M1ZswYbdiwQSkpKUpISNCf//xnSdKECRP0/fffa8SIEfr73/+ub7/9Vtu2bdOYMWNK9cs7LCxMO3bsUEZGhs6fP3/b81CzZk39+te/1vPPP6+tW7fqH//4h8aNG6crV67oqaeekiSNHz9e33zzjWO+Vq1ale/zbGbOnKm9e/dqwoQJSkpKUnJysjZt2qRJkyaVqI62bduqTp06+uCDDxwBKCIiQhs2bNDVq1fVs2fPQvuGhYXpyJEjOnHihDIzM4t8J1Zp9OnTRwEBAXriiScUFhambt26OfY1bdpUqampWr16tb755hstWbJE69evL/HYtWrV0lNPPaXnn39eO3bs0Ndff63Ro0eX6qMUwsLCtGvXLp05c8bpHVp33323Bg8erOeff16RkZFq0KBBvr7Lli3T+vXrdfz4cU2YMEHnz5/XmDFjJLnuXAXKigAElKNRo0bp6tWr6tKliyZMmKBJkybp6aeflvTju2ZGjRql3/zmN2rRooUefvhh7du3Tw0bNixyzDfeeENDhgzRs88+q5YtW2rcuHGOt4EHBwfr888/140bNzRgwAC1bdtWU6ZMkZ+fX6l+6b366quKj49Xw4YN1aFDh7JPwC0WLFigRx99VCNHjtS9996rkydP6tNPP9Xdd98t6cdbWGvXrtXHH3+s8PBwLV++XC+99JLTGD/72c+UmJio5ORk9erVSx06dNDs2bNLfMvEZrM5rvL06tXLMaafn586dOggX1/fQvuOGzdOLVq0UKdOnXTPPffo888/L8s0FFjTiBEj9OWXXzpd/ZGkQYMGaerUqZo4caLat2+vPXv2aPbs2aUa//e//7169+6thx9+WL/4xS/Us2dPdezYscT958+fr1OnTqlJkya65557nPY99dRTys3NdYSan1qwYIEWLlyo8PBwffbZZ9q4caPq1q0ryXXnKlBWNlOeN4kBC4uIiFD79u35FF3csT744ANNmTJF3333ndMtyFOnTqlRo0Y6fPhwsV8nArgLD0EDAErlypUrSklJUWxsrJ555pkSPX8FeBquMwIoVps2bZzernzr8tMP8nOXzz77rNAa3fWVFlFRUYXW89Pbe+5Q1jl7+eWX1b59ewUEBDg+ORuobLgFBqBYp0+fLvSh34CAAKfP/3GXq1ev6syZM4Xub9q0aQVW86MzZ87o6tWrBe7z9/eXv79/BVfkzBPnDKgoBCAAAGA53AIDAACWQwACAACWQwACAACWQwACAACWQwACAACWQwACAACWQwACAACWQwACAACW8/8A3gai9QXxD0EAAAAASUVORK5CYII=", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_type\")"]}, {"cell_type": "markdown", "id": "8aa6499d", "metadata": {}, "source": ["# Base Model LLM Score"]}, {"cell_type": "code", "execution_count": null, "id": "5db373f1", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='llm_score', ylabel='Count'>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"llm_score\", bins=20)"]}, {"cell_type": "markdown", "id": "9813c3e6", "metadata": {}, "source": ["# SFT LLM Score"]}, {"cell_type": "code", "execution_count": null, "id": "81b670aa", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='llm_score', ylabel='Count'>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAjMAAAGxCAYAAACXwjeMAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjAsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvlHJYcgAAAAlwSFlzAAAPYQAAD2EBqD+naQAAJFlJREFUeJzt3XtwVPX9//HXEsImYBIIKbloQoIggXARCToKSqgQi0BVKlaQy4h2QK4hLUIEhaAQoSOmYxQbp6KtosxURVovEBG5FNQQjCKiFEVCEUyjNEsgLJCc3x9+2V9TEghxN+d81udjZmc45+yefXOU7HPOns26LMuyBAAAYKgWdg8AAADwYxAzAADAaMQMAAAwGjEDAACMRswAAACjETMAAMBoxAwAADAaMQMAAIzW0u4BAq22tlbffPONIiIi5HK57B4HAAA0gmVZOnbsmBISEtSixfnPvQR9zHzzzTdKTEy0ewwAANAEBw8e1GWXXXbe+wR9zEREREj64WBERkbaPA0AAGgMj8ejxMRE3+v4+QR9zJx9aykyMpKYAQDAMI25RIQLgAEAgNGIGQAAYDRiBgAAGI2YAQAARiNmAACA0YgZAABgNGIGAAAYjZgBAABGI2YAAIDRiBkAAGA0W2Nm8+bNGjFihBISEuRyubRmzZoG7ztp0iS5XC7l5+c323wAAMD5bI2Z48ePq3fv3iooKDjv/dasWaMPPvhACQkJzTQZAAAwha1fNDl06FANHTr0vPc5dOiQpk2bpnXr1mnYsGHNNBkAADCFo6+Zqa2t1bhx4zR79mylpaXZPQ4AAHAgW8/MXMjSpUvVsmVLzZgxo9GP8Xq98nq9vmWPxxOI0QAAMEpZWZkqKir8vt+YmBglJSX5fb8Xw7ExU1JSoj/84Q/auXOnXC5Xox+Xl5en3NzcAE4GAIBZysrKlJraTdXVJ/y+7/Dw1vr88z22Bo1jY2bLli0qLy+vc3Bqamr029/+Vvn5+fr666/rfVxOTo6ys7N9yx6PR4mJiYEeFwAAx6qoqFB19QldM3GBIuOT/bZfz+Gv9cGzuaqoqCBm6jNu3DgNHjy4zrqbbrpJ48aN0913393g49xut9xud6DHAwDAOJHxyYpO6mr3GH5na8xUVVVp3759vuX9+/ertLRU0dHRSkpKUvv27evcPzQ0VHFxceraNfj+QwAAgKaxNWZ27NihQYMG+ZbPvj00YcIEPffcczZNBQAATGJrzGRkZMiyrEbfv6HrZAAAwE+Xo3/PDAAAwIUQMwAAwGjEDAAAMBoxAwAAjEbMAAAAoxEzAADAaMQMAAAwGjEDAACMRswAAACjETMAAMBoxAwAADAaMQMAAIxGzAAAAKMRMwAAwGjEDAAAMBoxAwAAjEbMAAAAoxEzAADAaMQMAAAwGjEDAACMRswAAACjETMAAMBoxAwAADAaMQMAAIxGzAAAAKMRMwAAwGjEDAAAMBoxAwAAjEbMAAAAoxEzAADAaMQMAAAwGjEDAACMRswAAACjETMAAMBoxAwAADAaMQMAAIxGzAAAAKMRMwAAwGjEDAAAMJqtMbN582aNGDFCCQkJcrlcWrNmjW/b6dOnNWfOHPXs2VNt2rRRQkKCxo8fr2+++ca+gQEAgOPYGjPHjx9X7969VVBQcM62EydOaOfOnXrwwQe1c+dOvfrqq9q7d69++ctf2jApAABwqpZ2PvnQoUM1dOjQerdFRUWpqKiozronnnhCV199tcrKypSUlNQcIwIAAIcz6pqZyspKuVwutW3b1u5RAACAQ9h6ZuZinDx5UnPnztWYMWMUGRnZ4P28Xq+8Xq9v2ePxNMd4AADAJkacmTl9+rTuvPNO1dbW6qmnnjrvffPy8hQVFeW7JSYmNtOUAADADo6PmdOnT+uOO+7Q/v37VVRUdN6zMpKUk5OjyspK3+3gwYPNNCkAALCDo99mOhsy//znP7Vx40a1b9/+go9xu91yu93NMB0AAHACW2OmqqpK+/bt8y3v379fpaWlio6OVkJCgm6//Xbt3LlTf//731VTU6MjR45IkqKjo9WqVSu7xgYAAA5ia8zs2LFDgwYN8i1nZ2dLkiZMmKCFCxdq7dq1kqQrr7yyzuM2btyojIyM5hoTAAA4mK0xk5GRIcuyGtx+vm0AAACSARcAAwAAnA8xAwAAjEbMAAAAoxEzAADAaMQMAAAwGjEDAACMRswAAACjETMAAMBoxAwAADAaMQMAAIxGzAAAAKMRMwAAwGjEDAAAMBoxAwAAjEbMAAAAoxEzAADAaMQMAAAwGjEDAACMRswAAACjETMAAMBoxAwAADAaMQMAAIxGzAAAAKMRMwAAwGjEDAAAMBoxAwAAjEbMAAAAoxEzAADAaMQMAAAwGjEDAACMRswAAACjETMAAMBoxAwAADAaMQMAAIxGzAAAAKMRMwAAwGjEDAAAMBoxAwAAjEbMAAAAoxEzAADAaLbGzObNmzVixAglJCTI5XJpzZo1dbZblqWFCxcqISFB4eHhysjI0O7du+0ZFgAAOJKtMXP8+HH17t1bBQUF9W5ftmyZli9froKCAhUXFysuLk5DhgzRsWPHmnlSAADgVC3tfPKhQ4dq6NCh9W6zLEv5+fmaN2+eRo4cKUl6/vnnFRsbq1WrVmnSpEnNOSoAAHAox14zs3//fh05ckSZmZm+dW63WwMHDtS2bdsafJzX65XH46lzAwAAwcuxMXPkyBFJUmxsbJ31sbGxvm31ycvLU1RUlO+WmJgY0DkBAIC9HBszZ7lcrjrLlmWds+6/5eTkqLKy0nc7ePBgoEcEAAA2svWamfOJi4uT9MMZmvj4eN/68vLyc87W/De32y232x3w+QAAgDM49sxMSkqK4uLiVFRU5Ft36tQpbdq0Sdddd52NkwEAACex9cxMVVWV9u3b51vev3+/SktLFR0draSkJGVlZWnJkiXq0qWLunTpoiVLlqh169YaM2aMjVMDAAAnsTVmduzYoUGDBvmWs7OzJUkTJkzQc889p/vvv1/V1dWaMmWKjh49qmuuuUbr169XRESEXSMDAACHsTVmMjIyZFlWg9tdLpcWLlyohQsXNt9QAADAKI69ZgYAAKAxiBkAAGA0YgYAABiNmAEAAEYjZgAAgNGIGQAAYDRiBgAAGI2YAQAARiNmAACA0YgZAABgNGIGAAAYjZgBAABGI2YAAIDRiBkAAGA0YgYAABiNmAEAAEYjZgAAgNGIGQAAYDRiBgAAGI2YAQAARiNmAACA0YgZAABgNGIGAAAYjZgBAABGI2YAAIDRiBkAAGA0YgYAABiNmAEAAEYjZgAAgNGIGQAAYDRiBgAAGI2YAQAARiNmAACA0YgZAABgNGIGAAAYjZgBAABGI2YAAIDRiBkAAGA0YgYAABjN0TFz5swZzZ8/XykpKQoPD1enTp20aNEi1dbW2j0aAABwiJZ2D3A+S5cu1dNPP63nn39eaWlp2rFjh+6++25FRUVp5syZdo8HAAAcoElnZjp16qTvvvvunPX/+c9/1KlTpx891Fnbt2/XLbfcomHDhik5OVm33367MjMztWPHDr89BwAAMFuTYubrr79WTU3NOeu9Xq8OHTr0o4c6a8CAAdqwYYP27t0rSfr444+1detW3XzzzQ0+xuv1yuPx1LkBAIDgdVFvM61du9b353Xr1ikqKsq3XFNTow0bNig5Odlvw82ZM0eVlZVKTU1VSEiIampqtHjxYo0ePbrBx+Tl5Sk3N9dvMwAAAGe7qJi59dZbJUkul0sTJkyosy00NFTJycl67LHH/Dbc6tWr9cILL2jVqlVKS0tTaWmpsrKylJCQcM7zn5WTk6Ps7GzfssfjUWJiot9mAgAAznJRMXP2U0QpKSkqLi5WTExMQIY6a/bs2Zo7d67uvPNOSVLPnj114MAB5eXlNRgzbrdbbrc7oHMBAADnaNKnmfbv3+/vOep14sQJtWhR97KekJAQPpoNAAB8mvzR7A0bNmjDhg0qLy8/Jy6effbZHz2YJI0YMUKLFy9WUlKS0tLS9NFHH2n58uWaOHGiX/YPAADM16SYyc3N1aJFi5Senq74+Hi5XC5/zyVJeuKJJ/Tggw9qypQpKi8vV0JCgiZNmqSHHnooIM8HAADM06SYefrpp/Xcc89p3Lhx/p6njoiICOXn5ys/Pz+gzwMAAMzVpN8zc+rUKV133XX+ngUAAOCiNSlm7r33Xq1atcrfswAAAFy0Jr3NdPLkSRUWFuqdd95Rr169FBoaWmf78uXL/TIcAADAhTQpZj755BNdeeWVkqRPP/20zrZAXQwMAABQnybFzMaNG/09BwAAQJM06ZoZAAAAp2jSmZlBgwad9+2kd999t8kDAQAAXIwmxczZ62XOOn36tEpLS/Xpp582+J1JAAAAgdCkmHn88cfrXb9w4UJVVVX9qIEAAAAuhl+vmRk7dqzfvpcJAACgMfwaM9u3b1dYWJg/dwkAAHBeTXqbaeTIkXWWLcvS4cOHtWPHDj344IN+GQwAAKAxmhQzUVFRdZZbtGihrl27atGiRcrMzPTLYAAAAI3RpJhZuXKlv+cAAABokibFzFklJSXas2ePXC6Xunfvrj59+vhrLgAAgEZpUsyUl5frzjvv1Hvvvae2bdvKsixVVlZq0KBBevnll/Wzn/3M33MCAADUq0mfZpo+fbo8Ho92796t77//XkePHtWnn34qj8ejGTNm+HtGAACABjXpzMzbb7+td955R926dfOt6969u5588kkuAAYAAM2qSWdmamtrFRoaes760NBQ1dbW/uihAAAAGqtJMfPzn/9cM2fO1DfffONbd+jQIc2aNUs33nij34YDAAC4kCbFTEFBgY4dO6bk5GRdfvnl6ty5s1JSUnTs2DE98cQT/p4RAACgQU26ZiYxMVE7d+5UUVGRPv/8c1mWpe7du2vw4MH+ng8AAOC8LurMzLvvvqvu3bvL4/FIkoYMGaLp06drxowZ6tevn9LS0rRly5aADAoAAFCfi4qZ/Px8/eY3v1FkZOQ526KiojRp0iQtX77cb8MBAABcyEXFzMcff6xf/OIXDW7PzMxUSUnJjx4KAACgsS4qZr799tt6P5J9VsuWLfXvf//7Rw8FAADQWBcVM5deeql27drV4PZPPvlE8fHxP3ooAACAxrqomLn55pv10EMP6eTJk+dsq66u1oIFCzR8+HC/DQcAAHAhF/XR7Pnz5+vVV1/VFVdcoWnTpqlr165yuVzas2ePnnzySdXU1GjevHmBmhUAAOAcFxUzsbGx2rZtm+677z7l5OTIsixJksvl0k033aSnnnpKsbGxARkUAACgPhf9S/M6duyoN998U0ePHtW+fftkWZa6dOmidu3aBWI+AACA82rSbwCWpHbt2qlfv37+nAUAAOCiNem7mQAAAJyCmAEAAEYjZgAAgNGIGQAAYDRiBgAAGI2YAQAARiNmAACA0RwfM4cOHdLYsWPVvn17tW7dWldeeaVKSkrsHgsAADhEk39pXnM4evSo+vfvr0GDBumtt95Shw4d9OWXX6pt27Z2jwYAABzC0TGzdOlSJSYmauXKlb51ycnJ9g0EAAAcx9FvM61du1bp6ekaNWqUOnTooD59+uiZZ56xeywAAOAgjo6Zr776SitWrFCXLl20bt06TZ48WTNmzNCf//znBh/j9Xrl8Xjq3AAAQPBy9NtMtbW1Sk9P15IlSyRJffr00e7du7VixQqNHz++3sfk5eUpNze3OccEAAA2cvSZmfj4eHXv3r3Oum7duqmsrKzBx+Tk5KiystJ3O3jwYKDHBAAANnL0mZn+/fvriy++qLNu79696tixY4OPcbvdcrvdgR4NAAA4hKPPzMyaNUvvv/++lixZon379mnVqlUqLCzU1KlT7R4NAAA4hKNjpl+/fnrttdf00ksvqUePHnr44YeVn5+vu+66y+7RAACAQzj6bSZJGj58uIYPH273GAAAwKEcfWYGAADgQogZAABgNGIGAAAYjZgBAABGI2YAAIDRiBkAAGA0YgYAABiNmAEAAEYjZgAAgNGIGQAAYDRiBgAAGI2YAQAARiNmAACA0YgZAABgNGIGAAAYjZgBAABGI2YAAIDRiBkAAGA0YgYAABiNmAEAAEYjZgAAgNGIGQAAYDRiBgAAGI2YAQAARiNmAACA0YgZAABgNGIGAAAYjZgBAABGI2YAAIDRiBkAAGA0YgYAABiNmAEAAEYjZgAAgNGIGQAAYDRiBgAAGI2YAQAARiNmAACA0YgZAABgNGIGAAAYjZgBAABGMypm8vLy5HK5lJWVZfcoAADAIYyJmeLiYhUWFqpXr152jwIAABzEiJipqqrSXXfdpWeeeUbt2rWzexwAAOAgLe0eoDGmTp2qYcOGafDgwXrkkUfOe1+v1yuv1+tb9ng8gR4PCBplZWWqqKjw+35jYmKUlJTk9/0CgGRAzLz88svauXOniouLG3X/vLw85ebmBngqIPiUlZUpNbWbqqtP+H3f4eGt9fnnewgaAAHh6Jg5ePCgZs6cqfXr1yssLKxRj8nJyVF2drZv2ePxKDExMVAjAkGjoqJC1dUndM3EBYqMT/bbfj2Hv9YHz+aqoqKCmAEQEI6OmZKSEpWXl6tv376+dTU1Ndq8ebMKCgrk9XoVEhJS5zFut1tut7u5RwWCRmR8sqKTuto9BgA0mqNj5sYbb9SuXbvqrLv77ruVmpqqOXPmnBMyAADgp8fRMRMREaEePXrUWdemTRu1b9/+nPUAAOCnyYiPZgMAADTE0Wdm6vPee+/ZPQIAAHAQzswAAACjETMAAMBoxAwAADAaMQMAAIxGzAAAAKMRMwAAwGjEDAAAMBoxAwAAjEbMAAAAoxEzAADAaMQMAAAwGjEDAACMRswAAACjETMAAMBoxAwAADAaMQMAAIxGzAAAAKO1tHsAIFiVlZWpoqLC7/uNiYlRUlKS3/cLAKYiZoAAKCsrU2pqN1VXn/D7vsPDW+vzz/cQNADwf4gZIAAqKipUXX1C10xcoMj4ZL/t13P4a33wbK4qKiqIGQD4P8QMEECR8cmKTupq9xgAENS4ABgAABiNmAEAAEYjZgAAgNGIGQAAYDRiBgAAGI2YAQAARiNmAACA0YgZAABgNGIGAAAYjZgBAABGI2YAAIDRiBkAAGA0YgYAABiNmAEAAEYjZgAAgNGIGQAAYDRiBgAAGI2YAQAARnN0zOTl5alfv36KiIhQhw4ddOutt+qLL76weywAAOAgjo6ZTZs2aerUqXr//fdVVFSkM2fOKDMzU8ePH7d7NAAA4BAt7R7gfN5+++06yytXrlSHDh1UUlKiG264waapAACAkzg6Zv5XZWWlJCk6OrrB+3i9Xnm9Xt+yx+MJ6ExlZWWqqKjw+35jYmKUlJTk9/0CsF+gfm5IgfvZYeLMJgrUcd6zZ4/f9+kkxsSMZVnKzs7WgAED1KNHjwbvl5eXp9zc3GaZqaysTKmp3VRdfcLv+w4Pb63PP9/DP3AgyATy54YUmJ8dJs5sokAfZ0k67T0VsH3byZiYmTZtmj755BNt3br1vPfLyclRdna2b9nj8SgxMTEgM1VUVKi6+oSumbhAkfHJftuv5/DX+uDZXFVUVPzk/3EDwSZQPzekwP3sMHFmEwXyOB/etV2fri3UmTNn/LpfpzAiZqZPn661a9dq8+bNuuyyy857X7fbLbfb3UyT/SAyPlnRSV2b9TkBmM3EnxsmzmyiQBxnz+Gv/bo/p3F0zFiWpenTp+u1117Te++9p5SUFLtHAgAADuPomJk6dapWrVql119/XRERETpy5IgkKSoqSuHh4TZPBwAAnMDRv2dmxYoVqqysVEZGhuLj43231atX2z0aAABwCEefmbEsy+4RAACAwzn6zAwAAMCFEDMAAMBoxAwAADAaMQMAAIxGzAAAAKMRMwAAwGjEDAAAMBoxAwAAjEbMAAAAoxEzAADAaMQMAAAwGjEDAACMRswAAACjETMAAMBoxAwAADAaMQMAAIxGzAAAAKMRMwAAwGjEDAAAMBoxAwAAjEbMAAAAoxEzAADAaMQMAAAwGjEDAACMRswAAACjETMAAMBoxAwAADAaMQMAAIxGzAAAAKMRMwAAwGjEDAAAMBoxAwAAjEbMAAAAoxEzAADAaMQMAAAwGjEDAACMRswAAACjETMAAMBoxAwAADCaETHz1FNPKSUlRWFhYerbt6+2bNli90gAAMAhHB8zq1evVlZWlubNm6ePPvpI119/vYYOHaqysjK7RwMAAA7g+JhZvny57rnnHt17773q1q2b8vPzlZiYqBUrVtg9GgAAcABHx8ypU6dUUlKizMzMOuszMzO1bds2m6YCAABO0tLuAc6noqJCNTU1io2NrbM+NjZWR44cqfcxXq9XXq/Xt1xZWSlJ8ng8fp+vqqpKkvT9gS90xlvtt/16jvzwFlpJSYnvOfypRYsWqq2tNWa/gdx3oPb7xRdfSDLr/w0TZ5bM+/8uUMdZCtyxNnFmif83/pvn8AFJUuWhfyq0pct/+/2//35VVVV+f509uz/Lsi58Z8vBDh06ZEmytm3bVmf9I488YnXt2rXexyxYsMCSxI0bN27cuHELgtvBgwcv2AuOPjMTExOjkJCQc87ClJeXn3O25qycnBxlZ2f7lmtra/X999+rffv2crn8V6PSD9WYmJiogwcPKjIy0q/7xv/HcW4eHOfmwXFuHhzn5hHI42xZlo4dO6aEhIQL3tfRMdOqVSv17dtXRUVFuu2223zri4qKdMstt9T7GLfbLbfbXWdd27ZtAzmmIiMj+cfSDDjOzYPj3Dw4zs2D49w8AnWco6KiGnU/R8eMJGVnZ2vcuHFKT0/Xtddeq8LCQpWVlWny5Ml2jwYAABzA8THz61//Wt99950WLVqkw4cPq0ePHnrzzTfVsWNHu0cDAAAO4PiYkaQpU6ZoypQpdo9xDrfbrQULFpzzthb8i+PcPDjOzYPj3Dw4zs3DKcfZZVmN+cwTAACAMzn6l+YBAABcCDEDAACMRswAAACjETNN9NRTTyklJUVhYWHq27evtmzZYvdIQSUvL0/9+vVTRESEOnTooFtvvdX3q74ROHl5eXK5XMrKyrJ7lKB06NAhjR07Vu3bt1fr1q115ZVXqqSkxO6xgsqZM2c0f/58paSkKDw8XJ06ddKiRYsC9rUGPxWbN2/WiBEjlJCQIJfLpTVr1tTZblmWFi5cqISEBIWHhysjI0O7d+9utvmImSZYvXq1srKyNG/ePH300Ue6/vrrNXToUJWVldk9WtDYtGmTpk6dqvfff19FRUU6c+aMMjMzdfz4cbtHC1rFxcUqLCxUr1697B4lKB09elT9+/dXaGio3nrrLX322Wd67LHHAv5LPX9qli5dqqeffloFBQXas2ePli1bpt///vd64okn7B7NaMePH1fv3r1VUFBQ7/Zly5Zp+fLlKigoUHFxseLi4jRkyBAdO3aseQb80V+g9BN09dVXW5MnT66zLjU11Zo7d65NEwW/8vJyS5K1adMmu0cJSseOHbO6dOliFRUVWQMHDrRmzpxp90hBZ86cOdaAAQPsHiPoDRs2zJo4cWKddSNHjrTGjh1r00TBR5L12muv+ZZra2utuLg469FHH/WtO3nypBUVFWU9/fTTzTITZ2Yu0qlTp1RSUqLMzMw66zMzM7Vt2zabpgp+Z7/9PDo62uZJgtPUqVM1bNgwDR482O5RgtbatWuVnp6uUaNGqUOHDurTp4+eeeYZu8cKOgMGDNCGDRu0d+9eSdLHH3+srVu36uabb7Z5suC1f/9+HTlypM7rotvt1sCBA5vtddGIX5rnJBUVFaqpqTnniy5jY2PP+UJM+IdlWcrOztaAAQPUo0cPu8cJOi+//LJ27typ4uJiu0cJal999ZVWrFih7OxsPfDAA/rwww81Y8YMud1ujR8/3u7xgsacOXNUWVmp1NRUhYSEqKamRosXL9bo0aPtHi1onX3tq+918cCBA80yAzHTRP/7DdyWZfn9W7nxg2nTpumTTz7R1q1b7R4l6Bw8eFAzZ87U+vXrFRYWZvc4Qa22tlbp6elasmSJJKlPnz7avXu3VqxYQcz40erVq/XCCy9o1apVSktLU2lpqbKyspSQkKAJEybYPV5Qs/N1kZi5SDExMQoJCTnnLEx5efk5VYofb/r06Vq7dq02b96syy67zO5xgk5JSYnKy8vVt29f37qamhpt3rxZBQUF8nq9CgkJsXHC4BEfH6/u3bvXWdetWze98sorNk0UnGbPnq25c+fqzjvvlCT17NlTBw4cUF5eHjETIHFxcZJ+OEMTHx/vW9+cr4tcM3ORWrVqpb59+6qoqKjO+qKiIl133XU2TRV8LMvStGnT9Oqrr+rdd99VSkqK3SMFpRtvvFG7du1SaWmp75aenq677rpLpaWlhIwf9e/f/5xfL7B3716+NNfPTpw4oRYt6r60hYSE8NHsAEpJSVFcXFyd18VTp05p06ZNzfa6yJmZJsjOzta4ceOUnp6ua6+9VoWFhSorK9PkyZPtHi1oTJ06VatWrdLrr7+uiIgI35mwqKgohYeH2zxd8IiIiDjnOqQ2bdqoffv2XJ/kZ7NmzdJ1112nJUuW6I477tCHH36owsJCFRYW2j1aUBkxYoQWL16spKQkpaWl6aOPPtLy5cs1ceJEu0czWlVVlfbt2+db3r9/v0pLSxUdHa2kpCRlZWVpyZIl6tKli7p06aIlS5aodevWGjNmTPMM2CyfmQpCTz75pNWxY0erVatW1lVXXcVHhv1MUr23lStX2j1a0OOj2YHzt7/9zerRo4fldrut1NRUq7Cw0O6Rgo7H47FmzpxpJSUlWWFhYVanTp2sefPmWV6v1+7RjLZx48Z6fyZPmDDBsqwfPp69YMECKy4uznK73dYNN9xg7dq1q9nm41uzAQCA0bhmBgAAGI2YAQAARiNmAACA0YgZAABgNGIGAAAYjZgBAABGI2YAAIDRiBkAAGA0YgbAj5aRkaGsrCxJUnJysvLz822dB8BPCzEDAACMRswAQAMsy9KZM2fsHgPABRAzAALK5XLpj3/8o4YPH67WrVurW7du2r59u/bt26eMjAy1adNG1157rb788stG7e/jjz/WoEGDFBERocjISPXt21c7duzwbf/HP/6hgQMHqnXr1mrXrp1uuukmHT16VJLk9Xo1Y8YMdejQQWFhYRowYICKi4t9j33vvffkcrm0bt06paeny+12a8uWLbIsS8uWLVOnTp0UHh6u3r17669//at/DxSAJiNmAATcww8/rPHjx6u0tFSpqakaM2aMJk2apJycHF+ITJs2rVH7uuuuu3TZZZepuLhYJSUlmjt3rkJDQyVJpaWluvHGG5WWlqbt27dr69atGjFihGpqaiRJ999/v1555RU9//zz2rlzpzp37qybbrpJ33//fZ3nuP/++5WXl6c9e/aoV69emj9/vlauXKkVK1Zo9+7dmjVrlsaOHatNmzb58SgBaLJm+35uAEFr4MCB1syZMy3LsqyOHTtajz/+uG+bJGv+/Pm+5e3bt1uSrD/96U++dS+99JIVFhbWqOeKiIiwnnvuuXq3jR492urfv3+926qqqqzQ0FDrxRdf9K07deqUlZCQYC1btsyyLMvauHGjJclas2ZNnceFhYVZ27Ztq7O/e+65xxo9enSjZgYQWC1tbikAPwG9evXy/Tk2NlaS1LNnzzrrTp48KY/Ho8jIyPPuKzs7W/fee6/+8pe/aPDgwRo1apQuv/xyST+cmRk1alS9j/vyyy91+vRp9e/f37cuNDRUV199tfbs2VPnvunp6b4/f/bZZzp58qSGDBlS5z6nTp1Snz59zjsrgOZBzAAIuLNvA0k/XEPT0Lra2toL7mvhwoUaM2aM3njjDb311ltasGCBXn75Zd12220KDw9v8HGWZdV5rv9e/7/r2rRp4/vz2ZneeOMNXXrppXXu53a7LzgvgMDjmhkAxrniiis0a9YsrV+/XiNHjtTKlSsl/XAGaMOGDfU+pnPnzmrVqpW2bt3qW3f69Gnt2LFD3bp1a/C5unfvLrfbrbKyMnXu3LnOLTEx0b9/MQBNwpkZAMaorq7W7NmzdfvttyslJUX/+te/VFxcrF/96leSpJycHPXs2VNTpkzR5MmT1apVK23cuFGjRo1STEyM7rvvPs2ePVvR0dFKSkrSsmXLdOLECd1zzz0NPmdERIR+97vfadasWaqtrdWAAQPk8Xi0bds2XXLJJZowYUJz/fUBNICYAWCMkJAQfffddxo/fry+/fZbxcTEaOTIkcrNzZX0wxmb9evX64EHHtDVV1+t8PBwXXPNNRo9erQk6dFHH1Vtba3GjRunY8eOKT09XevWrVO7du3O+7wPP/ywOnTooLy8PH311Vdq27atrrrqKj3wwAMB/zsDuDCXdfaNZAAAAANxzQwAADAaMQPAUdLS0nTJJZfUe3vxxRftHg+AA/E2EwBHOXDggE6fPl3vttjYWEVERDTzRACcjpgBAABG420mAABgNGIGAAAYjZgBAABGI2YAAIDRiBkAAGA0YgYAABiNmAEAAEYjZgAAgNH+H08YrLh3bFj+AAAAAElFTkSuQmCC", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"llm_score\", bins=20)"]}, {"cell_type": "code", "execution_count": null, "id": "8c6ac210", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}