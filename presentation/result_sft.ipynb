{"cells": [{"cell_type": "code", "execution_count": 1, "id": "cf9d20bd", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import seaborn as sns"]}, {"cell_type": "code", "execution_count": 2, "id": "2a29a094", "metadata": {}, "outputs": [], "source": ["df = pd.read_json(\"qwen3-14B-sft.json\")"]}, {"cell_type": "code", "execution_count": 3, "id": "55ed4ffb", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "prompt", "rawType": "object", "type": "string"}, {"name": "generated_json", "rawType": "object", "type": "string"}, {"name": "ground_truth", "rawType": "object", "type": "string"}, {"name": "generated_text", "rawType": "object", "type": "string"}, {"name": "json_valid", "rawType": "bool", "type": "boolean"}, {"name": "tries", "rawType": "int64", "type": "integer"}, {"name": "llm_score", "rawType": "int64", "type": "integer"}, {"name": "json_parsed", "rawType": "float64", "type": "float"}, {"name": "top_level_keys_present", "rawType": "float64", "type": "float"}, {"name": "workflow_name_valid", "rawType": "float64", "type": "float"}, {"name": "num_nodes", "rawType": "float64", "type": "float"}, {"name": "num_nodes_with_valid_type", "rawType": "float64", "type": "float"}, {"name": "num_nodes_with_valid_version", "rawType": "float64", "type": "float"}, {"name": "num_nodes_with_valid_structure", "rawType": "float64", "type": "float"}, {"name": "num_nodes_with_parameters", "rawType": "float64", "type": "float"}, {"name": "num_connections", "rawType": "float64", "type": "float"}, {"name": "num_connections_with_valid_structure", "rawType": "float64", "type": "float"}, {"name": "num_connections_with_valid_target_node", "rawType": "float64", "type": "float"}, {"name": "active_field_boolean", "rawType": "float64", "type": "float"}, {"name": "evaluation_results", "rawType": "object", "type": "unknown"}], "ref": "9fee04dc-ca39-49c1-ab96-53321ee787ad", "rows": [["0", "Create an n8n workflow that sends a daily vegan recipe from Spoonacular API to a list of Telegram users stored in Airtable. Additionally, the workflow should handle new Telegram users by sending a welcome message with a recipe, and then adding their chat ID and name to the Airtable base, ensuring duplicates are not added.", "{\"name\": \"Send daily vegan recipes to Telegram users\", \"nodes\": [{\"parameters\": {\"rule\": {\"interval\": [{\"triggerAtHour\": 8}]}}, \"name\": \"Schedule Trigger\", \"type\": \"n8n-nodes-base.scheduleTrigger\", \"typeVersion\": 1.2, \"position\": [-100, 140]}, {\"parameters\": {\"operation\": \"getAll\", \"base\": {\"__rl\": true, \"value\": \"app6Qv97Y7Q777777\", \"mode\": \"list\", \"cachedResultName\": \"Base Name\", \"cachedResultUrl\": \"https://airtable.com/app6Qv97Y7Q777777\"}, \"table\": {\"__rl\": true, \"value\": \"tbl1234567890\", \"mode\": \"list\", \"cachedResultName\": \"Table Name\", \"cachedResultUrl\": \"https://airtable.com/app6Qv97Y7Q777777/tbl1234567890\"}, \"options\": {}}, \"name\": \"Airtable\", \"type\": \"n8n-nodes-base.airtable\", \"typeVersion\": 2.1, \"position\": [100, 140]}, {\"parameters\": {\"operation\": \"send\", \"chatId\": \"={{ $json.Telegram }}\", \"text\": \"=Here is your daily vegan recipe:\\n\\n**Name:** {{ $json.name }}\\n\\n**Instructions:**\\n{{ $json.instructions }}\\n\\n**Ingredients:**\\n{{ $json.ingredientList }}\", \"additionalFields\": {\"replyMarkup\": {\"keyboard\": [[{\"text\": \"Next\"}, {\"text\": \"Back\"}]], \"resizeKeyboard\": true, \"oneTimeKeyboard\": false, \"selective\": true}, \"parse_mode\": \"HTML\"}}, \"name\": \"Telegram\", \"type\": \"n8n-nodes-base.telegram\", \"typeVersion\": 1.2, \"position\": [300, 140]}, {\"parameters\": {\"url\": \"https://api.spoonacular.com/recipes/complexSearch\", \"sendQuery\": true, \"queryParameters\": {\"parameters\": [{\"name\": \"diet\", \"value\": \"vegan\"}, {\"name\": \"limit\", \"value\": \"1\"}]}, \"options\": {}, \"authentication\": \"genericCredentialType\", \"genericAuthType\": \"httpHeaderAuth\", \"headerParameters\": {\"parameters\": [{\"name\": \"X-API-Key\", \"value\": \"YOUR_SPOONACULAR_API_KEY\"}]}}, \"name\": \"Spoonacular\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 4.2, \"position\": [500, 140]}, {\"parameters\": {\"operation\": \"send\", \"chatId\": \"={{ $('Airtable').item.json.Telegram }}\", \"text\": \"=Here is your daily vegan recipe:\\n\\n**Name:** {{ $json.name }}\\n\\n**Instructions:**\\n{{ $json.instructions }}\\n\\n**Ingredients:**\\n{{ $json.ingredientList }}\", \"additionalFields\": {\"replyMarkup\": {\"keyboard\": [[{\"text\": \"Next\"}, {\"text\": \"Back\"}]], \"resizeKeyboard\": true, \"oneTimeKeyboard\": false, \"selective\": true}, \"parse_mode\": \"HTML\"}}, \"name\": \"Telegram1\", \"type\": \"n8n-nodes-base.telegram\", \"typeVersion\": 1.2, \"position\": [700, 140]}, {\"parameters\": {\"updates\": [\"message\"], \"additionalFields\": {}}, \"name\": \"Telegram Trigger\", \"type\": \"n8n-nodes-base.telegramTrigger\", \"typeVersion\": 1.1, \"position\": [-100, 340]}, {\"parameters\": {\"operation\": \"getAll\", \"base\": {\"__rl\": true, \"value\": \"app6Qv97Y7Q777777\", \"mode\": \"list\", \"cachedResultName\": \"Base Name\", \"cachedResultUrl\": \"https://airtable.com/app6Qv97Y7Q777777\"}, \"table\": {\"__rl\": true, \"value\": \"tbl1234567890\", \"mode\": \"list\", \"cachedResultName\": \"Table Name\", \"cachedResultUrl\": \"https://airtable.com/app6Qv97Y7Q777777/tbl1234567890\"}, \"options\": {}}, \"name\": \"Airtable1\", \"type\": \"n8n-nodes-base.airtable\", \"typeVersion\": 2.1, \"position\": [100, 340]}, {\"parameters\": {\"conditions\": {\"options\": {\"caseSensitive\": true, \"leftValue\": \"\", \"typeValidation\": \"strict\", \"version\": 2}, \"conditions\": [{\"id\": \"3333333333333333\", \"leftValue\": \"={{ $json.message.chat.id }}\", \"rightValue\": \"={{ $json.Telegram }}\", \"operator\": {\"type\": \"string\", \"operation\": \"notContains\"}}], \"combinator\": \"and\"}, \"options\": {}}, \"name\": \"If\", \"type\": \"n8n-nodes-base.if\", \"typeVersion\": 2.2, \"position\": [300, 340]}, {\"parameters\": {\"operation\": \"send\", \"chatId\": \"={{ $json.message.chat.id }}\", \"text\": \"=Welcome to the Daily Vegan Recipe Bot!\\n\\nClick the button below to get your first vegan recipe.\", \"additionalFields\": {\"replyMarkup\": {\"keyboard\": [[{\"text\": \"Start\"}]], \"resizeKeyboard\": true, \"oneTimeKeyboard\": false, \"selective\": true}}}, \"name\": \"Telegram2\", \"type\": \"n8n-nodes-base.telegram\", \"typeVersion\": 1.2, \"position\": [500, 340]}, {\"parameters\": {\"url\": \"https://api.spoonacular.com/recipes/complexSearch\", \"sendQuery\": true, \"queryParameters\": {\"parameters\": [{\"name\": \"diet\", \"value\": \"vegan\"}, {\"name\": \"limit\", \"value\": \"1\"}]}, \"options\": {}, \"authentication\": \"genericCredentialType\", \"genericAuthType\": \"httpHeaderAuth\", \"headerParameters\": {\"parameters\": [{\"name\": \"X-API-Key\", \"value\": \"YOUR_SPOONACULAR_API_KEY\"}]}}, \"name\": \"Spoonacular1\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 4.2, \"position\": [700, 340]}, {\"parameters\": {\"operation\": \"send\", \"chatId\": \"={{ $('Telegram Trigger').item.json.message.chat.id }}\", \"text\": \"=Here is your daily vegan recipe:\\n\\n**Name:** {{ $json.name }}\\n\\n**Instructions:**\\n{{ $json.instructions }}\\n\\n**Ingredients:**\\n{{ $json.ingredientList }}\", \"additionalFields\": {\"replyMarkup\": {\"keyboard\": [[{\"text\": \"Next\"}, {\"text\": \"Back\"}]], \"resizeKeyboard\": true, \"oneTimeKeyboard\": false, \"selective\": true}, \"parse_mode\": \"HTML\"}}, \"name\": \"Telegram3\", \"type\": \"n8n-nodes-base.telegram\", \"typeVersion\": 1.2, \"position\": [900, 340]}, {\"parameters\": {\"operation\": \"create\", \"base\": {\"__rl\": true, \"value\": \"app6Qv97Y7Q777777\", \"mode\": \"list\", \"cachedResultName\": \"Base Name\", \"cachedResultUrl\": \"https://airtable.com/app6Qv97Y7Q777777\"}, \"table\": {\"__rl\": true, \"value\": \"tbl1234567890\", \"mode\": \"list\", \"cachedResultName\": \"Table Name\", \"cachedResultUrl\": \"https://airtable.com/app6Qv97Y7Q777777/tbl1234567890\"}, \"columns\": {\"mappingMode\": \"defineBelow\", \"value\": {\"Name\": \"={{ $('Telegram Trigger').item.json.message.from.first_name }}\", \"Telegram\": \"={{ $('Telegram Trigger').item.json.message.chat.id }}\"}, \"matchingColumns\": [], \"schema\": [{\"id\": \"Name\", \"displayName\": \"Name\", \"required\": false, \"defaultMatch\": false, \"canBeUsedToMatch\": true, \"display\": true, \"type\": \"string\", \"readOnly\": false, \"removed\": false}, {\"id\": \"Telegram\", \"displayName\": \"Telegram\", \"required\": false, \"defaultMatch\": false, \"canBeUsedToMatch\": true, \"display\": true, \"type\": \"string\", \"readOnly\": false, \"removed\": false}], \"attemptToConvertTypes\": false, \"convertFieldsToString\": false}, \"options\": {}}, \"name\": \"Airtable2\", \"type\": \"n8n-nodes-base.airtable\", \"typeVersion\": 2.1, \"position\": [1100, 340]}], \"connections\": {\"Schedule Trigger\": {\"main\": [[{\"node\": \"Airtable\", \"type\": \"main\", \"index\": 0}]]}, \"Airtable\": {\"main\": [[{\"node\": \"Telegram\", \"type\": \"main\", \"index\": 0}]]}, \"Telegram\": {\"main\": [[{\"node\": \"Spoonacular\", \"type\": \"main\", \"index\": 0}]]}, \"Spoonacular\": {\"main\": [[{\"node\": \"Telegram1\", \"type\": \"main\", \"index\": 0}]]}, \"Telegram Trigger\": {\"main\": [[{\"node\": \"Airtable1\", \"type\": \"main\", \"index\": 0}]]}, \"Airtable1\": {\"main\": [[{\"node\": \"If\", \"type\": \"main\", \"index\": 0}]]}, \"If\": {\"main\": [[{\"node\": \"Telegram2\", \"type\": \"main\", \"index\": 0}]]}, \"Telegram2\": {\"main\": [[{\"node\": \"Spoonacular1\", \"type\": \"main\", \"index\": 0}]]}, \"Spoonacular1\": {\"main\": [[{\"node\": \"Telegram3\", \"type\": \"main\", \"index\": 0}]]}, \"Telegram3\": {\"main\": [[{\"node\": \"Airtable2\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}", "{\"nodes\":[{\"name\":\"Cron\",\"type\":\"n8n-nodes-base.cron\",\"position\":[440,440],\"parameters\":{\"triggerTimes\":{\"item\":[{}]}},\"typeVersion\":1},{\"name\":\"Airtable2\",\"type\":\"n8n-nodes-base.airtable\",\"notes\":\"Grab our list of chats from Airtable to send a random recipe\",\"position\":[660,440],\"parameters\":{\"table\":\"Table 1\",\"operation\":\"list\",\"application\":\"your_sheet_id\",\"additionalOptions\":{}},\"credentials\":{\"airtableApi\":{\"id\":\"5\",\"name\":\"Airtable account\"}},\"notesInFlow\":true,\"typeVersion\":1},{\"name\":\"Set\",\"type\":\"n8n-nodes-base.set\",\"position\":[860,600],\"parameters\":{\"values\":{\"number\":[{\"name\":\"chatid\",\"value\":\"={{$node[\\\"Airtable2\\\"].json[\\\"fields\\\"][\\\"chatid\\\"]}}\"}],\"string\":[]},\"options\":{}},\"typeVersion\":1},{\"name\":\"Recipe Photo\",\"type\":\"n8n-nodes-base.telegram\",\"position\":[1240,440],\"parameters\":{\"file\":\"={{$node[\\\"Get recipes from API\\\"].json[\\\"recipes\\\"][0][\\\"image\\\"]}}\",\"chatId\":\"={{$node[\\\"Set\\\"].json[\\\"chatid\\\"]}}\",\"operation\":\"sendPhoto\",\"additionalFields\":{}},\"credentials\":{\"telegramApi\":{\"id\":\"1\",\"name\":\"Telegram account\"}},\"typeVersion\":1,\"continueOnFail\":true},{\"name\":\"Recipe URL\",\"type\":\"n8n-nodes-base.telegram\",\"position\":[1420,440],\"parameters\":{\"text\":\"=\\n{{$node[\\\"Get recipes from API\\\"].json[\\\"recipes\\\"][0][\\\"title\\\"]}}\\n\\n{{$node[\\\"Get recipes from API\\\"].json[\\\"recipes\\\"][0][\\\"sourceUrl\\\"]}}\",\"chatId\":\"={{$node[\\\"Set\\\"].json[\\\"chatid\\\"]}}\",\"additionalFields\":{}},\"credentials\":{\"telegramApi\":{\"id\":\"1\",\"name\":\"Telegram account\"}},\"typeVersion\":1,\"continueOnFail\":true},{\"name\":\"IF\",\"type\":\"n8n-nodes-base.if\",\"notes\":\"If the chat ID isn't in our airtable, we add it. This is to send a new recipe daily. \",\"position\":[860,-80],\"parameters\":{\"conditions\":{\"number\":[],\"string\":[{\"value1\":\"= {{$node[\\\"Airtable1\\\"].parameter[\\\"fields\\\"][1]}}\",\"value2\":\"= {{$node[\\\"Airtable1\\\"].parameter[\\\"fields\\\"][0]}}\",\"operation\":\"notEqual\"}],\"boolean\":[]}},\"notesInFlow\":true,\"typeVersion\":1},{\"name\":\"Airtable\",\"type\":\"n8n-nodes-base.airtable\",\"position\":[620,-80],\"parameters\":{\"table\":\"Table 1\",\"operation\":\"list\",\"application\":\"your_sheet_id\",\"additionalOptions\":{}},\"credentials\":{\"airtableApi\":{\"id\":\"5\",\"name\":\"Airtable account\"}},\"typeVersion\":1},{\"name\":\"Airtable1\",\"type\":\"n8n-nodes-base.airtable\",\"position\":[1340,-100],\"parameters\":{\"table\":\"Table 1\",\"fields\":[\"chatid\",\"={{$node[\\\"Telegram Trigger - people join bot\\\"].json[\\\"message\\\"][\\\"chat\\\"][\\\"id\\\"]}}\",\"Name\",\"={{$node[\\\"Telegram Trigger - people join bot\\\"].json[\\\"message\\\"][\\\"from\\\"][\\\"first_name\\\"]}}\"],\"options\":{},\"operation\":\"append\",\"application\":\"your_sheet_id\",\"addAllFields\":false},\"credentials\":{\"airtableApi\":{\"id\":\"5\",\"name\":\"Airtable account\"}},\"typeVersion\":1},{\"name\":\"Telegram Recipe Image\",\"type\":\"n8n-nodes-base.telegram\",\"position\":[980,180],\"parameters\":{\"file\":\"={{$node[\\\"Get recipes\\\"].json[\\\"recipes\\\"][0][\\\"image\\\"]}}\",\"chatId\":\"={{$node[\\\"Telegram Trigger - people join bot\\\"].json[\\\"message\\\"][\\\"chat\\\"][\\\"id\\\"]}}\",\"operation\":\"sendPhoto\",\"additionalFields\":{}},\"credentials\":{\"telegramApi\":{\"id\":\"1\",\"name\":\"Telegram account\"}},\"typeVersion\":1},{\"name\":\"Telegram Recipe URL\",\"type\":\"n8n-nodes-base.telegram\",\"position\":[1180,180],\"parameters\":{\"text\":\"=\\n{{$node[\\\"Get recipes\\\"].json[\\\"recipes\\\"][0][\\\"title\\\"]}}\\n\\n{{$node[\\\"Get recipes\\\"].json[\\\"recipes\\\"][0][\\\"sourceUrl\\\"]}}\",\"chatId\":\"={{$node[\\\"Telegram Trigger - people join bot\\\"].json[\\\"message\\\"][\\\"chat\\\"][\\\"id\\\"]}}\",\"additionalFields\":{}},\"credentials\":{\"telegramApi\":{\"id\":\"1\",\"name\":\"Telegram account\"}},\"typeVersion\":1},{\"name\":\"Set1\",\"type\":\"n8n-nodes-base.set\",\"position\":[1120,-100],\"parameters\":{\"values\":{\"string\":[{\"name\":\"chatid\",\"value\":\"={{$node[\\\"Telegram Trigger - people join bot\\\"].json[\\\"message\\\"][\\\"chat\\\"][\\\"id\\\"]}}\"},{\"name\":\"Name\",\"value\":\"={{$node[\\\"Telegram Trigger - people join bot\\\"].json[\\\"message\\\"][\\\"from\\\"][\\\"first_name\\\"]}}\"}]},\"options\":{}},\"typeVersion\":1},{\"name\":\"Get recipes from API\",\"type\":\"n8n-nodes-base.httpRequest\",\"notes\":\"https://spoonacular.com/food-api/docs\",\"position\":[1080,440],\"parameters\":{\"url\":\"https://api.spoonacular.com/recipes/random?apiKey=APIKEYHERE&number=1&tags=vegan\",\"options\":{\"fullResponse\":false},\"queryParametersUi\":{\"parameter\":[]}},\"typeVersion\":1},{\"name\":\"Get recipes\",\"type\":\"n8n-nodes-base.httpRequest\",\"notes\":\"https://spoonacular.com/food-api/docs\",\"position\":[800,180],\"parameters\":{\"url\":\"https://api.spoonacular.com/recipes/random?apiKey=APIKEYHERE&number=1&tags=vegan\",\"options\":{\"fullResponse\":false},\"queryParametersUi\":{\"parameter\":[]}},\"typeVersion\":1},{\"name\":\"Telegram Trigger - people join bot\",\"type\":\"n8n-nodes-base.telegramTrigger\",\"position\":[420,140],\"webhookId\":\"your_bot_id\",\"parameters\":{\"updates\":[\"message\"],\"additionalFields\":{}},\"credentials\":{\"telegramApi\":{\"id\":\"1\",\"name\":\"Telegram account\"}},\"typeVersion\":1},{\"name\":\"Telegram - Welcome Message\",\"type\":\"n8n-nodes-base.telegram\",\"position\":[620,180],\"parameters\":{\"text\":\"=Welcome! This bot will send you one vegan recipe a day. Here is your first recipe!\",\"chatId\":\"={{$node[\\\"Telegram Trigger - people join bot\\\"].json[\\\"message\\\"][\\\"chat\\\"][\\\"id\\\"]}}\",\"additionalFields\":{}},\"credentials\":{\"telegramApi\":{\"id\":\"1\",\"name\":\"Telegram account\"}},\"typeVersion\":1}],\"connections\":{\"IF\":{\"main\":[[{\"node\":\"Set1\",\"type\":\"main\",\"index\":0}]]},\"Set\":{\"main\":[[{\"node\":\"Get recipes from API\",\"type\":\"main\",\"index\":0}]]},\"Cron\":{\"main\":[[{\"node\":\"Airtable2\",\"type\":\"main\",\"index\":0}]]},\"Set1\":{\"main\":[[{\"node\":\"Airtable1\",\"type\":\"main\",\"index\":0}]]},\"Airtable\":{\"main\":[[{\"node\":\"IF\",\"type\":\"main\",\"index\":0}]]},\"Airtable2\":{\"main\":[[{\"node\":\"Set\",\"type\":\"main\",\"index\":0}]]},\"Get recipes\":{\"main\":[[{\"node\":\"Telegram Recipe Image\",\"type\":\"main\",\"index\":0}]]},\"Recipe Photo\":{\"main\":[[{\"node\":\"Recipe URL\",\"type\":\"main\",\"index\":0}]]},\"Get recipes from API\":{\"main\":[[{\"node\":\"Recipe Photo\",\"type\":\"main\",\"index\":0}]]},\"Telegram Recipe Image\":{\"main\":[[{\"node\":\"Telegram Recipe URL\",\"type\":\"main\",\"index\":0}]]},\"Telegram - Welcome Message\":{\"main\":[[{\"node\":\"Get recipes\",\"type\":\"main\",\"index\":0}]]},\"Telegram Trigger - people join bot\":{\"main\":[[{\"node\":\"Airtable\",\"type\":\"main\",\"index\":0},{\"node\":\"Telegram - Welcome Message\",\"type\":\"main\",\"index\":0}]]}}}", "assistant\n<think>\n\n</think>\n\n<Thinking>\nOkay, let's start planning this n8n workflow. The goal is to send daily vegan recipes to Telegram users from Airtable, handle new users, and ensure no duplicates are added.\n\nFirst, I need to think about how this workflow will be triggered. The prompt mentions \"daily vegan recipe,\" so a daily trigger is essential. I'll also need a way to handle new Telegram users, which implies a webhook for incoming messages.\n\n**Node 1: Schedule Trigger**\n*   **Type:** `n8n-nodes-base.scheduleTrigger`\n*   **Type Version:** `1.2`\n*   **Reasoning:** This is the perfect node to kick off the daily recipe sending. It allows me to set a specific time each day for the workflow to run automatically. I'll configure it to trigger once a day, say at 8 AM, to ensure the recipe is sent out promptly.\n*   **Configuration:** I'll set the `rule` to trigger `interval` and specify `triggerAtHour: 8`. This ensures the workflow runs daily at the desired time.\n\n**Node 2: Airtable**\n*   **Type:** `n8n-nodes-base.airtable`\n*   **Type Version:** `2.1`\n*   **Reasoning:** Before sending out recipes, I need to know who my Telegram users are. This means I need to fetch the list of users from Airtable. The Airtable node is the direct way to interact with an Airtable base.\n*   **Connection:** This node will connect directly from the `Schedule Trigger`'s `main` output. It needs to run immediately after the schedule triggers.\n*   **Configuration:** I'll set the `operation` to `getAll` to retrieve all user records. I'll specify the `base` and `table` where my users are stored. For credentials, I'll link it to an Airtable API credential, which would contain the necessary authentication details to access my base.\n\n**Node 3: Telegram**\n*   **Type:** `n8n-nodes-base.telegram`\n*   **Type Version:** `1.2`\n*   **Reasoning:** Now that I have the list of users from Airtable, I need to send them the recipe. The Telegram node is the standard way to interact with the Telegram Bot API.\n*   **Connection:** This node will connect from the `Airtable` node's `main` output. It will receive the list of users and then iterate through them to send messages.\n*   **Configuration:** The `operation` will be `send`. I'll set the `chatId` dynamically using an expression like `={{ $json.Telegram }}` to target each user individually. The `text` field will contain the recipe details, which I'll pull from the Spoonacular API. I'll also set `parse_mode` to `HTML` for better formatting. For `credentials`, I'll link it to a Telegram Bot API credential.\n\n**Node 4: Spoonacular**\n*   **Type:** `n8n-nodes-base.httpRequest`\n*   **Type Version:** `4.2`\n*   **Reasoning:** To get the vegan recipe, I need to make an API call to Spoonacular. The HTTP Request node is the most flexible way to interact with any external API.\n*   **Connection:** This node will connect from the `Telegram` node's `main` output. It will run for each user, fetching a new recipe for them.\n*   **Configuration:** The `url` will be the Spoonacular API endpoint for recipes, including the `diet` parameter set to `vegan`. I'll also add a `limit` parameter to get a single recipe. For `authentication`, I'll use a `genericCredentialType` with `httpHeaderAuth` and provide the Spoonacular API key as a header. I'll also set `sendQuery` to `true` to include the query parameters.\n\n**Node 5: Telegram1**\n*   **Type:** `n8n-nodes-base.telegram`\n*   **Type Version:** `1.2`\n*   **Reasoning:** After fetching the recipe, I need to send it to the user. This is another Telegram node, similar to the first one, but it will use the data from the Spoonacular API.\n*   **Connection:** This node will connect from the `Spoonacular` node's `main` output. It will receive the recipe data and send it to the user.\n*   **Configuration:** The `operation` will be `send`. The `chatId` will again be dynamic, using `={{ $('Airtable').item.json.Telegram }}` to ensure it sends to the correct user from the Airtable list. The `text` will be constructed using expressions to pull the recipe's name, instructions, and ingredients from the Spoonacular response. I'll also include a `replyMarkup` with a `keyboard` for \"Next\" and \"Back\" buttons, allowing users to navigate recipes. `parse_mode` will be `HTML` again. `credentials` will link to the Telegram Bot API.\n\nNow, let's consider the second part of the prompt: handling new Telegram users.\n\n**Node 6: Telegram Trigger**\n*   **Type:** `n8n-nodes-base.telegramTrigger`\n*   **Type Version:** `1.1`\n*   **Reasoning:** When a new user sends a message to the bot, I need to capture that event. The Telegram Trigger node is designed specifically for this, listening for incoming messages.\n*   **Connection:** This node will run independently of the schedule trigger, as it's an event-based trigger. It will connect to the `Airtable` node (the one that gets all users) via a connection named `Airtable1` to check if the incoming user is already in the Airtable base.\n\n**Node 7: Airtable1**\n*   **Type:** `n8n-nodes-base.airtable`\n*   **Type Version:** `2.1`\n*   **Reasoning:** Before sending a welcome message, I need to check if the incoming Telegram user is already registered in my Airtable base. This prevents duplicate entries and ensures the welcome message is only sent once.\n*   **Connection:** This node will connect from the `Telegram Trigger`'s `main` output. It will also connect to the `Airtable` node (the one that gets all users) via a connection named `Airtable` to get the full list of existing users for comparison.\n*   **Configuration:** The `operation` will be `getAll` to retrieve all user records from the Airtable base. I'll specify the `base` and `table` again. `credentials` will link to the Airtable API.\n\n**Node 8: If**\n*   **Type:** `n8n-nodes-base.if`\n*   **Type Version:** `2.2`\n*   **Reasoning:** This node is crucial for the logic of checking if a user is new. It will compare the incoming Telegram user's chat ID with the list of existing users from Airtable.\n*   **Connection:** This node will connect from the `Airtable1` node's `main` output. It will also connect to the `Airtable` node (the one that gets all users) via a connection named `Airtable` to get the full list of existing users for comparison.\n*   **Configuration:** I'll set up a condition to check if the `chatId` from the incoming Telegram message (`={{ $json.message.chat.id }}`) is *not* present in the list of existing Airtable users. This means if the `chatId` is *not* found in the Airtable data, the condition will be true, indicating a new user.\n\n**Node 9: Telegram2**\n*   **Type:** `n8n-nodes-base.telegram`\n*   **Type Version:** `1.2`\n*   **Reasoning:** If the `If` node determines that the user is new, I need to send them a welcome message. This is another Telegram node.\n*   **Connection:** This node will connect from the `If` node's `true` output. It will only execute if the user is indeed new.\n*   **Configuration:** The `operation` will be `send`. The `chatId` will be dynamic, using `={{ $json.message.chat.id }}` to address the new user. The `text` will be a welcome message, including a \"Start\" button to initiate the recipe process. `credentials` will link to the Telegram Bot API.\n\n**Node 10: Spoonacular1**\n*   **Type:** `n8n-nodes-base.httpRequest`\n*   **Type Version:** `4.2`\n*   **Reasoning:** After sending the welcome message, the new user will click \"Start,\" and I need to fetch a recipe for them. This is another HTTP Request node to Spoonacular.\n*   **Connection:** This node will connect from the `Telegram2` node's `main` output. It will run after the welcome message is sent.\n*   **Configuration:** The `url` will be the Spoonacular API endpoint for recipes, including the `diet` parameter set to `vegan`. I'll also add a `limit` parameter to get a single recipe. For `authentication`, I'll use a `genericCredentialType` with `httpHeaderAuth` and provide the Spoonacular API key as a header. I'll also set `sendQuery` to `true` to include the query parameters.\n\n**Node 11: Telegram3**\n*   **Type:** `n8n-nodes-base.telegram`\n*   **Type Version:** `1.2`\n*   **Reasoning:** After fetching the recipe for the new user, I need to send it to them. This is another Telegram node.\n*   **Connection:** This node will connect from the `Spoonacular1` node's `main` output. It will receive the recipe data and send it to the new user.\n*   **Configuration:** The `operation` will be `send`. The `chatId` will be dynamic, using `={{ $('Telegram Trigger').item.json.message.chat.id }}` to ensure it sends to the correct new user. The `text` will be constructed using expressions to pull the recipe's name, instructions, and ingredients from the Spoonacular response. I'll also include a `replyMarkup` with a `keyboard` for \"Next\" and \"Back\" buttons, allowing users to navigate recipes. `parse_mode` will be `HTML` again. `credentials` will link to the Telegram Bot API.\n\n**Node 12: Airtable2**\n*   **Type:** `n8n-nodes-base.airtable`\n*   **Type Version:** `2.1`\n*   **Reasoning:** Finally, after a new user has been welcomed and received their first recipe, I need to add their chat ID and name to the Airtable base. This ensures they are properly registered for future daily recipes.\n*   **Connection:** This node will connect from the `Telegram3` node's `main` output. It will run after the new user has received their first recipe.\n*   **Configuration:** The `operation` will be `create` to add a new record. I'll map the `fields` to include `Name` (from `={{ $('Telegram Trigger').item.json.message.from.first_name }}`) and `Telegram` (from `={{ $('Telegram Trigger').item.json.message.chat.id }}`). I'll specify the `base` and `table` again. `credentials` will link to the Airtable API.\n\nThis setup covers both the daily recipe sending and the new user registration process, ensuring a seamless experience for all users.\n</Thinking>\n\n<Json>\n{\"name\": \"Send daily vegan recipes to Telegram users\", \"nodes\": [{\"parameters\": {\"rule\": {\"interval\": [{\"triggerAtHour\": 8}]}}, \"name\": \"Schedule Trigger\", \"type\": \"n8n-nodes-base.scheduleTrigger\", \"typeVersion\": 1.2, \"position\": [-100, 140]}, {\"parameters\": {\"operation\": \"getAll\", \"base\": {\"__rl\": true, \"value\": \"app6Qv97Y7Q777777\", \"mode\": \"list\", \"cachedResultName\": \"Base Name\", \"cachedResultUrl\": \"https://airtable.com/app6Qv97Y7Q777777\"}, \"table\": {\"__rl\": true, \"value\": \"tbl1234567890\", \"mode\": \"list\", \"cachedResultName\": \"Table Name\", \"cachedResultUrl\": \"https://airtable.com/app6Qv97Y7Q777777/tbl1234567890\"}, \"options\": {}}, \"name\": \"Airtable\", \"type\": \"n8n-nodes-base.airtable\", \"typeVersion\": 2.1, \"position\": [100, 140]}, {\"parameters\": {\"operation\": \"send\", \"chatId\": \"={{ $json.Telegram }}\", \"text\": \"=Here is your daily vegan recipe:\\n\\n**Name:** {{ $json.name }}\\n\\n**Instructions:**\\n{{ $json.instructions }}\\n\\n**Ingredients:**\\n{{ $json.ingredientList }}\", \"additionalFields\": {\"replyMarkup\": {\"keyboard\": [[{\"text\": \"Next\"}, {\"text\": \"Back\"}]], \"resizeKeyboard\": true, \"oneTimeKeyboard\": false, \"selective\": true}, \"parse_mode\": \"HTML\"}}, \"name\": \"Telegram\", \"type\": \"n8n-nodes-base.telegram\", \"typeVersion\": 1.2, \"position\": [300, 140]}, {\"parameters\": {\"url\": \"https://api.spoonacular.com/recipes/complexSearch\", \"sendQuery\": true, \"queryParameters\": {\"parameters\": [{\"name\": \"diet\", \"value\": \"vegan\"}, {\"name\": \"limit\", \"value\": \"1\"}]}, \"options\": {}, \"authentication\": \"genericCredentialType\", \"genericAuthType\": \"httpHeaderAuth\", \"headerParameters\": {\"parameters\": [{\"name\": \"X-API-Key\", \"value\": \"YOUR_SPOONACULAR_API_KEY\"}]}}, \"name\": \"Spoonacular\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 4.2, \"position\": [500, 140]}, {\"parameters\": {\"operation\": \"send\", \"chatId\": \"={{ $('Airtable').item.json.Telegram }}\", \"text\": \"=Here is your daily vegan recipe:\\n\\n**Name:** {{ $json.name }}\\n\\n**Instructions:**\\n{{ $json.instructions }}\\n\\n**Ingredients:**\\n{{ $json.ingredientList }}\", \"additionalFields\": {\"replyMarkup\": {\"keyboard\": [[{\"text\": \"Next\"}, {\"text\": \"Back\"}]], \"resizeKeyboard\": true, \"oneTimeKeyboard\": false, \"selective\": true}, \"parse_mode\": \"HTML\"}}, \"name\": \"Telegram1\", \"type\": \"n8n-nodes-base.telegram\", \"typeVersion\": 1.2, \"position\": [700, 140]}, {\"parameters\": {\"updates\": [\"message\"], \"additionalFields\": {}}, \"name\": \"Telegram Trigger\", \"type\": \"n8n-nodes-base.telegramTrigger\", \"typeVersion\": 1.1, \"position\": [-100, 340]}, {\"parameters\": {\"operation\": \"getAll\", \"base\": {\"__rl\": true, \"value\": \"app6Qv97Y7Q777777\", \"mode\": \"list\", \"cachedResultName\": \"Base Name\", \"cachedResultUrl\": \"https://airtable.com/app6Qv97Y7Q777777\"}, \"table\": {\"__rl\": true, \"value\": \"tbl1234567890\", \"mode\": \"list\", \"cachedResultName\": \"Table Name\", \"cachedResultUrl\": \"https://airtable.com/app6Qv97Y7Q777777/tbl1234567890\"}, \"options\": {}}, \"name\": \"Airtable1\", \"type\": \"n8n-nodes-base.airtable\", \"typeVersion\": 2.1, \"position\": [100, 340]}, {\"parameters\": {\"conditions\": {\"options\": {\"caseSensitive\": true, \"leftValue\": \"\", \"typeValidation\": \"strict\", \"version\": 2}, \"conditions\": [{\"id\": \"3333333333333333\", \"leftValue\": \"={{ $json.message.chat.id }}\", \"rightValue\": \"={{ $json.Telegram }}\", \"operator\": {\"type\": \"string\", \"operation\": \"notContains\"}}], \"combinator\": \"and\"}, \"options\": {}}, \"name\": \"If\", \"type\": \"n8n-nodes-base.if\", \"typeVersion\": 2.2, \"position\": [300, 340]}, {\"parameters\": {\"operation\": \"send\", \"chatId\": \"={{ $json.message.chat.id }}\", \"text\": \"=Welcome to the Daily Vegan Recipe Bot!\\n\\nClick the button below to get your first vegan recipe.\", \"additionalFields\": {\"replyMarkup\": {\"keyboard\": [[{\"text\": \"Start\"}]], \"resizeKeyboard\": true, \"oneTimeKeyboard\": false, \"selective\": true}}}, \"name\": \"Telegram2\", \"type\": \"n8n-nodes-base.telegram\", \"typeVersion\": 1.2, \"position\": [500, 340]}, {\"parameters\": {\"url\": \"https://api.spoonacular.com/recipes/complexSearch\", \"sendQuery\": true, \"queryParameters\": {\"parameters\": [{\"name\": \"diet\", \"value\": \"vegan\"}, {\"name\": \"limit\", \"value\": \"1\"}]}, \"options\": {}, \"authentication\": \"genericCredentialType\", \"genericAuthType\": \"httpHeaderAuth\", \"headerParameters\": {\"parameters\": [{\"name\": \"X-API-Key\", \"value\": \"YOUR_SPOONACULAR_API_KEY\"}]}}, \"name\": \"Spoonacular1\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 4.2, \"position\": [700, 340]}, {\"parameters\": {\"operation\": \"send\", \"chatId\": \"={{ $('Telegram Trigger').item.json.message.chat.id }}\", \"text\": \"=Here is your daily vegan recipe:\\n\\n**Name:** {{ $json.name }}\\n\\n**Instructions:**\\n{{ $json.instructions }}\\n\\n**Ingredients:**\\n{{ $json.ingredientList }}\", \"additionalFields\": {\"replyMarkup\": {\"keyboard\": [[{\"text\": \"Next\"}, {\"text\": \"Back\"}]], \"resizeKeyboard\": true, \"oneTimeKeyboard\": false, \"selective\": true}, \"parse_mode\": \"HTML\"}}, \"name\": \"Telegram3\", \"type\": \"n8n-nodes-base.telegram\", \"typeVersion\": 1.2, \"position\": [900, 340]}, {\"parameters\": {\"operation\": \"create\", \"base\": {\"__rl\": true, \"value\": \"app6Qv97Y7Q777777\", \"mode\": \"list\", \"cachedResultName\": \"Base Name\", \"cachedResultUrl\": \"https://airtable.com/app6Qv97Y7Q777777\"}, \"table\": {\"__rl\": true, \"value\": \"tbl1234567890\", \"mode\": \"list\", \"cachedResultName\": \"Table Name\", \"cachedResultUrl\": \"https://airtable.com/app6Qv97Y7Q777777/tbl1234567890\"}, \"columns\": {\"mappingMode\": \"defineBelow\", \"value\": {\"Name\": \"={{ $('Telegram Trigger').item.json.message.from.first_name }}\", \"Telegram\": \"={{ $('Telegram Trigger').item.json.message.chat.id }}\"}, \"matchingColumns\": [], \"schema\": [{\"id\": \"Name\", \"displayName\": \"Name\", \"required\": false, \"defaultMatch\": false, \"canBeUsedToMatch\": true, \"display\": true, \"type\": \"string\", \"readOnly\": false, \"removed\": false}, {\"id\": \"Telegram\", \"displayName\": \"Telegram\", \"required\": false, \"defaultMatch\": false, \"canBeUsedToMatch\": true, \"display\": true, \"type\": \"string\", \"readOnly\": false, \"removed\": false}], \"attemptToConvertTypes\": false, \"convertFieldsToString\": false}, \"options\": {}}, \"name\": \"Airtable2\", \"type\": \"n8n-nodes-base.airtable\", \"typeVersion\": 2.1, \"position\": [1100, 340]}], \"connections\": {\"Schedule Trigger\": {\"main\": [[{\"node\": \"Airtable\", \"type\": \"main\", \"index\": 0}]]}, \"Airtable\": {\"main\": [[{\"node\": \"Telegram\", \"type\": \"main\", \"index\": 0}]]}, \"Telegram\": {\"main\": [[{\"node\": \"Spoonacular\", \"type\": \"main\", \"index\": 0}]]}, \"Spoonacular\": {\"main\": [[{\"node\": \"Telegram1\", \"type\": \"main\", \"index\": 0}]]}, \"Telegram Trigger\": {\"main\": [[{\"node\": \"Airtable1\", \"type\": \"main\", \"index\": 0}]]}, \"Airtable1\": {\"main\": [[{\"node\": \"If\", \"type\": \"main\", \"index\": 0}]]}, \"If\": {\"main\": [[{\"node\": \"Telegram2\", \"type\": \"main\", \"index\": 0}]]}, \"Telegram2\": {\"main\": [[{\"node\": \"Spoonacular1\", \"type\": \"main\", \"index\": 0}]]}, \"Spoonacular1\": {\"main\": [[{\"node\": \"Telegram3\", \"type\": \"main\", \"index\": 0}]]}, \"Telegram3\": {\"main\": [[{\"node\": \"Airtable2\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}\n</Json>", "True", "1", "3", "1.0", "4.0", "1.0", "12.0", "12.0", "12.0", "12.0", "12.0", "10.0", "10.0", "10.0", "1.0", null], ["1", "Create an n8n workflow that is triggered by a webhook. This workflow should read data from a Google Sheet (sheet ID '1uFISwZJ1rzkOnOSNocX-_n-ASSAznWGdpcPK3_KCvVo') and then build an HTML table using that data. Finally, the workflow should respond to the webhook with the generated HTML as the body and set the 'Content-Type' header to 'text/html; charset=UTF-8'.", "{\"name\": \"Untitled Workflow\", \"nodes\": [{\"parameters\": {\"path\": \"1234567890\", \"options\": {}, \"httpMethod\": \"POST\", \"responseMode\": \"responseNode\"}, \"name\": \"Webhook\", \"type\": \"n8n-nodes-base.webhook\", \"typeVersion\": 1, \"position\": [450, 300]}, {\"parameters\": {\"range\": \"A:Z\", \"sheetId\": \"1uFISwZJ1rzkOnOSNocX-_n-ASSAznWGdpcPK3_KCvVo\", \"options\": {}}, \"name\": \"Google Sheets\", \"type\": \"n8n-nodes-base.googleSheets\", \"typeVersion\": 1, \"position\": [650, 300]}, {\"parameters\": {\"jsCode\": \"const table = '<table border=\\\"1\\\" style=\\\"border-collapse: collapse;\\\">\\n  <thead>\\n    <tr>\\n      <th>Name</th>\\n      <th>Email</th>\\n    </tr>\\n  </thead>\\n  <tbody>\\n';\\n\\nconst style = '<style>\\n  table {\\n    border-collapse: collapse;\\n    width: 100%;\\n  }\\n  th, td {\\n    border: 1px solid #ccc;\\n    padding: 8px;\\n    text-align: left;\\n  }\\n</style>';\\n\\nconst rows = $input.all().map(item => {\\n  const name = item.json.name;\\n  const email = item.json.email;\\n  return `<tr><td>${name}</td><td>${email}</td></tr>`;\\n}).join('\\\\n');\\n\\nconst footer = '<tfoot>\\n  <tr>\\n    <td colspan=\\\"2\\\">This is the footer</td>\\n  </tr>\\n</tfoot>\\n</table>';\\n\\nreturn $input.all().map(item => {\\n  return table + rows + footer;\\n}).join('\\\\n');\\n\"}, \"name\": \"Code\", \"type\": \"n8n-nodes-base.code\", \"typeVersion\": 1, \"position\": [850, 300]}, {\"parameters\": {\"respondWith\": \"text\", \"responseBody\": \"={{$node[\\\"Code\\\"].json[\\\"data\\\"]}}\", \"options\": {\"responseHeaders\": {\"entries\": [{\"name\": \"Content-Type\", \"value\": \"text/html; charset=UTF-8\"}]}}}, \"name\": \"Respond to Webhook\", \"type\": \"n8n-nodes-base.respondToWebhook\", \"typeVersion\": 1, \"position\": [1050, 300]}], \"connections\": {\"Webhook\": {\"main\": [[{\"node\": \"Google Sheets\", \"type\": \"main\", \"index\": 0}]]}, \"Google Sheets\": {\"main\": [[{\"node\": \"Code\", \"type\": \"main\", \"index\": 0}]]}, \"Code\": {\"main\": [[{\"node\": \"Respond to Webhook\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}", "{\"nodes\":[{\"name\":\"Read from Google Sheets\",\"type\":\"n8n-nodes-base.googleSheets\",\"position\":[460,300],\"parameters\":{\"options\":{},\"sheetId\":\"1uFISwZJ1rzkOnOSNocX-_n-ASSAznWGdpcPK3_KCvVo\"},\"credentials\":{\"googleSheetsOAuth2Api\":{\"id\":\"19\",\"name\":\"Tom's Google Sheets account\"}},\"typeVersion\":2},{\"name\":\"Respond to Webhook\",\"type\":\"n8n-nodes-base.respondToWebhook\",\"position\":[900,300],\"parameters\":{\"options\":{\"responseHeaders\":{\"entries\":[{\"name\":\"Content-Type\",\"value\":\"text/html; charset=UTF-8\"}]}},\"respondWith\":\"text\",\"responseBody\":\"={{$json[\\\"html\\\"]}}\"},\"typeVersion\":1},{\"name\":\"Build HTML\",\"type\":\"n8n-nodes-base.function\",\"position\":[680,300],\"parameters\":{\"functionCode\":\"const columns = Object.keys(items[0].json);\\n\\nconst html = `\\n<!doctype html>\\n<html lang=\\\"en\\\">\\n  <head>\\n    <meta charset=\\\"utf-8\\\">\\n    <meta name=\\\"viewport\\\" content=\\\"width=device-width, initial-scale=1\\\">\\n    <title>HTML Table Example</title>\\n    <link href=\\\"https://cdn.jsdelivr.net/npm/bootstrap@5.2.0/dist/css/bootstrap.min.css\\\" rel=\\\"stylesheet\\\" integrity=\\\"sha384-gH2yIJqKdNHPEq0n4Mqa/HGKIhSkIHeL5AyhkYV8i59U5AR6csBvApHHNl/vI1Bx\\\" crossorigin=\\\"anonymous\\\">\\n  </head>\\n  <body>\\n    <div class=\\\"container\\\">\\n      <div class=\\\"row\\\">\\n        <div class=\\\"col\\\">\\n          <h1>HTML Table Example</h1>\\n          <table class=\\\"table\\\">\\n            <thead>\\n              <tr>\\n                ${columns.map(e => '<th scope=\\\"col\\\">'  e  '</th>').join('\\\\n')}\\n              </tr>\\n            </thead>\\n            <tbody>\\n              ${items.map(e => '<tr>'  columns.map(ee => '<td>'  e.json[ee]  '</td>').join('\\\\n')  '</tr>').join('\\\\n')}\\n            </tbody>\\n          </table>\\n        </div>\\n      </div>\\n    </div>\\n    <script src=\\\"https://cdn.jsdelivr.net/npm/bootstrap@5.2.0/dist/js/bootstrap.bundle.min.js\\\" integrity=\\\"sha384-A3rJD856KowSb7dwlZdYEkO39Gagi7vIsF0jrRAoQmDKKtQBHUuLZ9AsSv4jD4Xa\\\" crossorigin=\\\"anonymous\\\"></script>\\n  </body>\\n</html>\\n`;\\n\\nreturn [{\\n  json: {\\n    html: html\\n  }\\n}];\"},\"typeVersion\":1},{\"name\":\"Webhook\",\"type\":\"n8n-nodes-base.webhook\",\"position\":[240,300],\"webhookId\":\"bbcd9487-54f9-449d-8246-49f3f61f44fc\",\"parameters\":{\"path\":\"bbcd9487-54f9-449d-8246-49f3f61f44fc\",\"options\":{},\"responseMode\":\"responseNode\"},\"typeVersion\":1}],\"connections\":{\"Webhook\":{\"main\":[[{\"node\":\"Read from Google Sheets\",\"type\":\"main\",\"index\":0}]]},\"Build HTML\":{\"main\":[[{\"node\":\"Respond to Webhook\",\"type\":\"main\",\"index\":0}]]},\"Read from Google Sheets\":{\"main\":[[{\"node\":\"Build HTML\",\"type\":\"main\",\"index\":0}]]}}}", "assistant\n<think>\n\n</think>\n\n<Thinking>\nAlright, let's start planning this n8n workflow from the ground up, thinking through each step as we go.\n\nThe user prompt clearly states that the workflow needs to be \"triggered by a webhook.\" This immediately tells me what my very first node has to be.\n\n**Node 1: Webhook**\n*   **Type:** `n8n-nodes-base.webhook`\n*   **Type Version:** `1`\n*   **Reasoning:** This is the standard entry point for any workflow that needs to be initiated by an external system or an HTTP request. It's perfect for receiving data from an external source.\n*   **Configuration:**\n    *   I'll set the `httpMethod` to `POST` because it's a common and secure way to send data to a webhook.\n    *   For the `path`, I'll generate a unique identifier, something like `1234567890`, to make the webhook URL distinct and easily identifiable. This path will be appended to the n8n instance's base URL to form the complete webhook URL.\n    *   Crucially, the prompt specifies that the workflow should \"respond to the webhook with the generated HTML.\" This means I need to configure the `responseMode` to `responseNode`. This tells the Webhook node to wait for a subsequent \"Respond to Webhook\" node to send the actual response, rather than sending an immediate default response. This is essential for our final step.\n\nNow that we have our trigger, the next logical step, according to the prompt, is to \"read data from a Google Sheet.\"\n\n**Node 2: Google Sheets**\n*   **Type:** `n8n-nodes-base.googleSheets`\n*   **Type Version:** `1`\n*   **Reasoning:** This node is specifically designed to interact with Google Sheets, allowing us to read, write, or update data. Since our goal is to read data, this is the ideal choice.\n*   **Connection:** This node will connect directly from the `Webhook` node's main output. The data received by the webhook isn't directly used by the Google Sheets node, but the workflow execution flow is sequential.\n*   **Configuration:**\n    *   I'll need to specify the `sheetId`. The prompt provides a specific ID: `1uFISwZJ1rzkOnOSNocX-_n-ASSAznWGdpcPK3_KCvVo`. This tells the node exactly which spreadsheet to access.\n    *   For the `range`, I'll set it to `A:Z`. This is a broad range that typically covers all columns in a sheet, ensuring we capture all available data. If the sheet has a specific named range or a particular sheet tab, that would be specified here, but `A:Z` is a safe default for reading all data.\n    *   Of course, this node will require credentials to access Google Sheets. I'd link it to a pre-configured Google Sheets OAuth2 API credential, which would handle the authentication securely.\n\nWith the data now read from the Google Sheet, the next step is to \"build an HTML table using that data.\" This sounds like a custom data transformation task, which immediately makes me think of a Code node.\n\n**Node 3: Code**\n*   **Type:** `n8n-nodes-base.code`\n*   **Type Version:** `1`\n*   **Reasoning:** The Code node is incredibly powerful for custom logic, data manipulation, and generating specific output formats like HTML. It allows us to write JavaScript to process the incoming data and construct the desired HTML table.\n*   **Connection:** This node will connect from the `Google Sheets` node's main output. The data read from the Google Sheet will be passed as input to this node.\n*   **Configuration:**\n    *   The `jsCode` parameter is where the magic happens. I'll write a JavaScript function that takes the input data (which will be an array of objects, one for each row from the Google Sheet).\n    *   First, I'll define the HTML table structure, including the `<table>` and `<thead>` tags. I'll also add a `<style>` block to make the table look good, setting a border and padding for the cells.\n    *   Then, I'll loop through the input data. For each item (which represents a row from the Google Sheet), I'll extract its `name` and `email` properties (assuming these are the column headers in the sheet). I'll then dynamically create a `<tr>` (table row) and `<td>` (table data) elements for each of these values.\n    *   Finally, I'll add a `<tfoot>` section with a simple message like \"This is the footer\" to demonstrate that part of the table can be customized.\n    *   The crucial part is returning the final HTML string. The `return $input.all().map(...).join('\\n')` line ensures that all the generated HTML rows are combined into a single string, which will be the output of this node.\n\nFinally, the prompt states that the workflow should \"respond to the webhook with the generated HTML as the body and set the 'Content-Type' header to 'text/html; charset=UTF-8'.\" This clearly points to the last node in our sequence.\n\n**Node 4: Respond to Webhook**\n*   **Type:** `n8n-nodes-base.respondToWebhook`\n*   **Type Version:** `1`\n*   **Reasoning:** This node is specifically designed to send a response back to the webhook that triggered the workflow. It's the counterpart to the `responseMode: \"responseNode\"` setting on the initial Webhook node.\n*   **Connection:** This node will connect from the `Code` node's main output. The HTML string generated by the Code node will be passed to this node.\n*   **Configuration:**\n    *   For the `respondWith` parameter, I'll set it to `text`. This tells the node that the response body will be plain text, which is suitable for HTML content.\n    *   The `responseBody` parameter is where I'll dynamically insert the HTML content. I'll use an expression: `={{$node[\"Code\"].json[\"data\"]}}`. This expression tells n8n to take the `data` property from the JSON output of the `Code` node and use it as the body of the response. This is how the HTML table we built in the Code node gets sent back.\n    *   Lastly, I need to set the `Content-Type` header. I'll add a `headerParameter` with `name: \"Content-Type\"` and `value: \"text/html; charset=UTF-8\"`. This is critical for the receiving system to correctly interpret the response as HTML and handle character encoding properly.\n\nAnd that completes the workflow. The data flows from the trigger, through the data source, gets transformed into the desired format, and then the response is sent back.\n</Thinking>\n\n<Json>\n{\"name\": \"Untitled Workflow\", \"nodes\": [{\"parameters\": {\"path\": \"1234567890\", \"options\": {}, \"httpMethod\": \"POST\", \"responseMode\": \"responseNode\"}, \"name\": \"Webhook\", \"type\": \"n8n-nodes-base.webhook\", \"typeVersion\": 1, \"position\": [450, 300]}, {\"parameters\": {\"range\": \"A:Z\", \"sheetId\": \"1uFISwZJ1rzkOnOSNocX-_n-ASSAznWGdpcPK3_KCvVo\", \"options\": {}}, \"name\": \"Google Sheets\", \"type\": \"n8n-nodes-base.googleSheets\", \"typeVersion\": 1, \"position\": [650, 300]}, {\"parameters\": {\"jsCode\": \"const table = '<table border=\\\"1\\\" style=\\\"border-collapse: collapse;\\\">\\n  <thead>\\n    <tr>\\n      <th>Name</th>\\n      <th>Email</th>\\n    </tr>\\n  </thead>\\n  <tbody>\\n';\\n\\nconst style = '<style>\\n  table {\\n    border-collapse: collapse;\\n    width: 100%;\\n  }\\n  th, td {\\n    border: 1px solid #ccc;\\n    padding: 8px;\\n    text-align: left;\\n  }\\n</style>';\\n\\nconst rows = $input.all().map(item => {\\n  const name = item.json.name;\\n  const email = item.json.email;\\n  return `<tr><td>${name}</td><td>${email}</td></tr>`;\\n}).join('\\\\n');\\n\\nconst footer = '<tfoot>\\n  <tr>\\n    <td colspan=\\\"2\\\">This is the footer</td>\\n  </tr>\\n</tfoot>\\n</table>';\\n\\nreturn $input.all().map(item => {\\n  return table + rows + footer;\\n}).join('\\\\n');\\n\"}, \"name\": \"Code\", \"type\": \"n8n-nodes-base.code\", \"typeVersion\": 1, \"position\": [850, 300]}, {\"parameters\": {\"respondWith\": \"text\", \"responseBody\": \"={{$node[\\\"Code\\\"].json[\\\"data\\\"]}}\", \"options\": {\"responseHeaders\": {\"entries\": [{\"name\": \"Content-Type\", \"value\": \"text/html; charset=UTF-8\"}]}}}, \"name\": \"Respond to Webhook\", \"type\": \"n8n-nodes-base.respondToWebhook\", \"typeVersion\": 1, \"position\": [1050, 300]}], \"connections\": {\"Webhook\": {\"main\": [[{\"node\": \"Google Sheets\", \"type\": \"main\", \"index\": 0}]]}, \"Google Sheets\": {\"main\": [[{\"node\": \"Code\", \"type\": \"main\", \"index\": 0}]]}, \"Code\": {\"main\": [[{\"node\": \"Respond to Webhook\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}\n</Json>", "True", "2", "3", "1.0", "4.0", "1.0", "4.0", "4.0", "4.0", "4.0", "4.0", "3.0", "3.0", "3.0", "1.0", null], ["2", "Generate an n8n workflow that listens for a webhook, checks a 'type' value, and either registers a URL or, if the type is not 1, fetches a URL, extracts its title, adds the title and URL to a Notion database, and then replies on Discord.", "{\"name\": \"Untitled Workflow\", \"nodes\": [{\"parameters\": {\"httpMethod\": \"POST\", \"path\": \"298022f3-8561-4275-8105-205907719930\", \"responseMode\": \"responseNode\", \"options\": {}}, \"name\": \"Webhook\", \"type\": \"n8n-nodes-base.webhook\", \"typeVersion\": 2, \"position\": [0, 0]}, {\"parameters\": {\"conditions\": {\"options\": {\"caseSensitive\": true, \"leftValue\": \"\", \"typeValidation\": \"strict\"}, \"conditions\": [{\"id\": \"6834913c-0716-4765-810c-239908474236\", \"leftValue\": \"={{ $json.type }}\", \"rightValue\": \"1\", \"operator\": {\"type\": \"number\", \"operation\": \"equals\"}}], \"combinator\": \"and\"}, \"options\": {}}, \"name\": \"If\", \"type\": \"n8n-nodes-base.if\", \"typeVersion\": 2.2, \"position\": [220, 0]}, {\"parameters\": {\"text\": \"=URL: {{ $json.url }}\", \"select\": \"channel\", \"channelId\": {\"__rl\": true, \"value\": \"1282437863302400000\", \"mode\": \"list\", \"cachedResultName\": \"n8n\"}, \"options\": {}}, \"name\": \"Discord\", \"type\": \"n8n-nodes-base.discord\", \"typeVersion\": 2, \"position\": [440, -100]}, {\"parameters\": {\"url\": \"={{ $json.url }}\", \"options\": {\"redirect\": {\"redirect\": \"follow\"}}}, \"name\": \"HTTP Request\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 4.2, \"position\": [440, 100]}, {\"parameters\": {\"extractionValues\": {\"values\": [{\"key\": \"title\", \"cssSelector\": \"title\", \"returnValue\": \"text\"}]}, \"options\": {}}, \"name\": \"HTML Extract\", \"type\": \"n8n-nodes-base.htmlExtract\", \"typeVersion\": 1, \"position\": [660, 100]}, {\"parameters\": {\"resource\": \"databasePage\", \"databaseId\": {\"__rl\": true, \"value\": \"1282437863302400000\", \"mode\": \"list\", \"cachedResultName\": \"URLs\"}, \"propertiesUi\": {\"propertyValues\": [{\"key\": \"URL|url\", \"title\": \"={{ $json.url }}\"}, {\"key\": \"Title|title\", \"title\": \"={{ $json.title }}\"}]}}, \"name\": \"Notion\", \"type\": \"n8n-nodes-base.notion\", \"typeVersion\": 2.2, \"position\": [880, 100]}, {\"parameters\": {\"text\": \"=URL: {{ $('Webhook').item.json.url }}\", \"select\": \"channel\", \"channelId\": {\"__rl\": true, \"value\": \"1282437863302400000\", \"mode\": \"list\", \"cachedResultName\": \"n8n\"}, \"options\": {}}, \"name\": \"Discord1\", \"type\": \"n8n-nodes-base.discord\", \"typeVersion\": 2, \"position\": [1100, 100]}], \"connections\": {\"Webhook\": {\"main\": [[{\"node\": \"If\", \"type\": \"main\", \"index\": 0}]]}, \"If\": {\"main\": [[{\"node\": \"Discord\", \"type\": \"main\", \"index\": 0}], [{\"node\": \"HTTP Request\", \"type\": \"main\", \"index\": 0}]]}, \"HTTP Request\": {\"main\": [[{\"node\": \"HTML Extract\", \"type\": \"main\", \"index\": 0}]]}, \"HTML Extract\": {\"main\": [[{\"node\": \"Notion\", \"type\": \"main\", \"index\": 0}]]}, \"Notion\": {\"main\": [[{\"node\": \"Discord1\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}", "{\"nodes\":[{\"name\":\"Webhook\",\"type\":\"n8n-nodes-base.webhook\",\"position\":[450,300],\"webhookId\":\"45e2593e-f25d-4be5-9b50-4a7c1e566a9e\",\"parameters\":{\"path\":\"45e2593e-f25d-4be5-9b50-4a7c1e566a9e\",\"options\":{},\"httpMethod\":\"POST\",\"responseMode\":\"lastNode\"},\"typeVersion\":1},{\"name\":\"HTTP Request\",\"type\":\"n8n-nodes-base.httpRequest\",\"position\":[850,200],\"parameters\":{\"url\":\"=https://{{$json[\\\"body\\\"][\\\"data\\\"][\\\"options\\\"][0][\\\"value\\\"]}}\",\"options\":{},\"responseFormat\":\"string\"},\"typeVersion\":1},{\"name\":\"Check type\",\"type\":\"n8n-nodes-base.if\",\"position\":[650,300],\"parameters\":{\"conditions\":{\"number\":[{\"value1\":\"={{$json[\\\"body\\\"][\\\"type\\\"]}}\",\"value2\":1,\"operation\":\"notEqual\"}]}},\"typeVersion\":1},{\"name\":\"Extract Title\",\"type\":\"n8n-nodes-base.htmlExtract\",\"position\":[1050,200],\"parameters\":{\"options\":{},\"extractionValues\":{\"values\":[{\"key\":\"title\",\"cssSelector\":\"title\"}]}},\"typeVersion\":1},{\"name\":\"Add Link to Notion\",\"type\":\"n8n-nodes-base.notion\",\"position\":[1250,200],\"parameters\":{\"resource\":\"databasePage\",\"databaseId\":\"8a1638ce-da33-41b7-8fd9-37a4c272ba95\",\"propertiesUi\":{\"propertyValues\":[{\"key\":\"Name|title\",\"title\":\"={{$json[\\\"title\\\"]}}\"},{\"key\":\"Link|url\",\"urlValue\":\"={{$node[\\\"Check type\\\"].json[\\\"body\\\"][\\\"data\\\"][\\\"options\\\"][0][\\\"value\\\"]}}\"}]}},\"credentials\":{\"notionApi\":\"Notion API Credentials\"},\"typeVersion\":1},{\"name\":\"Reply on Discord\",\"type\":\"n8n-nodes-base.set\",\"position\":[1450,200],\"parameters\":{\"values\":{\"number\":[{\"name\":\"type\",\"value\":4}],\"string\":[{\"name\":\"data.content\",\"value\":\"Added Link to notion\"}]},\"options\":{},\"keepOnlySet\":true},\"typeVersion\":1},{\"name\":\"Register URL\",\"type\":\"n8n-nodes-base.set\",\"position\":[850,410],\"parameters\":{\"values\":{\"number\":[{\"name\":\"type\",\"value\":1}],\"string\":[]},\"options\":{},\"keepOnlySet\":true},\"typeVersion\":1}],\"connections\":{\"Webhook\":{\"main\":[[{\"node\":\"Check type\",\"type\":\"main\",\"index\":0}]]},\"Check type\":{\"main\":[[{\"node\":\"HTTP Request\",\"type\":\"main\",\"index\":0}],[{\"node\":\"Register URL\",\"type\":\"main\",\"index\":0}]]},\"HTTP Request\":{\"main\":[[{\"node\":\"Extract Title\",\"type\":\"main\",\"index\":0}]]},\"Extract Title\":{\"main\":[[{\"node\":\"Add Link to Notion\",\"type\":\"main\",\"index\":0}]]},\"Add Link to Notion\":{\"main\":[[{\"node\":\"Reply on Discord\",\"type\":\"main\",\"index\":0}]]}}}", "assistant\n<think>\n\n</think>\n\n<Thinking>\nOkay, let's start planning this n8n workflow. The goal is to listen for a webhook, check a 'type' value, and then either register a URL or, if the type is not 1, fetch a URL, extract its title, add it to Not<PERSON>, and reply on Discord.\n\nFirst things first, we need a way to receive the initial data. The prompt clearly states \"listens for a webhook,\" so the very first node has to be a Webhook node.\n\n**Node 1: Webhook**\n*   **Type:** `n8n-nodes-base.webhook`\n*   **Type Version:** `2`\n*   **Reasoning:** This is the standard entry point for any workflow that needs to be triggered by an external HTTP request. It provides a unique URL that can be called to start the workflow.\n*   **Configuration:** I'd set its `httpMethod` to `POST` because webhooks typically send data in the request body, and `POST` is suitable for sending data. The `path` would be a unique identifier for this specific webhook, ensuring it's distinct from other webhooks if there are multiple. The `responseMode` is set to `responseNode`, which means we'll handle the response later in the workflow, allowing us to send a custom reply, which is exactly what the prompt asks for.\n\nNow that we have the incoming data, the next step is to check the 'type' value. The prompt says \"checks a 'type' value, and either registers a URL or, if the type is not 1, fetches a URL.\" This sounds like a conditional branching point. An If node is perfect for this.\n\n**Node 2: If**\n*   **Type:** `n8n-nodes-base.if`\n*   **Type Version:** `2.2`\n*   **Reasoning:** This node allows us to create conditional logic. We'll use it to determine which path the workflow should take based on the 'type' value received from the webhook.\n*   **Configuration:** The `conditions` parameter is where we define our logic. We need to check the `type` value. So, I'd set up a condition to check if `{{ $json.type }}` (assuming the 'type' value is in the root of the incoming JSON) `equals` `1`. This will create two output branches: one for when the condition is true (type is 1) and one for when it's false (type is not 1).\n\nLet's follow the \"true\" branch first, which means the type *is* 1. The prompt says \"either registers a URL.\" This implies we need to send some data to an external system. A Discord node seems like a good fit for this, as it's a common way to send messages.\n\n**Node 3: Discord**\n*   **Type:** `n8n-nodes-base.discord`\n*   **Type Version:** `2`\n*   **Reasoning:** This node is designed to interact with the Discord API, allowing us to send messages to a specific channel.\n*   **Configuration:** The `text` field would contain the message we want to send. It's set to `=URL: {{ $json.url }}`. This is a dynamic expression, meaning it will pull the `url` value from the incoming data from the Webhook node and include it in the Discord message. The `select` option is set to `channel`, and a specific `channelId` would be configured here to target the correct Discord channel. A Discord API credential would also be provided to authenticate with Discord.\n\nNow, let's go back to the \"false\" branch of the If node, which means the type is *not* 1. The prompt states \"fetches a URL, extracts its title, adds the title and URL to a Notion database, and then replies on Discord.\" This sequence involves several steps.\n\nFirst, we need to fetch the URL. An HTTP Request node is the standard way to make web requests.\n\n**Node 4: HTTP Request**\n*   **Type:** `n8n-nodes-base.httpRequest`\n*   **Type Version:** `4.2`\n*   **Reasoning:** This node is essential for making HTTP calls to external websites or APIs. We'll use it to retrieve the content of the URL provided in the webhook data.\n*   **Configuration:** The `url` parameter is crucial here. It's set to `={{ $json.url }}`, meaning it will dynamically take the URL from the incoming data. The `options` include `redirect: follow`, which is good practice to ensure we get the final destination of the URL if it redirects. A generic HTTP Request credential would be used for authentication, though for a simple GET request, it might not always be strictly necessary, but it's good to have it configured.\n\nAfter fetching the URL, the next step is to \"extract its title.\" The HTTP Request node will return the HTML content of the page. To extract specific data from HTML, an HTML Extract node is ideal.\n\n**Node 5: HTML Extract**\n*   **Type:** `n8n-nodes-base.htmlExtract`\n*   **Type Version:** `1`\n*   **Reasoning:** This node is specifically designed to parse HTML content and extract data based on CSS selectors or other patterns. We'll use it to find the title of the webpage.\n*   **Configuration:** The `extractionValues` parameter is where we define what to extract. We need the `title` of the page. So, I'd add an extraction value with `key: title` and `cssSelector: title`. This tells the node to look for an HTML element with the tag name `<title>` and extract its text content. The `returnValue: text` ensures we get the actual text of the title, not the HTML element itself.\n\nNow that we have the URL and the extracted title, the prompt says \"adds the title and URL to a Notion database.\" This requires interacting with Notion's API.\n\n**Node 6: Notion**\n*   **Type:** `n8n-nodes-base.notion`\n*   **Type Version:** `2.2`\n*   **Reasoning:** This node provides direct integration with the Notion API, allowing us to create pages, update them, or retrieve data from databases.\n*   **Configuration:** The `resource` is set to `databasePage`, indicating we want to add a new page to a database. The `databaseId` would be configured here to point to the specific Notion database where we want to store this information. For the `propertiesUi`, we need to map our extracted data to Notion's properties. We have two properties: `URL` and `Title`. So, I'd set up two property assignments:\n    *   One for `URL` (a URL type property) with the value `={{ $json.url }}` (from the original webhook data).\n    *   One for `Title` (a title type property) with the value `={{ $json.title }}` (from the output of the HTML Extract node). A Notion API credential would be provided for authentication.\n\nFinally, after adding the data to Notion, the prompt requires us to \"reply on Discord.\" This is similar to the first Discord node, but it's a separate reply.\n\n**Node 7: Discord1**\n*   **Type:** `n8n-nodes-base.discord`\n*   **Type Version:** `2`\n*   **Reasoning:** This is another Discord node, used to send a confirmation message after the Notion entry has been created.\n*   **Configuration:** The `text` field is set to `=URL: {{ $('Webhook').item.json.url }}`. This is a crucial detail: it uses the `$` syntax to reference data from a *previous* node in the workflow, specifically the `Webhook` node. This ensures we're using the original URL that triggered the workflow, not the URL that might have been modified or extracted in intermediate steps. The `select` option is `channel`, and the same `channelId` would be used to send the message to the correct Discord channel. The same Discord API credential would be used for authentication.\n\nNow, let's look at the connections to ensure the flow is correct.\n\n*   The `Webhook` node's `main` output connects to the `If` node's `main` input. This is the standard way to pass data from the trigger to the first logic node.\n*   The `If` node has two outputs:\n    *   Its `true` branch (output 0) connects to the `Discord` node. This means if the type is 1, the workflow will directly send a message to Discord.\n    *   Its `false` branch (output 1) connects to the `HTTP Request` node. This means if the type is *not* 1, the workflow will proceed to fetch the URL.\n*   The `HTTP Request` node's `main` output connects to the `HTML Extract` node. This passes the fetched HTML content to the extraction step.\n*   The `HTML Extract` node's `main` output connects to the `Notion` node. This provides the extracted title and the original URL to Notion.\n*   The `Notion` node's `main` output connects to the `Discord1` node. This sends the confirmation message to Discord after the Notion entry is created.\n\nThis setup ensures that the workflow correctly branches based on the 'type' value, performs the necessary actions for each branch, and provides feedback on Discord as requested.\n</Thinking>\n\n<Json>\n{\"name\": \"Untitled Workflow\", \"nodes\": [{\"parameters\": {\"httpMethod\": \"POST\", \"path\": \"298022f3-8561-4275-8105-205907719930\", \"responseMode\": \"responseNode\", \"options\": {}}, \"name\": \"Webhook\", \"type\": \"n8n-nodes-base.webhook\", \"typeVersion\": 2, \"position\": [0, 0]}, {\"parameters\": {\"conditions\": {\"options\": {\"caseSensitive\": true, \"leftValue\": \"\", \"typeValidation\": \"strict\"}, \"conditions\": [{\"id\": \"6834913c-0716-4765-810c-239908474236\", \"leftValue\": \"={{ $json.type }}\", \"rightValue\": \"1\", \"operator\": {\"type\": \"number\", \"operation\": \"equals\"}}], \"combinator\": \"and\"}, \"options\": {}}, \"name\": \"If\", \"type\": \"n8n-nodes-base.if\", \"typeVersion\": 2.2, \"position\": [220, 0]}, {\"parameters\": {\"text\": \"=URL: {{ $json.url }}\", \"select\": \"channel\", \"channelId\": {\"__rl\": true, \"value\": \"1282437863302400000\", \"mode\": \"list\", \"cachedResultName\": \"n8n\"}, \"options\": {}}, \"name\": \"Discord\", \"type\": \"n8n-nodes-base.discord\", \"typeVersion\": 2, \"position\": [440, -100]}, {\"parameters\": {\"url\": \"={{ $json.url }}\", \"options\": {\"redirect\": {\"redirect\": \"follow\"}}}, \"name\": \"HTTP Request\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 4.2, \"position\": [440, 100]}, {\"parameters\": {\"extractionValues\": {\"values\": [{\"key\": \"title\", \"cssSelector\": \"title\", \"returnValue\": \"text\"}]}, \"options\": {}}, \"name\": \"HTML Extract\", \"type\": \"n8n-nodes-base.htmlExtract\", \"typeVersion\": 1, \"position\": [660, 100]}, {\"parameters\": {\"resource\": \"databasePage\", \"databaseId\": {\"__rl\": true, \"value\": \"1282437863302400000\", \"mode\": \"list\", \"cachedResultName\": \"URLs\"}, \"propertiesUi\": {\"propertyValues\": [{\"key\": \"URL|url\", \"title\": \"={{ $json.url }}\"}, {\"key\": \"Title|title\", \"title\": \"={{ $json.title }}\"}]}}, \"name\": \"Notion\", \"type\": \"n8n-nodes-base.notion\", \"typeVersion\": 2.2, \"position\": [880, 100]}, {\"parameters\": {\"text\": \"=URL: {{ $('Webhook').item.json.url }}\", \"select\": \"channel\", \"channelId\": {\"__rl\": true, \"value\": \"1282437863302400000\", \"mode\": \"list\", \"cachedResultName\": \"n8n\"}, \"options\": {}}, \"name\": \"Discord1\", \"type\": \"n8n-nodes-base.discord\", \"typeVersion\": 2, \"position\": [1100, 100]}], \"connections\": {\"Webhook\": {\"main\": [[{\"node\": \"If\", \"type\": \"main\", \"index\": 0}]]}, \"If\": {\"main\": [[{\"node\": \"Discord\", \"type\": \"main\", \"index\": 0}], [{\"node\": \"HTTP Request\", \"type\": \"main\", \"index\": 0}]]}, \"HTTP Request\": {\"main\": [[{\"node\": \"HTML Extract\", \"type\": \"main\", \"index\": 0}]]}, \"HTML Extract\": {\"main\": [[{\"node\": \"Notion\", \"type\": \"main\", \"index\": 0}]]}, \"Notion\": {\"main\": [[{\"node\": \"Discord1\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}\n</Json>", "True", "1", "6", "1.0", "4.0", "1.0", "7.0", "7.0", "7.0", "7.0", "7.0", "6.0", "6.0", "6.0", "1.0", null], ["3", "Create an n8n workflow that triggers every minute using a Cron node. This trigger should then execute a Python 3 command located at C:\\\\Users\\\\<USER>\\\\Desktop\\\\redrive\\\\master\\\\main.py. The output from this command should then be sent as a message to <PERSON><PERSON> via a webhook, prefixed with 'Hello World!'. Additionally, the output from the executed command should also be passed to a Spreadsheet File node, with the 'Raw Data' option enabled.", "{\"name\": \"Untitled Workflow\", \"nodes\": [{\"parameters\": {\"triggerTimes\": {\"item\": [{\"mode\": \"everyMinute\"}]}}, \"name\": \"Cron\", \"type\": \"n8n-nodes-base.cron\", \"typeVersion\": 1, \"position\": [450, 300]}, {\"parameters\": {\"command\": \"python3 C:\\\\Users\\\\<USER>\\\\Desktop\\\\redrive\\\\master\\\\main.py\"}, \"name\": \"Execute Command\", \"type\": \"n8n-nodes-base.executeCommand\", \"typeVersion\": 1, \"position\": [650, 300]}, {\"parameters\": {\"text\": \"=Hello World! {{ $json.data }}\", \"webhookUri\": \"https://discord.com/api/webhooks/1234567890123456789/AbcDeFgHiJkLmNoPqRsTuVwXyZ\"}, \"name\": \"Discord\", \"type\": \"n8n-nodes-base.discord\", \"typeVersion\": 1, \"position\": [850, 200]}, {\"parameters\": {\"operation\": \"toFile\", \"fileName\": \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\redrive\\\\master\\\\output.xlsx\", \"options\": {\"rawData\": true}}, \"name\": \"Spreadsheet File\", \"type\": \"n8n-nodes-base.spreadsheetFile\", \"typeVersion\": 1, \"position\": [850, 400]}], \"connections\": {\"Cron\": {\"main\": [[{\"node\": \"Execute Command\", \"type\": \"main\", \"index\": 0}]]}, \"Execute Command\": {\"main\": [[{\"node\": \"Discord\", \"type\": \"main\", \"index\": 0}, {\"node\": \"Spreadsheet File\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}", "{\"nodes\":[{\"name\":\"Discord\",\"type\":\"n8n-nodes-base.discord\",\"position\":[760,160],\"parameters\":{\"text\":\"=Hello World! {{$node[\\\"Execute Command\\\"].json[\\\"stdout\\\"]}}\",\"webhookUri\":\"https://discord.com/api/webhooks/958040440559505478/5YIYFvnNDdcUxTuAxhTRzzulTGmNPLN978XukoOc06kZKVVzzz7BaG_iUUWVw8QP-edP\"},\"typeVersion\":1},{\"name\":\"Execute Command\",\"type\":\"n8n-nodes-base.executeCommand\",\"position\":[400,160],\"parameters\":{\"command\":\"python3 C:\\\\Users\\\\<USER>\\\\Desktop\\\\redrive\\\\master\\\\main.py\"},\"typeVersion\":1},{\"name\":\"<PERSON>ron\",\"type\":\"n8n-nodes-base.cron\",\"position\":[240,460],\"parameters\":{\"triggerTimes\":{\"item\":[{\"mode\":\"everyMinute\"}]}},\"typeVersion\":1},{\"name\":\"Spreadsheet File\",\"type\":\"n8n-nodes-base.spreadsheetFile\",\"position\":[600,460],\"parameters\":{\"options\":{\"rawData\":true}},\"typeVersion\":1}],\"connections\":{\"Cron\":{\"main\":[[{\"node\":\"Execute Command\",\"type\":\"main\",\"index\":0}]]},\"Execute Command\":{\"main\":[[{\"node\":\"Discord\",\"type\":\"main\",\"index\":0},{\"node\":\"Spreadsheet File\",\"type\":\"main\",\"index\":0}]]}}}", "assistant\n<think>\n\n</think>\n\n<Thinking>\nOkay, let's start planning this n8n workflow. The user wants something that triggers regularly, runs a Python script, sends its output to Discord, and also saves that output to a spreadsheet.\n\nFirst things first, we need a way to kick off this whole process on a schedule. The prompt clearly states \"triggers every minute using a Cron node.\" So, the very first node I'd reach for is a **Cron** node. This is perfect for time-based triggers. I'd set its type to `n8n-nodes-base.cron` and its `typeVersion` to `1`. For its configuration, I'd go into the parameters and set the `triggerTimes` to `everyMinute`. This ensures our workflow starts precisely when needed.\n\nNow that we have our trigger, what's the next step? The prompt says the trigger \"should then execute a Python 3 command located at C:\\\\Users\\\\<USER>\\\\Desktop\\\\redrive\\\\master\\\\main.py.\" This immediately tells me I need a node that can run external commands. The **Execute Command** node is exactly what we need for this. I'd add a node of type `n8n-nodes-base.executeCommand` with `typeVersion` `1`. I'd connect the `Cron` node's main output to the `Execute Command` node's main input. For the `Execute Command` node's parameters, I'd specify the `command` as `python3 C:\\\\Users\\\\<USER>\\\\Desktop\\\\redrive\\\\master\\\\main.py`. This directly addresses the requirement to run the specific Python script.\n\nAfter executing the command, we have two distinct paths for the output: sending it to Discord and saving it to a spreadsheet. This means the `Execute Command` node will need to connect to two different subsequent nodes.\n\nLet's tackle the Discord part first. The prompt states the output \"should then be sent as a message to Discord via a webhook, prefixed with 'Hello World!'.\" For sending messages to Discord, the **Discord** node is the go-to. I'd add a node of type `n8n-nodes-base.discord` with `typeVersion` `1`. I'd connect the `Execute Command` node's main output to this `Discord` node's main input. For its configuration, I'd set the `text` parameter to `=Hello World! {{ $json.data }}`. This is crucial because it uses an expression to dynamically include the output from the `Execute Command` node. The `{{ $json.data }}` part assumes that the output of the executed command is available in the `data` field of the JSON output from the `Execute Command` node. I'd also make sure to configure the `webhookUri` parameter with the specific Discord webhook URL, which would be provided by the user. Of course, a Discord API credential would also need to be set up and linked to this node.\n\nNow, for the second path from the `Execute Command` node: \"The output from this command should also be passed to a Spreadsheet File node, with the 'Raw Data' option enabled.\" This points directly to the **Spreadsheet File** node. I'd add a node of type `n8n-nodes-base.spreadsheetFile` with `typeVersion` `1`. I'd connect the `Execute Command` node's main output to this `Spreadsheet File` node's main input. For its parameters, I'd set the `operation` to `toFile`. This tells the node to take the incoming data and write it to a file. Crucially, I'd enable the `rawData` option, as specified in the prompt. This ensures that the raw output from the Python script is written directly to the spreadsheet file without any additional formatting or parsing by the node itself. The `fileName` parameter would be set to a specific path, like `C:\\\\Users\\\\<USER>\\\\Desktop\\\\redrive\\\\master\\\\output.xlsx`, where the user wants the spreadsheet saved. This completes the two parallel paths from the command execution.\n</Thinking>\n\n<Json>\n{\"name\": \"Untitled Workflow\", \"nodes\": [{\"parameters\": {\"triggerTimes\": {\"item\": [{\"mode\": \"everyMinute\"}]}}, \"name\": \"Cron\", \"type\": \"n8n-nodes-base.cron\", \"typeVersion\": 1, \"position\": [450, 300]}, {\"parameters\": {\"command\": \"python3 C:\\\\Users\\\\<USER>\\\\Desktop\\\\redrive\\\\master\\\\main.py\"}, \"name\": \"Execute Command\", \"type\": \"n8n-nodes-base.executeCommand\", \"typeVersion\": 1, \"position\": [650, 300]}, {\"parameters\": {\"text\": \"=Hello World! {{ $json.data }}\", \"webhookUri\": \"https://discord.com/api/webhooks/1234567890123456789/AbcDeFgHiJkLmNoPqRsTuVwXyZ\"}, \"name\": \"Discord\", \"type\": \"n8n-nodes-base.discord\", \"typeVersion\": 1, \"position\": [850, 200]}, {\"parameters\": {\"operation\": \"toFile\", \"fileName\": \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\redrive\\\\master\\\\output.xlsx\", \"options\": {\"rawData\": true}}, \"name\": \"Spreadsheet File\", \"type\": \"n8n-nodes-base.spreadsheetFile\", \"typeVersion\": 1, \"position\": [850, 400]}], \"connections\": {\"Cron\": {\"main\": [[{\"node\": \"Execute Command\", \"type\": \"main\", \"index\": 0}]]}, \"Execute Command\": {\"main\": [[{\"node\": \"Discord\", \"type\": \"main\", \"index\": 0}, {\"node\": \"Spreadsheet File\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}\n</Json>", "True", "1", "10", "1.0", "4.0", "1.0", "4.0", "4.0", "4.0", "4.0", "4.0", "3.0", "3.0", "3.0", "1.0", null], ["4", "Generate an n8n workflow that uses a Hubspot Trigger to listen for events, configured with appId 'dghert3' and a maximum of 5 concurrent requests, using the 'hubspot_trigger' credential.", "{\"name\": \"Untitled Workflow\", \"nodes\": [{\"parameters\": {\"appId\": \"dghert3\", \"maxConcurrency\": 5}, \"name\": \"Hubspot Trigger\", \"type\": \"n8n-nodes-base.hubspotTrigger\", \"typeVersion\": 1, \"position\": [380, 260]}], \"connections\": {}, \"active\": false}", "{\"nodes\":[{\"name\":\"Hubspot Trigger\",\"type\":\"n8n-nodes-base.hubspotTrigger\",\"position\":[700,260],\"webhookId\":\"9fe8c037-be4f-4809-a7c2-96e509bfc52e\",\"parameters\":{\"appId\":\"dghert3\",\"additionalFields\":{\"maxConcurrentRequests\":5}},\"credentials\":{\"hubspotDeveloperApi\":\"hubspot_trigger\"},\"typeVersion\":1}],\"connections\":{}}", "assistant\n<think>\n\n</think>\n\n<Thinking>\nOkay, let's start planning this n8n workflow. The user's request is pretty clear: we need to set up a workflow that listens for events from Hubspot, specifically using a Hubspot Trigger node. They've also given us some specific configurations for the trigger itself and the credential it needs to use.\n\nSo, the very first thing that comes to mind is, \"How do we even start listening for events from Hubspot?\" The answer, of course, is a trigger node. And since the request explicitly mentions a \"Hubspot Trigger,\" that's exactly what we'll need to kick things off.\n\nI'd drag out a **Hubspot Trigger** node. Looking at the JSON, its `type` is `n8n-nodes-base.hubspotTrigger` and its `typeVersion` is `1`. This is the standard node for this purpose, and version 1 is what we're working with here. It's designed specifically to integrate with Hubspot's event-driven API, which is perfect for our goal.\n\nNow, let's think about how to configure this Hubspot Trigger node. The user prompt gives us a few key pieces of information:\n1.  It needs to be configured with an `appId` of 'dghert3'.\n2.  It should handle a `maximum of 5 concurrent requests`.\n3.  It needs to use the `hubspot_trigger` credential.\n\nSo, within the Hubspot Trigger node's parameters, I'd go to the `appId` field and input 'dghert3'. This is crucial because Hubspot uses app IDs to identify and manage different integrations. Without the correct app ID, the trigger wouldn't know which specific integration to listen for events on.\n\nNext, for the `maximum of 5 concurrent requests`, I'd look for a setting related to concurrency or parallelism. The JSON shows `maxConcurrency: 5`. This is a really important setting for a trigger node. It means that if multiple events come in simultaneously, the workflow will process up to 5 of them at the same time. This helps prevent overwhelming the system or the Hubspot API if there's a sudden spike in events. It's a good practice for managing resource usage and ensuring the workflow can handle a reasonable load.\n\nFinally, for the `hubspot_trigger` credential, I'd navigate to the `credentials` section of the Hubspot Trigger node. Here, I'd select the appropriate credential. The JSON indicates `hubspotTriggerApi: \"hubspot_trigger\"`. This means that a pre-configured credential named \"hubspot_trigger\" would be selected. This credential would contain the necessary authentication details, like an API key or OAuth token, that allow n8n to securely connect to Hubspot and register the trigger. It's essential for the node to actually be able to communicate with Hubspot.\n\nAt this point, the Hubspot Trigger node is fully configured according to the user's specifications. It's set up to listen for events, it has a specific app ID, a concurrency limit, and it's linked to the correct credential. This node would be the starting point of our workflow, receiving data from Hubspot whenever an event occurs that matches its configuration. There are no other nodes in the provided JSON, which implies that this is a very simple, single-node workflow, likely designed to just receive the event data and perhaps pass it along to another node (which isn't shown here) for further processing. But for now, the Hubspot Trigger is the complete solution for the stated requirement.\n</Thinking>\n\n<Json>\n{\"name\": \"Untitled Workflow\", \"nodes\": [{\"parameters\": {\"appId\": \"dghert3\", \"maxConcurrency\": 5}, \"name\": \"Hubspot Trigger\", \"type\": \"n8n-nodes-base.hubspotTrigger\", \"typeVersion\": 1, \"position\": [380, 260]}], \"connections\": {}, \"active\": false}\n</Json>", "True", "1", "7", "1.0", "4.0", "1.0", "1.0", "1.0", "1.0", "1.0", "1.0", "0.0", "0.0", "0.0", "1.0", null]], "shape": {"columns": 20, "rows": 5}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>prompt</th>\n", "      <th>generated_json</th>\n", "      <th>ground_truth</th>\n", "      <th>generated_text</th>\n", "      <th>json_valid</th>\n", "      <th>tries</th>\n", "      <th>llm_score</th>\n", "      <th>json_parsed</th>\n", "      <th>top_level_keys_present</th>\n", "      <th>workflow_name_valid</th>\n", "      <th>num_nodes</th>\n", "      <th>num_nodes_with_valid_type</th>\n", "      <th>num_nodes_with_valid_version</th>\n", "      <th>num_nodes_with_valid_structure</th>\n", "      <th>num_nodes_with_parameters</th>\n", "      <th>num_connections</th>\n", "      <th>num_connections_with_valid_structure</th>\n", "      <th>num_connections_with_valid_target_node</th>\n", "      <th>active_field_boolean</th>\n", "      <th>evaluation_results</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Create an n8n workflow that sends a daily vega...</td>\n", "      <td>{\"name\": \"Send daily vegan recipes to Telegram...</td>\n", "      <td>{\"nodes\":[{\"name\":\"<PERSON>ron\",\"type\":\"n8n-nodes-bas...</td>\n", "      <td>assistant\\n&lt;think&gt;\\n\\n&lt;/think&gt;\\n\\n&lt;Thinking&gt;\\n...</td>\n", "      <td>True</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>1.0</td>\n", "      <td>4.0</td>\n", "      <td>1.0</td>\n", "      <td>12.0</td>\n", "      <td>12.0</td>\n", "      <td>12.0</td>\n", "      <td>12.0</td>\n", "      <td>12.0</td>\n", "      <td>10.0</td>\n", "      <td>10.0</td>\n", "      <td>10.0</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Create an n8n workflow that is triggered by a ...</td>\n", "      <td>{\"name\": \"Untitled Workflow\", \"nodes\": [{\"para...</td>\n", "      <td>{\"nodes\":[{\"name\":\"Read from Google Sheets\",\"t...</td>\n", "      <td>assistant\\n&lt;think&gt;\\n\\n&lt;/think&gt;\\n\\n&lt;Thinking&gt;\\n...</td>\n", "      <td>True</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "      <td>1.0</td>\n", "      <td>4.0</td>\n", "      <td>1.0</td>\n", "      <td>4.0</td>\n", "      <td>4.0</td>\n", "      <td>4.0</td>\n", "      <td>4.0</td>\n", "      <td>4.0</td>\n", "      <td>3.0</td>\n", "      <td>3.0</td>\n", "      <td>3.0</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Generate an n8n workflow that listens for a we...</td>\n", "      <td>{\"name\": \"Untitled Workflow\", \"nodes\": [{\"para...</td>\n", "      <td>{\"nodes\":[{\"name\":\"Webhook\",\"type\":\"n8n-nodes-...</td>\n", "      <td>assistant\\n&lt;think&gt;\\n\\n&lt;/think&gt;\\n\\n&lt;Thinking&gt;\\n...</td>\n", "      <td>True</td>\n", "      <td>1</td>\n", "      <td>6</td>\n", "      <td>1.0</td>\n", "      <td>4.0</td>\n", "      <td>1.0</td>\n", "      <td>7.0</td>\n", "      <td>7.0</td>\n", "      <td>7.0</td>\n", "      <td>7.0</td>\n", "      <td>7.0</td>\n", "      <td>6.0</td>\n", "      <td>6.0</td>\n", "      <td>6.0</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Create an n8n workflow that triggers every min...</td>\n", "      <td>{\"name\": \"Untitled Workflow\", \"nodes\": [{\"para...</td>\n", "      <td>{\"nodes\":[{\"name\":\"Discord\",\"type\":\"n8n-nodes-...</td>\n", "      <td>assistant\\n&lt;think&gt;\\n\\n&lt;/think&gt;\\n\\n&lt;Thinking&gt;\\n...</td>\n", "      <td>True</td>\n", "      <td>1</td>\n", "      <td>10</td>\n", "      <td>1.0</td>\n", "      <td>4.0</td>\n", "      <td>1.0</td>\n", "      <td>4.0</td>\n", "      <td>4.0</td>\n", "      <td>4.0</td>\n", "      <td>4.0</td>\n", "      <td>4.0</td>\n", "      <td>3.0</td>\n", "      <td>3.0</td>\n", "      <td>3.0</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Generate an n8n workflow that uses a Hubspot T...</td>\n", "      <td>{\"name\": \"Untitled Workflow\", \"nodes\": [{\"para...</td>\n", "      <td>{\"nodes\":[{\"name\":\"Hubspot Trigger\",\"type\":\"n8...</td>\n", "      <td>assistant\\n&lt;think&gt;\\n\\n&lt;/think&gt;\\n\\n&lt;Thinking&gt;\\n...</td>\n", "      <td>True</td>\n", "      <td>1</td>\n", "      <td>7</td>\n", "      <td>1.0</td>\n", "      <td>4.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                              prompt  \\\n", "0  Create an n8n workflow that sends a daily vega...   \n", "1  Create an n8n workflow that is triggered by a ...   \n", "2  Generate an n8n workflow that listens for a we...   \n", "3  Create an n8n workflow that triggers every min...   \n", "4  Generate an n8n workflow that uses a Hubspot T...   \n", "\n", "                                      generated_json  \\\n", "0  {\"name\": \"Send daily vegan recipes to Telegram...   \n", "1  {\"name\": \"Untitled Workflow\", \"nodes\": [{\"para...   \n", "2  {\"name\": \"Untitled Workflow\", \"nodes\": [{\"para...   \n", "3  {\"name\": \"Untitled Workflow\", \"nodes\": [{\"para...   \n", "4  {\"name\": \"Untitled Workflow\", \"nodes\": [{\"para...   \n", "\n", "                                        ground_truth  \\\n", "0  {\"nodes\":[{\"name\":\"Cron\",\"type\":\"n8n-nodes-bas...   \n", "1  {\"nodes\":[{\"name\":\"Read from Google Sheets\",\"t...   \n", "2  {\"nodes\":[{\"name\":\"Webhook\",\"type\":\"n8n-nodes-...   \n", "3  {\"nodes\":[{\"name\":\"Discord\",\"type\":\"n8n-nodes-...   \n", "4  {\"nodes\":[{\"name\":\"Hubspot Trigger\",\"type\":\"n8...   \n", "\n", "                                      generated_text  json_valid  tries  \\\n", "0  assistant\\n<think>\\n\\n</think>\\n\\n<Thinking>\\n...        True      1   \n", "1  assistant\\n<think>\\n\\n</think>\\n\\n<Thinking>\\n...        True      2   \n", "2  assistant\\n<think>\\n\\n</think>\\n\\n<Thinking>\\n...        True      1   \n", "3  assistant\\n<think>\\n\\n</think>\\n\\n<Thinking>\\n...        True      1   \n", "4  assistant\\n<think>\\n\\n</think>\\n\\n<Thinking>\\n...        True      1   \n", "\n", "   llm_score  json_parsed  top_level_keys_present  workflow_name_valid  \\\n", "0          3          1.0                     4.0                  1.0   \n", "1          3          1.0                     4.0                  1.0   \n", "2          6          1.0                     4.0                  1.0   \n", "3         10          1.0                     4.0                  1.0   \n", "4          7          1.0                     4.0                  1.0   \n", "\n", "   num_nodes  num_nodes_with_valid_type  num_nodes_with_valid_version  \\\n", "0       12.0                       12.0                          12.0   \n", "1        4.0                        4.0                           4.0   \n", "2        7.0                        7.0                           7.0   \n", "3        4.0                        4.0                           4.0   \n", "4        1.0                        1.0                           1.0   \n", "\n", "   num_nodes_with_valid_structure  num_nodes_with_parameters  num_connections  \\\n", "0                            12.0                       12.0             10.0   \n", "1                             4.0                        4.0              3.0   \n", "2                             7.0                        7.0              6.0   \n", "3                             4.0                        4.0              3.0   \n", "4                             1.0                        1.0              0.0   \n", "\n", "   num_connections_with_valid_structure  \\\n", "0                                  10.0   \n", "1                                   3.0   \n", "2                                   6.0   \n", "3                                   3.0   \n", "4                                   0.0   \n", "\n", "   num_connections_with_valid_target_node  active_field_boolean  \\\n", "0                                    10.0                   1.0   \n", "1                                     3.0                   1.0   \n", "2                                     6.0                   1.0   \n", "3                                     3.0                   1.0   \n", "4                                     0.0                   1.0   \n", "\n", "  evaluation_results  \n", "0                NaN  \n", "1                NaN  \n", "2                NaN  \n", "3                NaN  \n", "4                NaN  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": 4, "id": "f9419ccf", "metadata": {}, "outputs": [], "source": ["df[\"percent_node_with_valid_type\"] = df[\"num_nodes_with_valid_type\"] / df[\"num_nodes\"] * 100\n", "df[\"percent_node_with_valid_version\"] = df[\"num_nodes_with_valid_version\"] / df[\"num_nodes\"] * 100\n", "df[\"percent_node_with_valid_structure\"] = df[\"num_nodes_with_valid_structure\"] / df[\"num_nodes\"] * 100\n", "df[\"percent_connections_with_valid_structure\"] = df[\"num_connections_with_valid_structure\"] / df[\"num_connections\"] * 100\n", "df[\"percent_connections_with_valid_target_node\"] = df[\"num_connections_with_valid_target_node\"] / df[\"num_connections\"] * 100\n", "df[\"percent_node_with_parameters\"] = df[\"num_nodes_with_parameters\"] / df[\"num_nodes\"] * 100"]}, {"cell_type": "code", "execution_count": 5, "id": "298bdacb", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='json_valid', ylabel='Count'>"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"json_valid\")"]}, {"cell_type": "code", "execution_count": 18, "id": "3a6dd530", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.int64(27)"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"json_valid\"].sum()"]}, {"cell_type": "code", "execution_count": 17, "id": "8825d978", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.int64(18)"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["(df[\"llm_score\"]>=7).sum()"]}, {"cell_type": "code", "execution_count": 8, "id": "c1ecdd0c", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='top_level_keys_present', ylabel='Count'>"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"top_level_keys_present\")"]}, {"cell_type": "code", "execution_count": 9, "id": "3b2bfd01", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='workflow_name_valid', ylabel='Count'>"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"workflow_name_valid\")"]}, {"cell_type": "code", "execution_count": 10, "id": "a18db11e", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_type', ylabel='Count'>"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_type\")"]}, {"cell_type": "code", "execution_count": 11, "id": "3c7c398e", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_version', ylabel='Count'>"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_version\")"]}, {"cell_type": "code", "execution_count": 12, "id": "4c46bdcf", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_structure', ylabel='Count'>"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_structure\")"]}, {"cell_type": "code", "execution_count": 13, "id": "44883d67", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_parameters', ylabel='Count'>"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_parameters\")"]}, {"cell_type": "code", "execution_count": 14, "id": "048db9e6", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_connections_with_valid_structure', ylabel='Count'>"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_connections_with_valid_structure\")"]}, {"cell_type": "code", "execution_count": 15, "id": "24225a83", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_connections_with_valid_target_node', ylabel='Count'>"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_connections_with_valid_target_node\")"]}, {"cell_type": "code", "execution_count": 16, "id": "4ed22d8e", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='active_field_boolean', ylabel='Count'>"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"active_field_boolean\")"]}, {"cell_type": "code", "execution_count": null, "id": "9bb20feb", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}