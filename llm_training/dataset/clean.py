import json

import pandas as pd


# === Clean each node to required fields ===
def clean_node(node):
    return {
        "parameters": node.get("parameters", {}),
        "name": node.get("name", ""),
        "type": node.get("type", ""),
        "typeVersion": node.get("typeVersion", 1),
        "position": node.get("position", [0, 0]),
    }


# === Main JSON cleaner ===
def clean_json_entry(entry):
    try:
        data = json.loads(entry) if isinstance(entry, str) else entry
        cleaned = {
            "name": data.get("name", "Untitled Workflow"),
            "nodes": [clean_node(n) for n in data.get("nodes", [])],
            "connections": data.get("connections", {}),
            "active": False,
        }
        return json.dumps(cleaned)
    except Exception as e:
        print("Error cleaning entry:", e)
        return None


# === Example usage ===
df = pd.read_json(
    "final_dataset_with_thinking.jsonl", lines=True
)  # or pd.read_csv(...)
df["json"] = df["json"].apply(clean_json_entry)
df = df[df["json"].notnull()]
df.to_json("cleaned_dataset.jsonl", lines=True, orient="records", force_ascii=False)
