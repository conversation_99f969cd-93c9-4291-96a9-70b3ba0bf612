# Improved n8n Workflow Reward Function
import json
from typing import Any, Dict, Optional, Set, Tuple


# Load valid node types and versions from the text file
def load_node_types_and_versions():
    """Parse the n8n_nodes_list.txt file to extract node types and their valid versions."""
    node_types = set()
    node_versions = {}

    try:
        with open("n8n_nodes_list.txt", "r") as f:
            for line in f:
                line = line.strip()
                if line.startswith("- ") and " : " in line:
                    # Parse format: "- node_type : version1,version2,version3"
                    parts = line[2:].split(
                        " : ", 1
                    )  # Remove "- " prefix and split on " : "
                    if len(parts) == 2:
                        node_type = parts[0].strip()
                        versions_str = parts[1].strip()

                        # Parse versions (comma-separated)
                        versions = [v.strip() for v in versions_str.split(",")]

                        node_types.add(node_type)
                        node_versions[node_type] = versions

    except FileNotFoundError:
        print("Warning: n8n_nodes_list.txt not found, falling back to empty lists")

    return node_types, node_versions


VALID_N8N_NODE_TYPES, VALID_N8N_NODE_VERSIONS = load_node_types_and_versions()


# Reward weights for different components
REWARDS = {
    "json_parsing": 0.05,
    "top_level_keys": 0.15,
    "workflow_name": 0.05,
    "nodes_list": 0.05,
    "nodes_non_empty": 0.05,
    "node_structure": 0.15,
    "node_types_valid": 0.15,
    "node_versions_valid": 0.10,
    "node_parameters": 0.10,
    "connections_dict": 0.05,
    "connection_structure": 0.05,
    "connection_validity": 0.05,
    "active_boolean": 0.05,
}

MAX_POSSIBLE_REWARD = sum(REWARDS.values())


def validate_node_structure(node: Dict[str, Any]) -> float:
    """
    Validate the structure of a single node.
    Returns a score between 0.0 and 1.0.
    """
    if not isinstance(node, dict):
        return 0.0

    score = 0.0
    required_fields = ["id", "name", "type", "typeVersion", "position"]

    for field in required_fields:
        if field in node:
            if (
                field == "position"
                and isinstance(node[field], list)
                and len(node[field]) >= 2
            ):
                score += 0.2
            elif field == "typeVersion" and isinstance(node[field], (int, float)):
                score += 0.2
            elif (
                field in ["id", "name", "type"]
                and isinstance(node[field], str)
                and node[field].strip()
            ):
                score += 0.2

    return score


def validate_node_version(node_type: str, node_type_version: Any) -> float:
    """
    Validate if the node type version is valid for the given node type.
    Returns 1.0 if valid, 0.5 if type exists but version unknown, 0.0 if invalid.
    """
    if not isinstance(node_type, str):
        return 0.0

    # Check if node type exists in our valid types
    if node_type not in VALID_N8N_NODE_TYPES:
        return 0.0

    valid_versions = VALID_N8N_NODE_VERSIONS[node_type]

    # Convert version to string for comparison
    version_str = str(node_type_version) if node_type_version is not None else ""

    # Check if the version is in the list of valid versions
    if version_str in valid_versions:
        return 1.0

    # If version is provided but not in valid list, give partial credit
    if isinstance(node_type_version, (int, float, str)):
        return 0.3

    return 0.0


def validate_connections_structure(
    connections: Dict[str, Any], node_names: Set[str]
) -> Tuple[float, float, int]:
    """
    Validate the connections structure.
    Returns (structure_score, validity_score, total_connections).
    """
    if not isinstance(connections, dict):
        return 0.0, 0.0, 0

    # If no connections, return perfect score (empty connections are valid)
    if len(connections) == 0:
        return 1.0, 1.0, 0

    structure_score = 0.0
    validity_score = 0.0
    total_connections = 0
    valid_source_nodes = 0

    for source_node, connection_data in connections.items():
        if not isinstance(connection_data, dict):
            continue

        # Check if source node exists
        source_exists = source_node in node_names
        if source_exists:
            valid_source_nodes += 1

        for connection_type, connection_list in connection_data.items():
            if not isinstance(connection_list, list):
                continue

            for connection_group in connection_list:
                if not isinstance(connection_group, list):
                    continue

                for connection in connection_group:
                    total_connections += 1

                    if isinstance(connection, dict):
                        structure_score += 0.3  # Credit for being a dict

                        # Check required fields
                        if (
                            "node" in connection
                            and "type" in connection
                            and "index" in connection
                        ):
                            structure_score += 0.4  # Credit for having required fields

                            # Check if target node exists
                            target_node = connection.get("node")
                            if (
                                isinstance(target_node, str)
                                and target_node in node_names
                            ):
                                validity_score += (
                                    1.0  # Full credit for valid connection
                                )

    # Normalize scores
    if total_connections > 0:
        structure_score = min(structure_score / total_connections, 1.0)
        validity_score = min(validity_score / total_connections, 1.0)
    else:
        # No connections found but connections dict exists - partial credit
        structure_score = 0.5
        validity_score = 0.5

    # Factor in source node validity
    if len(connections) > 0:
        source_validity = valid_source_nodes / len(connections)
        validity_score = (validity_score + source_validity) / 2

    return structure_score, validity_score, total_connections


def reward_n8n_workflow(workflow_json_string: str) -> float:
    """
    Improved reward function for n8n workflow validation.
    Returns a score between 0.0 and 1.0 based on workflow quality.

    Args:
        workflow_json_string: JSON string or dict of the workflow to evaluate

    Returns:
        Float score between 0.0 and 1.0
    """
    reward = 0.0

    # 1. JSON Parsing
    try:
        workflow = (
            json.loads(workflow_json_string)
            if isinstance(workflow_json_string, str)
            else workflow_json_string
        )
        reward += REWARDS["json_parsing"]
    except Exception:
        return 0.0

    if not isinstance(workflow, dict):
        return 0.0

    # 2. Top-level structure validation
    required_keys = ["name", "nodes", "connections", "active"]
    present_keys = [key for key in required_keys if key in workflow]
    reward += REWARDS["top_level_keys"] * (len(present_keys) / len(required_keys))

    # 3. Workflow name validation
    workflow_name = workflow.get("name", "")
    if isinstance(workflow_name, str) and workflow_name.strip():
        reward += REWARDS["workflow_name"]

    # 4. Nodes validation
    nodes = workflow.get("nodes", [])
    node_names = set()

    if isinstance(nodes, list):
        reward += REWARDS["nodes_list"]

        if nodes:
            reward += REWARDS["nodes_non_empty"]
            # Collect node names, filtering out None values
            node_names = {
                node.get("name")
                for node in nodes
                if isinstance(node.get("name"), str) and node.get("name").strip()
            }
            # Also collect node IDs as fallback for connections
            node_ids = {
                node.get("id")
                for node in nodes
                if isinstance(node.get("id"), str) and node.get("id").strip()
            }
            # Use both names and IDs for connection validation
            node_identifiers = node_names.union(node_ids)

            # Detailed node validation
            total_structure_score = 0.0
            total_type_score = 0.0
            total_version_score = 0.0
            total_parameter_score = 0.0

            for node in nodes:
                # Structure validation
                total_structure_score += validate_node_structure(node)

                # Type validation
                node_type = node.get("type")
                if isinstance(node_type, str) and node_type in VALID_N8N_NODE_TYPES:
                    total_type_score += 1.0
                elif isinstance(node_type, str):
                    total_type_score += 0.1  # Small credit for having a type

                # Version validation
                node_type_version = node.get("typeVersion")
                if isinstance(node_type, str):
                    total_version_score += validate_node_version(
                        node_type, node_type_version
                    )

                # Parameter validation
                parameters = node.get("parameters")
                if isinstance(parameters, dict):
                    total_parameter_score += 1.0
                elif parameters is not None:
                    total_parameter_score += 0.3  # Partial credit

            # Normalize by number of nodes (with safety check)
            if len(nodes) > 0:
                reward += REWARDS["node_structure"] * (
                    total_structure_score / len(nodes)
                )
                reward += REWARDS["node_types_valid"] * (total_type_score / len(nodes))
                reward += REWARDS["node_versions_valid"] * (
                    total_version_score / len(nodes)
                )
                reward += REWARDS["node_parameters"] * (
                    total_parameter_score / len(nodes)
                )

    # 5. Connections validation
    connections = workflow.get("connections", {})

    if isinstance(connections, dict):
        reward += REWARDS["connections_dict"]

        # Use node_identifiers if available, otherwise fall back to node_names
        connection_node_refs = (
            node_identifiers if "node_identifiers" in locals() else node_names
        )
        structure_score, validity_score, _ = validate_connections_structure(
            connections, connection_node_refs
        )

        reward += REWARDS["connection_structure"] * structure_score
        reward += REWARDS["connection_validity"] * validity_score

    # 6. Active field validation
    if "active" in workflow and isinstance(workflow["active"], bool):
        reward += REWARDS["active_boolean"]

    # Final score calculation
    final_score = reward / MAX_POSSIBLE_REWARD
    return max(0.0, min(final_score, 1.0))


# Example usage and testing
if __name__ == "__main__":
    # Test with dataset
    try:
        with open("final_dataset.jsonl", "r") as f:
            jsons = f.readlines()

        rewards = []
        for data in jsons:
            data = json.loads(data)
            data_json = json.loads(data["json"])
            rewards.append(reward_n8n_workflow(data_json))

        with open("improved_rewards.json", "w") as f:
            json.dump(rewards, f)

        print(f"Processed {len(rewards)} workflows")
        print(f"Average reward: {sum(rewards) / len(rewards):.3f}")
        print(f"Min reward: {min(rewards):.3f}")
        print(f"Max reward: {max(rewards):.3f}")

    except FileNotFoundError:
        print(
            "Dataset files not found. Please ensure 'final_dataset.jsonl' and 'node_list.json' exist."
        )
