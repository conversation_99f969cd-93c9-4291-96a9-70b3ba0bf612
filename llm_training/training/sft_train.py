import spacy
from datasets import load_dataset
from trl import <PERSON><PERSON>onfig, SFTTrainer
from unsloth import FastLanguageModel, is_bfloat16_supported
from unsloth.chat_templates import get_chat_template

spacy.cli.download("en_core_web_sm")

SYSTEM_PROMPT = """
Generate a valid n8n workflow JSON in the following format:

<Thinking>
Step-by-step reasoning:
1. Goal: What is the user trying to achieve?
2. Nodes: Which nodes will you use? (name + type) Why are they needed?
3. Connections: How are nodes connected? List flow and order.
</Thinking>

<Json>
{Valid JSON here — strictly no comments}
</Json>

=============== JSON Structure ===============

{
  "name": "Workflow Name",
  "nodes": [NODE_OBJECTS],
  "connections": CONNECTIONS_OBJECT,
  "active": false
}

Each node:
{
  "parameters": { ... },
  "name": "NodeName",           // Unique
  "type": "NODE_TYPE",          // Must be valid
  "typeVersion": INTEGER,       // Must be valid
  "position": [X, Y]            // Canvas position
}

Connections:
{
  "SourceNode": {
    "main": [
      [
        { "node": "TargetNode", "type": "main", "index": 0 }
      ]
    ]
  }
}

=============== Key Rules ===============

- All strings in double quotes
- Indent JSON with 2 spaces
- No comments in JSON
- JSON must be directly parsable (e.g. `json.loads`)

=============== Validation ===============

- The <Thinking> section is required and must follow: Goal → Nodes → Connections
- Node names must be unique
- Each node must have:
  - Valid `parameters` (non-empty if required)
  - Unique `name`
  - Valid `type` and `typeVersion`
  - `position`: two integers in an array
- Connections must:
  - Reference correct node names
  - Use "main" as connection type unless otherwise needed
  - Use 2D array format: "main": [[{ ... }]]
  - Use "index": 0 unless another index is required
- Final JSON must be valid with no trailing commasappropriate.
"""

dataset = load_dataset("AKS729/n8n-workflow-dataset")["train"]

max_seq_length = 16000
lora_rank = 32

model, tokenizer = FastLanguageModel.from_pretrained(
    "unsloth/Qwen3-14B-unsloth-bnb-4bit",
    max_seq_length=max_seq_length,
    load_in_4bit=True,
    fast_inference=True,
    gpu_memory_utilization=0.7,
)

model = FastLanguageModel.get_peft_model(
    model,
    r=lora_rank,
    target_modules=[
        "q_proj",
        "k_proj",
        "v_proj",
        "o_proj",
        "gate_proj",
        "up_proj",
        "down_proj",
    ],
    lora_alpha=lora_rank * 2,
    use_gradient_checkpointing="unsloth",
    random_state=3407,
)

tokenizer = get_chat_template(tokenizer, "qwen3")


def format_chat_template_sft(example):
    assistant_content = f"<Thinking>\n{example['thinking']}\n</Thinking>\n\n<Json>\n{example['json']}\n</Json>"
    messages = [
        {"role": "system", "content": SYSTEM_PROMPT},
        {"role": "user", "content": example["prompt"]},
        {"role": "assistant", "content": assistant_content},
    ]
    text = tokenizer.apply_chat_template(messages, tokenize=False)
    return {"text": text}


sft_dataset = dataset.map(
    format_chat_template_sft,
    desc="Formatting dataset for SFT",
    load_from_cache_file=False,
)

training_args = SFTConfig(
    dataset_text_field="text",
    learning_rate=1e-5,
    adam_beta1=0.9,
    adam_beta2=0.99,
    weight_decay=0.1,
    warmup_ratio=0.1,
    lr_scheduler_type="cosine",
    optim="adamw_8bit",
    logging_steps=25,
    logging_dir="logs",
    bf16=is_bfloat16_supported(),
    fp16=not is_bfloat16_supported(),
    per_device_train_batch_size=1,
    gradient_accumulation_steps=1,
    num_train_epochs=1,
    # max_steps = 4,
    save_strategy="epoch",
    report_to="none",
    output_dir="outputs",
    remove_unused_columns=False,
    dataloader_drop_last=False,
)

trainer = SFTTrainer(
    model=model,
    processing_class=tokenizer,
    args=training_args,
    train_dataset=sft_dataset,
)

trainer.train()
trainer.save_model("outputs/sft_model")
tokenizer.save_pretrained("outputs/sft_model")
