{"cells": [{"cell_type": "code", "execution_count": 35, "id": "cf9d20bd", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import seaborn as sns"]}, {"cell_type": "code", "execution_count": 36, "id": "2a29a094", "metadata": {}, "outputs": [], "source": ["df = pd.read_json(\"qwen3-14B-base.json\")"]}, {"cell_type": "code", "execution_count": 37, "id": "55ed4ffb", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "prompt", "rawType": "object", "type": "string"}, {"name": "generated_json", "rawType": "object", "type": "string"}, {"name": "generated_text", "rawType": "object", "type": "string"}, {"name": "json_valid", "rawType": "bool", "type": "boolean"}, {"name": "n8n_valid", "rawType": "bool", "type": "boolean"}, {"name": "llm_score", "rawType": "float64", "type": "float"}, {"name": "evaluation_results", "rawType": "object", "type": "unknown"}, {"name": "json_parsed", "rawType": "bool", "type": "boolean"}, {"name": "top_level_keys_present", "rawType": "int64", "type": "integer"}, {"name": "workflow_name_valid", "rawType": "bool", "type": "boolean"}, {"name": "num_nodes", "rawType": "int64", "type": "integer"}, {"name": "num_nodes_with_valid_type", "rawType": "int64", "type": "integer"}, {"name": "num_nodes_with_valid_version", "rawType": "int64", "type": "integer"}, {"name": "num_nodes_with_valid_structure", "rawType": "int64", "type": "integer"}, {"name": "num_nodes_with_parameters", "rawType": "int64", "type": "integer"}, {"name": "num_connections", "rawType": "int64", "type": "integer"}, {"name": "num_connections_with_valid_structure", "rawType": "int64", "type": "integer"}, {"name": "num_connections_with_valid_target_node", "rawType": "int64", "type": "integer"}, {"name": "active_field_boolean", "rawType": "bool", "type": "boolean"}], "ref": "74ca2704-22bf-4e20-8cad-3405c057db95", "rows": [["0", "Create an n8n workflow that sends a daily vegan recipe from Spoonacular API to a list of Telegram users stored in Airtable. Additionally, the workflow should handle new Telegram users by sending a welcome message with a recipe, and then adding their chat ID and name to the Airtable base, ensuring duplicates are not added.", "{\n  \"name\": \"Daily Vegan Recipe to Telegram Users\",\n  \"nodes\": [\n    {\n      \"parameters\": {\n        \"interval\": \"daily\",\n        \"time\": \"10:00\"\n      },\n      \"name\": \"Trigger_Schedule\",\n      \"type\": \"schedule\",\n      \"typeVersion\": 1,\n      \"position\": [100, 100]\n    },\n    {\n      \"parameters\": {\n        \"method\": \"searchRecipes\",\n        \"query\": \"vegan\",\n        \"number\": 1\n      },\n      \"name\": \"Spoonacular_Search_Recipes\",\n      \"type\": \"spoonacularApi\",\n      \"typeVersion\": 1,\n      \"position\": [300, 100]\n    },\n    {\n      \"parameters\": {\n        \"baseId\": \"appXXXXXXXXXXXXXX\",\n        \"tableName\": \"Users\",\n        \"filterByFormula\": \"\"\n      },\n      \"name\": \"Airtable_Get_Users\",\n      \"type\": \"airtable\",\n      \"typeVersion\": 1,\n      \"position\": [500, 100]\n    },\n    {\n      \"parameters\": {\n        \"item\": \"={{$node['Airtable_Get_Users'].json['records']}}\"\n      },\n      \"name\": \"For_Each_User\",\n      \"type\": \"forEach\",\n      \"typeVersion\": 1,\n      \"position\": [700, 100]\n    },\n    {\n      \"parameters\": {\n        \"message\": \"={{$node['Spoonacular_Search_Recipes'].json['recipes'][0]['title'] + '\\\\n' + $node['Spoonacular_Search_Recipes'].json['recipes'][0]['instructions']}}\",\n        \"chatId\": \"={{$node['For_Each_User'].item['chatId']}}\"\n      },\n      \"name\": \"Telegram_Send_Recipe\",\n      \"type\": \"telegramBot\",\n      \"typeVersion\": 1,\n      \"position\": [900, 100]\n    },\n    {\n      \"parameters\": {\n        \"method\": \"message\"\n      },\n      \"name\": \"Trigger_Telegram_Message\",\n      \"type\": \"telegramBot\",\n      \"typeVersion\": 1,\n      \"position\": [100, 300]\n    },\n    {\n      \"parameters\": {\n        \"function\": \"return {\\n  chatId: $input['chatId'],\\n  name: $input['from']['first_name']\\n};\"\n      },\n      \"name\": \"Extract_User_Data\",\n      \"type\": \"function\",\n      \"typeVersion\": 1,\n      \"position\": [300, 300]\n    },\n    {\n      \"parameters\": {\n        \"baseId\": \"appXXXXXXXXXXXXXX\",\n        \"tableName\": \"Users\",\n        \"filterByFormula\": \"={{\\\"chatId = \\\" + $node['Extract_User_Data'].json['chatId']}}\"\n      },\n      \"name\": \"Airtable_Check_User\",\n      \"type\": \"airtable\",\n      \"typeVersion\": 1,\n      \"position\": [500, 300]\n    },\n    {\n      \"parameters\": {\n        \"condition\": \"={{$node['Airtable_Check_User'].json['records'].length === 0}}\"\n      },\n      \"name\": \"Check_User_Exists\",\n      \"type\": \"if\",\n      \"typeVersion\": 1,\n      \"position\": [700, 300]\n    },\n    {\n      \"parameters\": {\n        \"baseId\": \"appXXXXXXXXXXXXXX\",\n        \"tableName\": \"Users\",\n        \"fields\": {\n          \"chatId\": \"={{$node['Extract_User_Data'].json['chatId']}}\",\n          \"name\": \"={{$node['Extract_User_Data'].json['name']}}\"\n        }\n      },\n      \"name\": \"Airtable_Create_User\",\n      \"type\": \"airtable\",\n      \"typeVersion\": 1,\n      \"position\": [900, 300]\n    },\n    {\n      \"parameters\": {\n        \"message\": \"Welcome! Here's a vegan recipe for you: {{ $node['Spoonacular_Search_Recipes'].json['recipes'][0]['title'] }}\",\n        \"chatId\": \"={{$node['Extract_User_Data'].json['chatId']}}\"\n      },\n      \"name\": \"Telegram_Send_Welcome\",\n      \"type\": \"telegramBot\",\n      \"typeVersion\": 1,\n      \"position\": [1100, 300]\n    }\n  ],\n  \"connections\": {\n    \"Trigger_Schedule\": {\n      \"main\": [\n        [\n          { \"node\": \"Spoonacular_Search_Recipes\", \"type\": \"main\", \"index\": 0 }\n        ]\n      ]\n    },\n    \"Spoonacular_Search_Recipes\": {\n      \"main\": [\n        [\n          { \"node\": \"Airtable_Get_Users\", \"type\": \"main\", \"index\": 0 }\n        ]\n      ]\n    },\n    \"Airtable_Get_Users\": {\n      \"main\": [\n        [\n          { \"node\": \"For_Each_User\", \"type\": \"main\", \"index\": 0 }\n        ]\n      ]\n    },\n    \"For_Each_User\": {\n      \"main\": [\n        [\n          { \"node\": \"Telegram_Send_Recipe\", \"type\": \"main\", \"index\": 0 }\n        ]\n      ]\n    },\n    \"Trigger_Telegram_Message\": {\n      \"main\": [\n        [\n          { \"node\": \"Extract_User_Data\", \"type\": \"main\", \"index\": 0 }\n        ]\n      ]\n    },\n    \"Extract_User_Data\": {\n      \"main\": [\n        [\n          { \"node\": \"Airtable_Check_User\", \"type\": \"main\", \"index\": 0 }\n        ]\n      ]\n    },\n    \"Airtable_Check_User\": {\n      \"main\": [\n        [\n          { \"node\": \"Check_User_Exists\", \"type\": \"main\", \"index\": 0 }\n        ]\n      ]\n    },\n    \"Check_User_Exists\": {\n      \"true\": [\n        [\n          { \"node\": \"Airtable_Create_User\", \"type\": \"main\", \"index\": 0 }\n        ]\n      ],\n      \"false\": [\n        [\n          { \"node\": \"Telegram_Send_Welcome\", \"type\": \"main\", \"index\": 0 }\n        ]\n      ]\n    },\n    \"Airtable_Create_User\": {\n      \"main\": [\n        [\n          { \"node\": \"Telegram_Send_Welcome\", \"type\": \"main\", \"index\": 0 }\n        ]\n      ]\n    }\n  },\n  \"active\": false\n}", "assistant\n<think>\nOkay, let's tackle this user's request. They want an n8n workflow that sends a daily vegan recipe from Spoonacular API to Telegram users in an Airtable. Also, handle new users by sending a welcome message and adding their info to Airtable without duplicates.\n\nFirst, I need to break down the steps. The main goal is to fetch a recipe, send it to users, and manage user data. Let's think about the nodes required.\n\nStarting with the Spoonacular API. The user needs a daily vegan recipe. So, the 'Spoonacular API' node with the 'Search Recipes' method. Parameters would include the query 'vegan', and maybe set a limit. But wait, the user wants a daily one, so maybe set it to 1? But maybe the user wants a random one each day. Hmm, but the parameters might need to be set to get one recipe. Let's check the Spoonacular API docs. Oh right, the 'Search Recipes' endpoint can take parameters like query, number, etc. So setting number to 1 would get one recipe each day.\n\nNext, the Telegram part. The workflow needs to send the recipe to users in Airtable. So first, get the list of users from Airtable. The 'Airtable' node with the 'Get Records' method. Then, for each user, send a message via Telegram. But also, handle new users. So, maybe a 'Telegram Bot' node to send messages. But how to handle new users? The user says that when a new user joins, send a welcome message and add their info to Airtable. Wait, but how does the workflow detect new users? Maybe the Telegram bot needs to have a webhook that triggers when a new user sends a message. But n8n can't do that directly. Alternatively, maybe the user is using a Telegram bot that's set up to capture new users, but that's outside the scope here. Wait, the user's requirement is that the workflow should handle new users by sending a welcome message and adding their info. So perhaps the workflow is triggered by a new user joining, but how?\n\nWait, maybe the workflow is triggered by a new entry in Airtable? Or maybe the Telegram bot has a way to detect new users. Hmm, this is a bit unclear. Alternatively, maybe the workflow is set up to check for new users in Airtable, but that might not be the case. Alternatively, the user might have a Telegram bot that, when a user sends a message, the bot captures their chat ID and name, then adds them to Airtable. But that would require another node to handle that. However, the user's main request is that the workflow should handle new users by sending a welcome message and adding their info. So perhaps the workflow is structured to first check if a user exists in Airtable. If not, send a welcome message and add them. But how to trigger that?\n\nAlternatively, maybe the workflow is triggered by a new user in Airtable. But that's not clear. Let me re-read the user's request.\n\nThe user says: \"the workflow should handle new Telegram users by sending a welcome message with a recipe, and then adding their chat ID and name to the Airtable base, ensuring duplicates are not added.\"\n\nSo, the workflow needs to detect new Telegram users. How? Maybe the workflow is triggered by a Telegram bot that captures new users. For example, when a user sends a message to the bot, the bot captures their chat ID and name, then adds them to Airtable. But that would require a Telegram bot node that listens for messages. However, n8n's Telegram bot node can be set up to trigger on messages. So perhaps the workflow is structured as follows:\n\n1. Trigger: Telegram Bot (when a user sends a message)\n2. Check if the user's chat ID is already in Airtable.\n3. If not, add them to Airtable and send a welcome message with a recipe.\n4. If they are already in Airtable, just send the daily recipe.\n\nBut the user also wants the daily recipe sent to all users. So maybe the workflow has two parts: one for handling new users and another for sending daily recipes.\n\nAlternatively, the workflow could be structured to first check for new users, then send the daily recipe to all users. But how to combine these?\n\nAlternatively, the workflow is triggered by a schedule (daily), then fetches the recipe, gets all users from Airtable, sends the recipe to each, and also checks for new users. Wait, but how would the workflow know about new users unless it's triggered by a Telegram message. Hmm, this is a bit conflicting.\n\nWait, maybe the user wants two separate workflows: one for handling new users (triggered by Telegram messages) and another for sending daily recipes (triggered by a schedule). But the user's request is to create a single workflow. So perhaps the workflow is structured to first handle new users and then send the daily recipe.\n\nAlternatively, the workflow is triggered by a schedule (daily), and during that run, it first checks for new users (maybe from a different source?), but that's unclear. The user's requirement is that the workflow should handle new users by sending a welcome message and adding them to Airtable. So perhaps the workflow is triggered by a Telegram message, which captures the user's info, adds them to Airtable, and sends a welcome message. Then, the same workflow (or another part) sends the daily recipe to all users.\n\nBut how to structure this in n8n. Let's think step by step.\n\nFirst, the workflow needs to:\n\n1. Send a daily vegan recipe to all users in Airtable.\n2. When a new user joins Telegram (i.e., sends a message to the bot), send a welcome message with a recipe and add their info to Airtable, ensuring no duplicates.\n\nSo, the workflow might have two triggers: one for daily schedule and one for Telegram messages. But n8n allows multiple triggers. However, the user might want a single workflow that handles both scenarios. Alternatively, two separate workflows. But the user's request is to create a single workflow. Hmm.\n\nAlternatively, the workflow is structured with a Telegram trigger that captures new users, adds them to Airtable, and sends a welcome message. Then, a separate part of the workflow (triggered by a schedule) sends the daily recipe to all users.\n\nBut how to combine these into one workflow. Maybe the workflow has two triggers: one for the schedule and one for Telegram messages. Then, the nodes are arranged to handle both cases.\n\nBut this might complicate the workflow. Let's proceed with the main parts.\n\nFirst, the daily recipe sending part:\n\n- Trigger: Schedule (daily)\n- Get the recipe from Spoonacular API\n- Get users from Airtable\n- For each user, send a message via Telegram\n\nSecond, handling new users:\n\n- Trigger: Telegram Bot (when a user sends a message)\n- Check if the user's chat ID is in Airtable\n- If not, add them to Airtable and send a welcome message with a recipe\n- If they are already there, do nothing or send a message\n\nBut how to structure this in n8n. Let's outline the nodes.\n\nFor the daily recipe:\n\n1. Trigger: Schedule (daily)\n2. Spoonacular API: Search Recipes (query: vegan, number: 1)\n3. Airtable: Get Records (to get all users)\n4. For each user, send a message via Telegram Bot\n\nFor handling new users:\n\n1. Trigger: Telegram Bot (when a message is received)\n2. Check if the user's chat ID exists in Airtable\n3. If not, add to Airtable and send a welcome message with a recipe\n4. If exists, do nothing\n\nBut how to combine these into one workflow. Maybe the workflow has two triggers: one for schedule and one for Telegram messages. Then, the nodes are arranged accordingly.\n\nBut the user might not have specified that, but the problem says to create a single workflow. So perhaps the workflow is structured with both triggers.\n\nBut the user's main request is to create a workflow that does both: send daily recipes and handle new users. So the workflow must have both triggers.\n\nNow, let's outline the nodes.\n\nFirst, the nodes for the daily recipe:\n\n- Trigger: Schedule (daily)\n- Spoonacular API: Search Recipes (query: vegan, number: 1)\n- Airtable: Get Records (base: users, table: users)\n- Telegram Bot: Send Message (for each user)\n\nFor handling new users:\n\n- Trigger: Telegram Bot (when a message is received)\n- Airtable: Get Records (to check if user exists)\n- If not exists, add to Airtable and send a welcome message\n- Else, do nothing\n\nBut how to structure the nodes. Let's think about the nodes:\n\nNodes:\n\n1. Trigger: Schedule (daily)\n2. Spoonacular API: Search Recipes (query: vegan, number: 1)\n3. Airtable: Get Records (base: users, table: users)\n4. Telegram Bot: Send Message (for each user)\n5. Trigger: Telegram Bot (when a message is received)\n6. Airtable: Get Records (base: users, table: users)\n7. Function: Check if user exists\n8. Airtable: Create Record (if user doesn't exist)\n9. Telegram Bot: Send Message (welcome message)\n\nBut this might require a lot of nodes. Also, the connections would need to be set up properly.\n\nBut the user's main request is to create a workflow that does both. So the workflow will have two triggers: one for daily schedule and one for Telegram messages.\n\nNow, the JSON structure needs to include both triggers and their respective nodes.\n\nBut the user might not have specified the exact parameters for the Airtable base, so I'll need to use placeholders.\n\nAlso, the 'Function' node is needed to check if the user exists in Airtable. The 'Function' node would take the user's chat ID and name from the Telegram message, then query Airtable to see if that chat ID exists. If not, proceed to add them.\n\nSo, the nodes would be:\n\nFor the daily recipe:\n\n- Trigger: Schedule\n- Spoonacular API: Search Recipes\n- Airtable: Get Records (users)\n- For each user, send message via Telegram\n\nFor new users:\n\n- Trigger: Telegram Bot (when message received)\n- Function: Extract chat ID and name from message\n- Airtable: Get Records (filter by chat ID)\n- If no records, add to Airtable and send welcome message\n\nBut how to structure this in n8n's JSON.\n\nAlso, the 'For each' node might be needed to loop through each user in Airtable and send a message.\n\nSo, the nodes would be:\n\n1. Trigger: Schedule (daily)\n2. Spoonacular API: Search Recipes (query: vegan, number: 1)\n3. Airtable: Get Records (base: users, table: users)\n4. For Each: Loop through each user\n5. Telegram Bot: Send Message (using user's chat ID)\n\nFor new users:\n\n1. Trigger: Telegram Bot (when message received)\n2. Function: Extract chat ID and name from message\n3. Airtable: Get Records (filter by chat ID)\n4. If: Check if any records exist\n5. If not, add to Airtable\n6. Send welcome message via Telegram Bot\n\nBut the JSON structure needs to have all these nodes with unique names and correct connections.\n\nNow, considering the JSON structure, each node must have a unique name, type, typeVersion, and position.\n\nAlso, the connections must be set up correctly. For example, the Schedule trigger connects to Spoonacular API, which connects to Airtable Get Records, which connects to For Each, which connects to Telegram Bot.\n\nFor the Telegram trigger, it connects to the Function node, which connects to Airtable Get Records, then to If node, then to Airtable Create Record and Telegram Bot.\n\nBut the 'If' node might have two branches: one for when the user exists (do nothing) and one for when they don't (add to Airtable and send message).\n\nBut in n8n, the 'If' node can have two outputs. However, the JSON structure for connections might need to handle that.\n\nThis is getting complex. Let's outline the nodes with their parameters and connections.\n\nFirst, the daily workflow part:\n\n- Node 1: Trigger (Schedule)\n- Node 2: Spoonacular API (Search Recipes)\n- Node 3: Airtable Get Records\n- Node 4: For Each\n- Node 5: Telegram Bot Send Message\n\nConnections: Trigger -> Spoonacular API -> Airtable Get Records -> For Each -> Telegram Bot\n\nFor the new user part:\n\n- Node 6: Trigger (Telegram Bot)\n- Node 7: Function (Extract chat ID and name)\n- Node 8: Airtable Get Records (filter by chat ID)\n- Node 9: If (Check if any records)\n- Node 10: Airtable Create Record (if no records)\n- Node 11: Telegram Bot Send Message (welcome)\n\nConnections: Telegram Trigger -> Function -> Airtable Get Records -> If (output 1: no records -> Airtable Create and Telegram Send; output 2: do nothing)\n\nBut the JSON structure must have all these nodes with unique names and correct connections.\n\nNow, the parameters for each node:\n\nFor the Spoonacular API node, parameters would include the query, number, etc.\n\nFor the Airtable Get Records, parameters would include the base ID, table name, and possibly a filter (like chat ID).\n\nThe Function node would need to extract the chat ID and name from the Telegram message.\n\nThe Airtable Create Record node would need to add the chat ID and name.\n\nThe Telegram Bot Send Message would use the chat ID from the user and send the welcome message or the daily recipe.\n\nAlso, the 'For Each' node would loop through each user in Airtable and send the message.\n\nNow, considering all this, the JSON structure would have all these nodes with their parameters and connections.\n\nBut the user might not have provided specific details like base IDs, so placeholders are used.\n\nNow, putting it all together into the JSON structure as per the user's instructions.\n</think>\n\n<Thinking>\nStep-by-step reasoning:\n1. Goal: Create a workflow that sends daily vegan recipes to Telegram users from Airtable, and handles new users by sending a welcome message and adding them to Airtable without duplicates.\n2. Nodes: \n   - **Trigger (Schedule)**: To trigger daily recipe sending.\n   - **Spoonacular API (Search Recipes)**: Fetch a vegan recipe.\n   - **Airtable (Get Records)**: Retrieve user data.\n   - **For Each**: Iterate over each user to send messages.\n   - **Telegram Bot (Send Message)**: Send daily recipe or welcome message.\n   - **Trigger (Telegram Bot)**: Detect new users.\n   - **Function**: Extract user data from Telegram messages.\n   - **Airtable (Get Records)**: Check for existing users.\n   - **If**: Conditional logic to handle new users.\n   - **Airtable (Create Record)**: Add new users to Airtable.\n3. Connections: \n   - Schedule → Spoonacular API → Airtable Get Records → For Each → Telegram Bot.\n   - Telegram Bot (Trigger) → Function → Airtable Get Records → If → Airtable Create Record and Telegram Bot (welcome message).\n</Thinking>\n\n<Json>\n{\n  \"name\": \"Daily Vegan Recipe to Telegram Users\",\n  \"nodes\": [\n    {\n      \"parameters\": {\n        \"interval\": \"daily\",\n        \"time\": \"10:00\"\n      },\n      \"name\": \"Trigger_Schedule\",\n      \"type\": \"schedule\",\n      \"typeVersion\": 1,\n      \"position\": [100, 100]\n    },\n    {\n      \"parameters\": {\n        \"method\": \"searchRecipes\",\n        \"query\": \"vegan\",\n        \"number\": 1\n      },\n      \"name\": \"Spoonacular_Search_Recipes\",\n      \"type\": \"spoonacularApi\",\n      \"typeVersion\": 1,\n      \"position\": [300, 100]\n    },\n    {\n      \"parameters\": {\n        \"baseId\": \"appXXXXXXXXXXXXXX\",\n        \"tableName\": \"Users\",\n        \"filterByFormula\": \"\"\n      },\n      \"name\": \"Airtable_Get_Users\",\n      \"type\": \"airtable\",\n      \"typeVersion\": 1,\n      \"position\": [500, 100]\n    },\n    {\n      \"parameters\": {\n        \"item\": \"={{$node['Airtable_Get_Users'].json['records']}}\"\n      },\n      \"name\": \"For_Each_User\",\n      \"type\": \"forEach\",\n      \"typeVersion\": 1,\n      \"position\": [700, 100]\n    },\n    {\n      \"parameters\": {\n        \"message\": \"={{$node['Spoonacular_Search_Recipes'].json['recipes'][0]['title'] + '\\\\n' + $node['Spoonacular_Search_Recipes'].json['recipes'][0]['instructions']}}\",\n        \"chatId\": \"={{$node['For_Each_User'].item['chatId']}}\"\n      },\n      \"name\": \"Telegram_Send_Recipe\",\n      \"type\": \"telegramBot\",\n      \"typeVersion\": 1,\n      \"position\": [900, 100]\n    },\n    {\n      \"parameters\": {\n        \"method\": \"message\"\n      },\n      \"name\": \"Trigger_Telegram_Message\",\n      \"type\": \"telegramBot\",\n      \"typeVersion\": 1,\n      \"position\": [100, 300]\n    },\n    {\n      \"parameters\": {\n        \"function\": \"return {\\n  chatId: $input['chatId'],\\n  name: $input['from']['first_name']\\n};\"\n      },\n      \"name\": \"Extract_User_Data\",\n      \"type\": \"function\",\n      \"typeVersion\": 1,\n      \"position\": [300, 300]\n    },\n    {\n      \"parameters\": {\n        \"baseId\": \"appXXXXXXXXXXXXXX\",\n        \"tableName\": \"Users\",\n        \"filterByFormula\": \"={{\\\"chatId = \\\" + $node['Extract_User_Data'].json['chatId']}}\"\n      },\n      \"name\": \"Airtable_Check_User\",\n      \"type\": \"airtable\",\n      \"typeVersion\": 1,\n      \"position\": [500, 300]\n    },\n    {\n      \"parameters\": {\n        \"condition\": \"={{$node['Airtable_Check_User'].json['records'].length === 0}}\"\n      },\n      \"name\": \"Check_User_Exists\",\n      \"type\": \"if\",\n      \"typeVersion\": 1,\n      \"position\": [700, 300]\n    },\n    {\n      \"parameters\": {\n        \"baseId\": \"appXXXXXXXXXXXXXX\",\n        \"tableName\": \"Users\",\n        \"fields\": {\n          \"chatId\": \"={{$node['Extract_User_Data'].json['chatId']}}\",\n          \"name\": \"={{$node['Extract_User_Data'].json['name']}}\"\n        }\n      },\n      \"name\": \"Airtable_Create_User\",\n      \"type\": \"airtable\",\n      \"typeVersion\": 1,\n      \"position\": [900, 300]\n    },\n    {\n      \"parameters\": {\n        \"message\": \"Welcome! Here's a vegan recipe for you: {{ $node['Spoonacular_Search_Recipes'].json['recipes'][0]['title'] }}\",\n        \"chatId\": \"={{$node['Extract_User_Data'].json['chatId']}}\"\n      },\n      \"name\": \"Telegram_Send_Welcome\",\n      \"type\": \"telegramBot\",\n      \"typeVersion\": 1,\n      \"position\": [1100, 300]\n    }\n  ],\n  \"connections\": {\n    \"Trigger_Schedule\": {\n      \"main\": [\n        [\n          { \"node\": \"Spoonacular_Search_Recipes\", \"type\": \"main\", \"index\": 0 }\n        ]\n      ]\n    },\n    \"Spoonacular_Search_Recipes\": {\n      \"main\": [\n        [\n          { \"node\": \"Airtable_Get_Users\", \"type\": \"main\", \"index\": 0 }\n        ]\n      ]\n    },\n    \"Airtable_Get_Users\": {\n      \"main\": [\n        [\n          { \"node\": \"For_Each_User\", \"type\": \"main\", \"index\": 0 }\n        ]\n      ]\n    },\n    \"For_Each_User\": {\n      \"main\": [\n        [\n          { \"node\": \"Telegram_Send_Recipe\", \"type\": \"main\", \"index\": 0 }\n        ]\n      ]\n    },\n    \"Trigger_Telegram_Message\": {\n      \"main\": [\n        [\n          { \"node\": \"Extract_User_Data\", \"type\": \"main\", \"index\": 0 }\n        ]\n      ]\n    },\n    \"Extract_User_Data\": {\n      \"main\": [\n        [\n          { \"node\": \"Airtable_Check_User\", \"type\": \"main\", \"index\": 0 }\n        ]\n      ]\n    },\n    \"Airtable_Check_User\": {\n      \"main\": [\n        [\n          { \"node\": \"Check_User_Exists\", \"type\": \"main\", \"index\": 0 }\n        ]\n      ]\n    },\n    \"Check_User_Exists\": {\n      \"true\": [\n        [\n          { \"node\": \"Airtable_Create_User\", \"type\": \"main\", \"index\": 0 }\n        ]\n      ],\n      \"false\": [\n        [\n          { \"node\": \"Telegram_Send_Welcome\", \"type\": \"main\", \"index\": 0 }\n        ]\n      ]\n    },\n    \"Airtable_Create_User\": {\n      \"main\": [\n        [\n          { \"node\": \"Telegram_Send_Welcome\", \"type\": \"main\", \"index\": 0 }\n        ]\n      ]\n    }\n  },\n  \"active\": false\n}\n</Json>", "True", "False", "6.0", "{'json_parsed': True, 'top_level_keys_present': 4, 'workflow_name_valid': True, 'num_nodes': 11, 'num_nodes_with_valid_type': 0, 'num_nodes_with_valid_version': 0, 'num_nodes_with_valid_structure': 0, 'num_nodes_with_parameters': 11, 'num_connections': 10, 'num_connections_with_valid_structure': 10, 'num_connections_with_valid_target_node': 10, 'active_field_boolean': True}", "True", "4", "True", "11", "0", "0", "11", "11", "10", "10", "10", "True"], ["1", "Create an n8n workflow that is triggered by a webhook. This workflow should read data from a Google Sheet (sheet ID '1uFISwZJ1rzkOnOSNocX-_n-ASSAznWGdpcPK3_KCvVo') and then build an HTML table using that data. Finally, the workflow should respond to the webhook with the generated HTML as the body and set the 'Content-Type' header to 'text/html; charset=UTF-8'.", "{\n  \"name\": \"WebhookToHTMLTable\",\n  \"nodes\": [\n    {\n      \"parameters\": {\n        \"response\": \"200\",\n        \"method\": \"POST\"\n      },\n      \"name\": \"WebhookTrigger\",\n      \"type\": \"webhook\",\n      \"typeVersion\": 1,\n      \"position\": [100, 100]\n    },\n    {\n      \"parameters\": {\n        \"sheetId\": \"1uFISwZJ1rzkOnOSNocX-_n-ASSAznWGdpcPK3_KCvVo\",\n        \"range\": \"Sheet1!A1:Z\"\n      },\n      \"name\": \"GoogleSheetsRead\",\n      \"type\": \"google_sheets\",\n      \"typeVersion\": 1,\n      \"position\": [300, 100]\n    },\n    {\n      \"parameters\": {\n        \"function\": \"function(data) {\\n  const table = '<table border=\\\"1\\\"><tr>' + data[0].map(h => `<th>${h}</th>`).join('') + '</tr>'\\n  data.slice(1).forEach(row => {\\n    table += '<tr>' + row.map(cell => `<td>${cell}</td>`).join('') + '</tr>'\\n  });\\n  table += '</table>';\\n  return { html: table };\\n}\"\n      },\n      \"name\": \"BuildHTMLTable\",\n      \"type\": \"function\",\n      \"typeVersion\": 1,\n      \"position\": [500, 100]\n    },\n    {\n      \"parameters\": {\n        \"body\": \"={{$node(\\\"BuildHTMLTable\\\").output().json.html}}\",\n        \"headers\": {\n          \"Content-Type\": \"text/html; charset=UTF-8\"\n        }\n      },\n      \"name\": \"SendResponse\",\n      \"type\": \"response\",\n      \"typeVersion\": 1,\n      \"position\": [700, 100]\n    }\n  ],\n  \"connections\": {\n    \"WebhookTrigger\": {\n      \"main\": [\n        [\n          { \"node\": \"GoogleSheetsRead\", \"type\": \"main\", \"index\": 0 }\n        ]\n      ]\n    },\n    \"GoogleSheetsRead\": {\n      \"main\": [\n        [\n          { \"node\": \"BuildHTMLTable\", \"type\": \"main\", \"index\": 0 }\n        ]\n      ]\n    },\n    \"BuildHTMLTable\": {\n      \"main\": [\n        [\n          { \"node\": \"SendResponse\", \"type\": \"main\", \"index\": 0 }\n        ]\n      ]\n    }\n  },\n  \"active\": false\n}", "assistant\n<think>\nOkay, let's tackle this user's request. They want an n8n workflow that's triggered by a webhook, reads data from a Google Sheet, builds an HTML table from that data, and responds with the HTML as the body with the correct Content-Type header.\n\nFirst, the trigger node. Since it's a webhook, the \"Webhook\" node is needed. The type is \"webhook\" and version 1. The parameters should have the \"response\" set to \"200\" and maybe a \"method\" like \"POST\" or \"GET\". Wait, the user didn't specify the method, but the default is probably POST. Also, the name should be unique, like \"WebhookTrigger\".\n\nNext, the Google Sheets node. The user provided the sheet ID, so the \"Google Sheets\" node (type \"google_sheets\", version 1) will be used. The parameters need the sheet ID, maybe the range. But the user didn't specify a range, so maybe just the sheet ID. Wait, the Google Sheets node might require the sheet ID and the range. If the user didn't specify, maybe the default is the first sheet. So parameters would include \"sheetId\": \"1uFISwZJ1rzkOnOSNocX-_n-ASSAznWGdpcPK3_KCvVo\" and \"range\": \"Sheet1\" or something. But the user might not have specified the sheet name, so maybe just the sheet ID and assume the first sheet. Hmm, but the node might require a range. Maybe the user expects to read the entire sheet, so the range could be \"A1:Z\" or similar. But I'll need to check the node's parameters. Since the user didn't specify, I'll use \"Sheet1\" as the sheet name and \"A1:Z\" as the range. So parameters would be \"sheetId\": \"1uFISwZJ1rzkOnOSNocX-_n-ASSAznWGdpcPK3_KCvVo\", \"range\": \"Sheet1!A1:Z\".\n\nThen, the next node is to build the HTML table. The \"Function\" node (type \"function\", version 1) can be used here. The function needs to take the data from the Google Sheets node and convert it into an HTML table. The parameters for the function node would include the function code. The code would loop through the rows and create table rows and cells. Also, the function should return the HTML string. The name could be \"BuildHTMLTable\".\n\nFinally, the response node. The \"Response\" node (type \"response\", version 1) will send the HTML back. The parameters need to set the body to the HTML from the previous node and set the headers, specifically \"Content-Type\" to \"text/html; charset=UTF-8\". The name could be \"SendHTMLResponse\".\n\nNow, connections. The WebhookTrigger connects to Google Sheets, then Google Sheets connects to the Function node, and the Function node connects to the Response node. Each connection uses the main output.\n\nFor positions, I'll assign coordinates to each node so they are spaced out on the canvas. Maybe WebhookTrigger at (100, 100), Google Sheets at (300, 100), Function at (500, 100), Response at (700, 100).\n\nCheck if all nodes have unique names and correct types. Also, ensure that the parameters are correctly filled. The Function node's code needs to be written properly. For example, using the data from the Google Sheets node's output, which is probably an array of rows. So the function would start with a table tag, loop through each row, create tr and td elements, and close the table. Also, make sure to return the HTML string.\n\nTesting the JSON structure: nodes array with each node's parameters, name, type, typeVersion, position. Connections object with each node's output connected to the next.\n\nI think that's all. Now, structure the JSON accordingly.\n</think>\n\n<Thinking>\nStep-by-step reasoning:\n1. Goal: Create a workflow that triggers via webhook, reads Google Sheet data, converts it to HTML table, and returns it as a response with proper headers.\n2. Nodes: \n   - \"WebhookTrigger\" (webhook) to receive the trigger\n   - \"GoogleSheetsRead\" (google_sheets) to fetch data from the sheet\n   - \"BuildHTMLTable\" (function) to transform data into HTML\n   - \"SendResponse\" (response) to return the HTML with headers\n3. Connections: Webhook → Google Sheets → Function → Response\n</Thinking>\n\n<Json>\n{\n  \"name\": \"WebhookToHTMLTable\",\n  \"nodes\": [\n    {\n      \"parameters\": {\n        \"response\": \"200\",\n        \"method\": \"POST\"\n      },\n      \"name\": \"WebhookTrigger\",\n      \"type\": \"webhook\",\n      \"typeVersion\": 1,\n      \"position\": [100, 100]\n    },\n    {\n      \"parameters\": {\n        \"sheetId\": \"1uFISwZJ1rzkOnOSNocX-_n-ASSAznWGdpcPK3_KCvVo\",\n        \"range\": \"Sheet1!A1:Z\"\n      },\n      \"name\": \"GoogleSheetsRead\",\n      \"type\": \"google_sheets\",\n      \"typeVersion\": 1,\n      \"position\": [300, 100]\n    },\n    {\n      \"parameters\": {\n        \"function\": \"function(data) {\\n  const table = '<table border=\\\"1\\\"><tr>' + data[0].map(h => `<th>${h}</th>`).join('') + '</tr>'\\n  data.slice(1).forEach(row => {\\n    table += '<tr>' + row.map(cell => `<td>${cell}</td>`).join('') + '</tr>'\\n  });\\n  table += '</table>';\\n  return { html: table };\\n}\"\n      },\n      \"name\": \"BuildHTMLTable\",\n      \"type\": \"function\",\n      \"typeVersion\": 1,\n      \"position\": [500, 100]\n    },\n    {\n      \"parameters\": {\n        \"body\": \"={{$node(\\\"BuildHTMLTable\\\").output().json.html}}\",\n        \"headers\": {\n          \"Content-Type\": \"text/html; charset=UTF-8\"\n        }\n      },\n      \"name\": \"SendResponse\",\n      \"type\": \"response\",\n      \"typeVersion\": 1,\n      \"position\": [700, 100]\n    }\n  ],\n  \"connections\": {\n    \"WebhookTrigger\": {\n      \"main\": [\n        [\n          { \"node\": \"GoogleSheetsRead\", \"type\": \"main\", \"index\": 0 }\n        ]\n      ]\n    },\n    \"GoogleSheetsRead\": {\n      \"main\": [\n        [\n          { \"node\": \"BuildHTMLTable\", \"type\": \"main\", \"index\": 0 }\n        ]\n      ]\n    },\n    \"BuildHTMLTable\": {\n      \"main\": [\n        [\n          { \"node\": \"SendResponse\", \"type\": \"main\", \"index\": 0 }\n        ]\n      ]\n    }\n  },\n  \"active\": false\n}\n</Json>", "True", "False", "5.0", "{'json_parsed': True, 'top_level_keys_present': 4, 'workflow_name_valid': True, 'num_nodes': 4, 'num_nodes_with_valid_type': 0, 'num_nodes_with_valid_version': 0, 'num_nodes_with_valid_structure': 0, 'num_nodes_with_parameters': 4, 'num_connections': 3, 'num_connections_with_valid_structure': 3, 'num_connections_with_valid_target_node': 3, 'active_field_boolean': True}", "True", "4", "True", "4", "0", "0", "4", "4", "3", "3", "3", "True"], ["2", "Generate an n8n workflow that listens for a webhook, checks a 'type' value, and either registers a URL or, if the type is not 1, fetches a URL, extracts its title, adds the title and URL to a Notion database, and then replies on Discord.", "{\n  \"name\": \"Webhook to Notion and Discord\",\n  \"nodes\": [\n    {\n      \"parameters\": {\n        \"method\": \"POST\"\n      },\n      \"name\": \"webhook\",\n      \"type\": \"webhook\",\n      \"typeVersion\": 1,\n      \"position\": [100, 100]\n    },\n    {\n      \"parameters\": {\n        \"conditions\": [\n          {\n            \"value\": \"1\",\n            \"property\": \"type\"\n          }\n        ]\n      },\n      \"name\": \"switch\",\n      \"type\": \"switch\",\n      \"typeVersion\": 1,\n      \"position\": [300, 100]\n    },\n    {\n      \"parameters\": {\n        \"url\": \"={{$node.webhook.json.url}}\",\n        \"method\": \"GET\"\n      },\n      \"name\": \"httpRequest\",\n      \"type\": \"httpRequest\",\n      \"typeVersion\": 1,\n      \"position\": [500, 100]\n    },\n    {\n      \"parameters\": {\n        \"function\": \"const $ = require('cheerio');\\nconst html = $($.parseHTML(data));\\nreturn { title: html('title').text() };\"\n      },\n      \"name\": \"function\",\n      \"type\": \"function\",\n      \"typeVersion\": 1,\n      \"position\": [700, 100]\n    },\n    {\n      \"parameters\": {\n        \"databaseId\": \"notion-database-id\",\n        \"properties\": {\n          \"title\": \"={{$node.function.json.title}}\",\n          \"url\": \"={{$node.webhook.json.url}}\"\n        }\n      },\n      \"name\": \"notion\",\n      \"type\": \"notion\",\n      \"typeVersion\": 1,\n      \"position\": [900, 100]\n    },\n    {\n      \"parameters\": {\n        \"webhookUrl\": \"discord-webhook-url\",\n        \"content\": \"Added {{title}} ({{url}}) to Notion.\"\n      },\n      \"name\": \"discordWebhook\",\n      \"type\": \"discordWebhook\",\n      \"typeVersion\": 1,\n      \"position\": [1100, 100]\n    }\n  ],\n  \"connections\": {\n    \"webhook\": {\n      \"main\": [\n        [\n          { \"node\": \"switch\", \"type\": \"main\", \"index\": 0 }\n        ]\n      ]\n    },\n    \"switch\": {\n      \"main\": [\n        [\n          { \"node\": \"httpRequest\", \"type\": \"main\", \"index\": 0 }\n        ]\n      ]\n    },\n    \"httpRequest\": {\n      \"main\": [\n        [\n          { \"node\": \"function\", \"type\": \"main\", \"index\": 0 }\n        ]\n      ]\n    },\n    \"function\": {\n      \"main\": [\n        [\n          { \"node\": \"notion\", \"type\": \"main\", \"index\": 0 }\n        ]\n      ]\n    },\n    \"notion\": {\n      \"main\": [\n        [\n          { \"node\": \"discordWebhook\", \"type\": \"main\", \"index\": 0 }\n        ]\n      ]\n    }\n  },\n  \"active\": false\n}", "assistant\n<think>\nOkay, let's tackle this user's request. They want an n8n workflow that listens for a webhook, checks the 'type' value, and then either registers a URL or, if the type isn't 1, fetches the URL, extracts the title, adds it to a Notion database, and replies on Discord.\n\nFirst, I need to break down the steps. The workflow starts with a webhook. So the first node should be a Webhook node. It needs to be set up to receive POST requests. The parameters would include the method as POST and maybe a name for the webhook.\n\nNext, after receiving the webhook, the workflow needs to check the 'type' value. That sounds like a Switch node. The Switch node can check the value of the 'type' field in the incoming data. Depending on whether it's 1 or not, it will branch into different paths.\n\nIf the type is 1, the workflow should register a URL. But the user didn't specify what \"register a URL\" entails. Maybe it's just storing it somewhere, but since the other path adds to Notion and replies on Discord, perhaps the 'register' part is just a placeholder. Maybe the user intended that if type is 1, they do something else, but the description says if type is not 1, then do the other steps. Wait, the original instruction says: \"either registers a URL or, if the type is not 1, fetches a URL...\" So if type is 1, register a URL. If not, fetch, extract, add to Notion, and reply on Discord. But the user didn't specify what \"register a URL\" does. Maybe it's a placeholder for some action, but since the user didn't mention it, perhaps the 'register' part is just a note, and the main action is when type is not 1. However, the workflow must handle both cases. But the user might not have provided details for the 'register' part. Hmm, maybe the user expects that when type is 1, the workflow does nothing else, or maybe it's a placeholder. Since the user didn't specify, perhaps I should model the 'register' part as a simple action, like setting a variable or logging, but since the user didn't mention, maybe it's just a note. Alternatively, maybe the user intended that when type is 1, the workflow ends, and when it's not, it proceeds. But the user's instruction says \"either registers a URL or, if the type is not 1, fetches a URL...\" So the 'register' is for type 1, and the other steps for type not 1. But without knowing what 'register' entails, maybe I can't model that. Wait, maybe the user made a mistake and meant that if type is 1, do something else, but the main path is when type is not 1. Alternatively, perhaps the 'register' is a placeholder, and the user expects that part to be handled by another node, but since it's not specified, maybe I can't include it. However, the user's instruction says the workflow must do both: register a URL if type is 1, or else do the other steps. So I need to model both branches.\n\nBut since the user hasn't provided details on what 'register a URL' entails, maybe I can't create a specific node for that. However, perhaps the user intended that when type is 1, the workflow just ends, or maybe it's a placeholder. Alternatively, maybe the user made a mistake and the 'register' part is not needed, but the main action is when type is not 1. But the original instruction says \"either registers a URL or, if the type is not 1, fetches a URL...\" So the 'register' is for type 1, and the other steps for type not 1. Since the user hasn't provided details on how to register a URL, maybe I can't model that part. However, the user might expect that the 'register' part is just a note, and the main action is the other path. But the user's instruction requires that the workflow does both. Hmm, this is a problem. Maybe the user intended that when type is 1, the workflow does nothing else, but that's not clear. Alternatively, perhaps the 'register' is a placeholder, and the user expects that part to be handled by a different node, like a Function node that just logs or sets a variable. But without more info, I'll have to make an assumption here. Let's proceed with the assumption that when type is 1, the workflow just ends, and when it's not, it proceeds with the other steps. But the user's instruction says \"either registers a URL or, if the type is not 1, fetches a URL...\" So the 'register' is for type 1, and the other steps for type not 1. Since the user hasn't provided details on how to register a URL, maybe I can't model that part. However, the user might have intended that the 'register' is a placeholder, and the main action is the other path. But the user's instruction requires that the workflow does both. Therefore, I need to include both branches. But without knowing what 'register' entails, perhaps I can't create a specific node for that. Maybe the user expects that the 'register' part is just a note, and the main action is the other path. Alternatively, maybe the user made a mistake and the 'register' is not needed. This is a bit of a problem. Let me check the original query again.\n\nOriginal query: \"either registers a URL or, if the type is not 1, fetches a URL, extracts its title, adds the title and URL to a Notion database, and then replies on Discord.\"\n\nSo the structure is: if type is 1, register a URL. Else, do the other steps. But the user hasn't specified what 'register a URL' does. Maybe it's a placeholder, and the user expects that part to be handled by a different node, like a Function node that just logs or sets a variable. But since the user hasn't provided details, perhaps I can't model that. However, the user might have intended that the 'register' is a placeholder, and the main action is the other path. But the user's instruction requires that the workflow does both. Therefore, I need to include both branches. But without knowing what 'register' entails, I can't create a specific node. Maybe the user expects that the 'register' is a placeholder, and the main action is the other path. Alternatively, perhaps the user made a mistake and the 'register' is not needed. This is a bit of a problem. Let me proceed with the assumption that the 'register' part is a placeholder, and the main action is the other path. But the user's instruction says the workflow must do both. Therefore, I need to include both branches. However, since the user hasn't provided details on how to register a URL, I can't create a specific node for that. Therefore, perhaps the user intended that the 'register' is a placeholder, and the main action is the other path. Alternatively, maybe the user made a mistake and the 'register' is not needed. This is a bit of a problem. Let me proceed with the assumption that the 'register' part is a placeholder, and the main action is the other path. Therefore, the workflow will have a Switch node that checks the 'type' value. If it's 1, it goes to a node that does nothing (like a Function node that just passes the data), and if not, it proceeds to fetch the URL, extract the title, add to Notion, and reply on Discord.\n\nBut the user's instruction says \"either registers a URL or, if the type is not 1, fetches a URL...\" So the 'register' is for type 1, and the other steps for type not 1. Therefore, the workflow must have two branches. However, without knowing what 'register' entails, I can't model that part. Therefore, perhaps the user expects that the 'register' is a placeholder, and the main action is the other path. Alternatively, maybe the user intended that the 'register' is a different action, but since it's not specified, I can't model it. Therefore, I'll proceed with the assumption that the 'register' part is a placeholder, and the main action is the other path. Therefore, the workflow will have a Switch node that checks the 'type' value. If it's 1, it goes to a node that does nothing (like a Function node that just passes the data), and if not, it proceeds to fetch the URL, extract the title, add to Notion, and reply on Discord.\n\nNow, moving on. The other branch (when type is not 1) needs to fetch a URL. So the next node after the Switch (for the 'not 1' branch) would be a HTTP Request node to fetch the URL. The parameters would include the URL from the incoming data. Then, extract the title. That can be done with a Function node that parses the HTML and extracts the title. Then, add the title and URL to a Notion database. So a Notion API node would be needed, with the parameters to add a new entry with the title and URL. Finally, reply on Discord. So a Discord Webhook node would be used to send a message.\n\nNow, let's structure the nodes:\n\n1. Webhook: To receive the initial request.\n2. Switch: To check the 'type' value.\n3. For the 'type is 1' branch: Maybe a Function node that does nothing or logs.\n4. For the 'type is not 1' branch:\n   a. HTTP Request: Fetch the URL from the data.\n   b. Function: Extract the title from the HTML.\n   c. Notion API: Add the title and URL to the database.\n   d. Discord Webhook: Send a message.\n\nConnections would be from Webhook to Switch, then Switch to the respective nodes based on the condition.\n\nNow, checking the node types and parameters:\n\n- Webhook node: type 'webhook', method 'POST'.\n- Switch node: type 'switch', with a condition on the 'type' field.\n- HTTP Request node: type 'httpRequest', with the URL parameter set to the incoming data's URL.\n- Function node: type 'function', with a JavaScript function to extract the title.\n- Notion API node: type 'notion', with the database ID and properties.\n- Discord Webhook node: type 'discordWebhook', with the webhook URL and message content.\n\nPositions need to be assigned, but they can be arbitrary as long as they are unique.\n\nNow, ensuring that all parameters are correctly set. For example, the HTTP Request node needs the URL from the incoming data. The Function node needs to parse the HTML and extract the title. The Notion node needs the database ID and the properties (title and URL). The Discord node needs the webhook URL and the message content.\n\nAlso, the connections need to be set up correctly. The Webhook node connects to the Switch node. The Switch node has two branches: one for type 1 (maybe to a Function node that does nothing), and one for type not 1, which connects to HTTP Request, then Function, then Notion, then Discord.\n\nBut the user didn't mention what happens when type is 1. Since the user's instruction says \"either registers a URL or, if the type is not 1, fetches a URL...\", maybe the 'register' is a different action. However, without details, I can't model that. Therefore, perhaps the user intended that the 'register' is a placeholder, and the main action is the other path. Therefore, the 'type is 1' branch can be a Function node that just passes the data without any action, or maybe logs it. But since the user didn't specify, I'll have to make an assumption here. Let's assume that the 'register' part is a placeholder and the main action is the other path. Therefore, the 'type is 1' branch can be a Function node that does nothing, or maybe just ends the workflow.\n\nBut the user's instruction says the workflow must do both. Therefore, I need to include both branches. However, without knowing what 'register' entails, I can't create a specific node. Therefore, perhaps the user made a mistake and the 'register' is not needed. Alternatively, maybe the user intended that the 'register' is a different action, but since it's not specified, I can't model it. Therefore, I'll proceed with the assumption that the 'register' is a placeholder and the main action is the other path. Therefore, the 'type is 1' branch can be a Function node that just passes the data, and the other branch proceeds with the steps.\n\nNow, putting it all together in JSON format, ensuring that all nodes are correctly named, types are valid, parameters are set, and connections are properly structured.\n</think>\n\n<Thinking>\nStep-by-step reasoning:\n1. Goal: Create a workflow that listens for a webhook, checks the 'type' field, and performs different actions based on its value.\n2. Nodes: \n   - `webhook` (type: webhook) to receive incoming requests.\n   - `switch` (type: switch) to branch based on the 'type' value.\n   - `httpRequest` (type: httpRequest) to fetch a URL when type is not 1.\n   - `function` (type: function) to extract the title from the fetched HTML.\n   - `notion` (type: notion) to add data to a Notion database.\n   - `discordWebhook` (type: discordWebhook) to send a message to Discord.\n3. Connections: \n   - Webhook → Switch.\n   - Switch (type not 1) → httpRequest → function → notion → discordWebhook.\n   - Switch (type 1) → (no further action, as per user instruction).\n</Thinking>\n\n<Json>\n{\n  \"name\": \"Webhook to Notion and Discord\",\n  \"nodes\": [\n    {\n      \"parameters\": {\n        \"method\": \"POST\"\n      },\n      \"name\": \"webhook\",\n      \"type\": \"webhook\",\n      \"typeVersion\": 1,\n      \"position\": [100, 100]\n    },\n    {\n      \"parameters\": {\n        \"conditions\": [\n          {\n            \"value\": \"1\",\n            \"property\": \"type\"\n          }\n        ]\n      },\n      \"name\": \"switch\",\n      \"type\": \"switch\",\n      \"typeVersion\": 1,\n      \"position\": [300, 100]\n    },\n    {\n      \"parameters\": {\n        \"url\": \"={{$node.webhook.json.url}}\",\n        \"method\": \"GET\"\n      },\n      \"name\": \"httpRequest\",\n      \"type\": \"httpRequest\",\n      \"typeVersion\": 1,\n      \"position\": [500, 100]\n    },\n    {\n      \"parameters\": {\n        \"function\": \"const $ = require('cheerio');\\nconst html = $($.parseHTML(data));\\nreturn { title: html('title').text() };\"\n      },\n      \"name\": \"function\",\n      \"type\": \"function\",\n      \"typeVersion\": 1,\n      \"position\": [700, 100]\n    },\n    {\n      \"parameters\": {\n        \"databaseId\": \"notion-database-id\",\n        \"properties\": {\n          \"title\": \"={{$node.function.json.title}}\",\n          \"url\": \"={{$node.webhook.json.url}}\"\n        }\n      },\n      \"name\": \"notion\",\n      \"type\": \"notion\",\n      \"typeVersion\": 1,\n      \"position\": [900, 100]\n    },\n    {\n      \"parameters\": {\n        \"webhookUrl\": \"discord-webhook-url\",\n        \"content\": \"Added {{title}} ({{url}}) to Notion.\"\n      },\n      \"name\": \"discordWebhook\",\n      \"type\": \"discordWebhook\",\n      \"typeVersion\": 1,\n      \"position\": [1100, 100]\n    }\n  ],\n  \"connections\": {\n    \"webhook\": {\n      \"main\": [\n        [\n          { \"node\": \"switch\", \"type\": \"main\", \"index\": 0 }\n        ]\n      ]\n    },\n    \"switch\": {\n      \"main\": [\n        [\n          { \"node\": \"httpRequest\", \"type\": \"main\", \"index\": 0 }\n        ]\n      ]\n    },\n    \"httpRequest\": {\n      \"main\": [\n        [\n          { \"node\": \"function\", \"type\": \"main\", \"index\": 0 }\n        ]\n      ]\n    },\n    \"function\": {\n      \"main\": [\n        [\n          { \"node\": \"notion\", \"type\": \"main\", \"index\": 0 }\n        ]\n      ]\n    },\n    \"notion\": {\n      \"main\": [\n        [\n          { \"node\": \"discordWebhook\", \"type\": \"main\", \"index\": 0 }\n        ]\n      ]\n    }\n  },\n  \"active\": false\n}\n</Json>", "True", "False", "3.0", "{'json_parsed': True, 'top_level_keys_present': 4, 'workflow_name_valid': True, 'num_nodes': 6, 'num_nodes_with_valid_type': 0, 'num_nodes_with_valid_version': 0, 'num_nodes_with_valid_structure': 0, 'num_nodes_with_parameters': 6, 'num_connections': 5, 'num_connections_with_valid_structure': 5, 'num_connections_with_valid_target_node': 5, 'active_field_boolean': True}", "True", "4", "True", "6", "0", "0", "6", "6", "5", "5", "5", "True"], ["3", "Create an n8n workflow that triggers every minute using a Cron node. This trigger should then execute a Python 3 command located at C:\\\\Users\\\\<USER>\\\\Desktop\\\\redrive\\\\master\\\\main.py. The output from this command should then be sent as a message to <PERSON><PERSON> via a webhook, prefixed with 'Hello World!'. Additionally, the output from the executed command should also be passed to a Spreadsheet File node, with the 'Raw Data' option enabled.", "{\n  \"name\": \"Python to Discord and Spreadsheet\",\n  \"nodes\": [\n    {\n      \"parameters\": {\n        \"cronExpression\": \"*/1 * * * *\"\n      },\n      \"name\": \"CronTrigger\",\n      \"type\": \"cron\",\n      \"typeVersion\": 1,\n      \"position\": [100, 100]\n    },\n    {\n      \"parameters\": {\n        \"scriptPath\": \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\redrive\\\\master\\\\main.py\"\n      },\n      \"name\": \"ExecutePython\",\n      \"type\": \"executePythonScript\",\n      \"typeVersion\": 1,\n      \"position\": [300, 100]\n    },\n    {\n      \"parameters\": {\n        \"webhookUrl\": \"https://discord.com/api/webhooks/1234567890/abcdefghijklmnopqrstuvwxyz\",\n        \"message\": \"Hello World! {{ $json }}\"\n      },\n      \"name\": \"SendToDiscord\",\n      \"type\": \"discordWebhook\",\n      \"typeVersion\": 1,\n      \"position\": [500, 100]\n    },\n    {\n      \"parameters\": {\n        \"filePath\": \"C:\\\\output.xlsx\",\n        \"rawData\": true\n      },\n      \"name\": \"SaveToSpreadsheet\",\n      \"type\": \"spreadsheetFile\",\n      \"typeVersion\": 1,\n      \"position\": [300, 300]\n    }\n  ],\n  \"connections\": {\n    \"CronTrigger\": {\n      \"main\": [\n        [\n          { \"node\": \"ExecutePython\", \"type\": \"main\", \"index\": 0 }\n        ]\n      ]\n    },\n    \"ExecutePython\": {\n      \"main\": [\n        [\n          { \"node\": \"SendToDiscord\", \"type\": \"main\", \"index\": 0 }\n        ],\n        [\n          { \"node\": \"SaveToSpreadsheet\", \"type\": \"main\", \"index\": 0 }\n        ]\n      ]\n    }\n  },\n  \"active\": false\n}", "assistant\n<think>\nOkay, let's tackle this user's request. They want an n8n workflow that triggers every minute using a Cron node, runs a Python script, sends the output to Discord with a prefix, and saves the data to a spreadsheet.\n\nFirst, the trigger. The Cron node is needed here. The user specified it should trigger every minute, so the cron expression would be \"*/1 * * * *\". I'll set that in the parameters. The type is \"cron\" and version 1. The position can be something like [100, 100].\n\nNext, the Python script execution. The user provided the path: C:\\\\Users\\\\<USER>\\\\Desktop\\\\redrive\\\\master\\\\main.py. So I need a \"Execute Python Script\" node. The parameters should include the script path. The type is \"executePythonScript\" and version 1. Position might be [300, 100].\n\nThen, the output from the Python script needs to be sent to Discord. The \"Webhook\" node under Discord is required. The parameters need the webhook URL. But the user didn't provide one, so I'll use a placeholder. Also, the message should be prefixed with 'Hello World!'. So the message parameter would be \"Hello World! {{ $json }}\". Position could be [500, 100].\n\nAlso, the output should go to a Spreadsheet File node. The \"Spreadsheet File\" node with the \"Raw Data\" option enabled. The parameters should have the file path and enable raw data. Let's say the file is saved at \"C:\\\\output.xlsx\". The type is \"spreadsheetFile\" and version 1. Position [300, 300].\n\nNow, connections. The Cron node connects to the Python script node. Then, the Python script's output goes to both the Webhook and Spreadsheet nodes. So the connections would be from the Python node to Webhook and Spreadsheet. Each connection needs to be in the main array with index 0.\n\nWait, the user said the output from the command should be sent to Discord and also to the spreadsheet. So the Python node's output is passed to both. So the connections would be from the Python node to both the Webhook and Spreadsheet nodes. Each of those connections would be in the connections object.\n\nI need to make sure all node names are unique. Let's name them: CronTrigger, ExecutePython, SendToDiscord, SaveToSpreadsheet.\n\nCheck parameters for each node. For the Cron, the cron expression. For the Python script, the scriptPath. For the Webhook, the webhookUrl and message. For the Spreadsheet, the filePath and rawData.\n\nPositions should be set so they don't overlap. Maybe Cron at [100, 100], Python at [300, 100], Discord at [500, 100], Spreadsheet at [300, 300].\n\nConnections: CronTrigger outputs to ExecutePython. Then ExecutePython outputs to SendToDiscord and SaveToSpreadsheet. So in the connections object, the ExecutePython node's main output connects to both.\n\nI need to structure the JSON accordingly, ensuring all parameters are correctly filled, even with placeholders where necessary. Also, make sure the JSON is valid with proper commas and brackets.\n</think>\n\n<Thinking>\nStep-by-step reasoning:\n1. Goal: Create a workflow that triggers every minute, runs a Python script, sends output to Discord with a prefix, and saves data to a spreadsheet.\n2. Nodes: \n   - Cron (trigger) to schedule execution every minute\n   - Execute Python Script to run the specified file\n   - Discord Webhook to send messages\n   - Spreadsheet File to save raw data\n3. Connections: \n   - Cron → Execute Python Script\n   - Execute Python Script → Discord Webhook\n   - Execute Python Script → Spreadsheet File\n</Thinking>\n\n<Json>\n{\n  \"name\": \"Python to Discord and Spreadsheet\",\n  \"nodes\": [\n    {\n      \"parameters\": {\n        \"cronExpression\": \"*/1 * * * *\"\n      },\n      \"name\": \"CronTrigger\",\n      \"type\": \"cron\",\n      \"typeVersion\": 1,\n      \"position\": [100, 100]\n    },\n    {\n      \"parameters\": {\n        \"scriptPath\": \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\redrive\\\\master\\\\main.py\"\n      },\n      \"name\": \"ExecutePython\",\n      \"type\": \"executePythonScript\",\n      \"typeVersion\": 1,\n      \"position\": [300, 100]\n    },\n    {\n      \"parameters\": {\n        \"webhookUrl\": \"https://discord.com/api/webhooks/1234567890/abcdefghijklmnopqrstuvwxyz\",\n        \"message\": \"Hello World! {{ $json }}\"\n      },\n      \"name\": \"SendToDiscord\",\n      \"type\": \"discordWebhook\",\n      \"typeVersion\": 1,\n      \"position\": [500, 100]\n    },\n    {\n      \"parameters\": {\n        \"filePath\": \"C:\\\\output.xlsx\",\n        \"rawData\": true\n      },\n      \"name\": \"SaveToSpreadsheet\",\n      \"type\": \"spreadsheetFile\",\n      \"typeVersion\": 1,\n      \"position\": [300, 300]\n    }\n  ],\n  \"connections\": {\n    \"CronTrigger\": {\n      \"main\": [\n        [\n          { \"node\": \"ExecutePython\", \"type\": \"main\", \"index\": 0 }\n        ]\n      ]\n    },\n    \"ExecutePython\": {\n      \"main\": [\n        [\n          { \"node\": \"SendToDiscord\", \"type\": \"main\", \"index\": 0 }\n        ],\n        [\n          { \"node\": \"SaveToSpreadsheet\", \"type\": \"main\", \"index\": 0 }\n        ]\n      ]\n    }\n  },\n  \"active\": false\n}\n</Json>", "True", "False", "10.0", "{'json_parsed': True, 'top_level_keys_present': 4, 'workflow_name_valid': True, 'num_nodes': 4, 'num_nodes_with_valid_type': 0, 'num_nodes_with_valid_version': 0, 'num_nodes_with_valid_structure': 0, 'num_nodes_with_parameters': 4, 'num_connections': 3, 'num_connections_with_valid_structure': 3, 'num_connections_with_valid_target_node': 3, 'active_field_boolean': True}", "True", "4", "True", "4", "0", "0", "4", "4", "3", "3", "3", "True"], ["4", "Generate an n8n workflow that uses a Hubspot Trigger to listen for events, configured with appId 'dghert3' and a maximum of 5 concurrent requests, using the 'hubspot_trigger' credential.", "{\n  \"name\": \"Hubspot Event Listener\",\n  \"nodes\": [\n    {\n      \"parameters\": {\n        \"appId\": \"dghert3\",\n        \"maxConcurrentRequests\": 5,\n        \"credential\": \"hubspot_trigger\"\n      },\n      \"name\": \"HubspotTriggerNode\",\n      \"type\": \"hubspotTrigger\",\n      \"typeVersion\": 1,\n      \"position\": [200, 100]\n    }\n  ],\n  \"connections\": {},\n  \"active\": false\n}", "assistant\n<think>\nOkay, let's tackle this user's request. They want an n8n workflow that uses a Hubspot Trigger node. The parameters needed are appId 'dghert3', max 5 concurrent requests, and the credential 'hubspot_trigger'.\n\nFirst, I need to recall the structure of an n8n workflow JSON. The main components are name, nodes, connections, and active status. The nodes array includes each node's parameters, name, type, type version, and position. Connections define how nodes are linked.\n\nThe user specified a Hubspot Trigger node. The type should be 'hubspotTrigger' and the type version. I need to check what the correct type version is. Assuming it's 1, but I should confirm. The parameters for the Hubspot Trigger node would include the appId, maxConcurrentRequests, and the credential. The parameters object should have these keys.\n\nNext, the node name must be unique. Let's call it 'HubspotTriggerNode'. The position can be arbitrary, like [200, 100]. \n\nConnections: Since this is a trigger node, it might not need to connect to another node unless there's an action. But the user didn't mention any subsequent nodes, so maybe the workflow just has the trigger. However, n8n workflows typically have at least a trigger and an action. Wait, the user didn't specify any actions, just the trigger setup. But maybe they just want the trigger node configured. However, the connections might be empty if there's only one node. But the example in the rules shows a connection from a source to a target. Hmm, but if there's only the trigger, there's no connection. So maybe the connections array is empty. Wait, the user's example in the rules shows a connections object with a source node connected to a target. But if there's only one node, there's no connection. So in this case, the connections might be empty. But the user's instruction says to generate a valid workflow. Maybe the user expects that the trigger is set up, but without any further nodes. However, in n8n, a workflow must have at least a trigger and an action. But the user hasn't mentioned an action. Maybe they just want the trigger node configured, even if it's not connected to anything else. But the JSON structure requires connections. Wait, looking back at the user's example, the connections are an object where each key is a source node, and the value is the target. If there's only one node, there's no connection. So the connections object would be empty. But the user's example shows a connection between nodes. However, the user's request is only about the trigger node. So perhaps the workflow has only the trigger node, and no connections. But in n8n, a workflow can have a single trigger node. So the connections would be empty. But the JSON structure requires the connections key. So maybe the connections object is empty. Let me check the rules again. The connections must reference correct node names, but if there's only one node, there's no connection. So the connections object would be empty. \n\nWait, the user's example in the rules shows a connections object with a source node connected to a target. But if there's only one node, there's no connection. So in this case, the connections would be an empty object. But the user's example might have a connection. However, the user's request is only about the trigger node. So the workflow would have the trigger node and no connections. Therefore, the connections object is empty. \n\nNow, the parameters for the Hubspot Trigger node. The parameters should include appId: 'dghert3', maxConcurrentRequests: 5, and credential: 'hubspot_trigger'. The parameters object must have these keys. \n\nSo the node object would be:\n\n{\n  \"parameters\": {\n    \"appId\": \"dghert3\",\n    \"maxConcurrentRequests\": 5,\n    \"credential\": \"hubspot_trigger\"\n  },\n  \"name\": \"HubspotTriggerNode\",\n  \"type\": \"hubspotTrigger\",\n  \"typeVersion\": 1,\n  \"position\": [200, 100]\n}\n\nThen, the connections would be an empty object. But wait, the user's example shows a connections object with a source node. However, if there's only one node, there's no connection. So the connections would be {}.\n\nPutting it all together, the workflow name could be \"Hubspot Event Listener\". The active status is false by default. \n\nSo the JSON would be:\n\n{\n  \"name\": \"Hubspot Event Listener\",\n  \"nodes\": [\n    {\n      \"parameters\": {\n        \"appId\": \"dghert3\",\n        \"maxConcurrentRequests\": 5,\n        \"credential\": \"hubspot_trigger\"\n      },\n      \"name\": \"HubspotTriggerNode\",\n      \"type\": \"hubspotTrigger\",\n      \"typeVersion\": 1,\n      \"position\": [200, 100]\n    }\n  ],\n  \"connections\": {},\n  \"active\": false\n}\n\nWait, but the user's example in the rules shows a connections object with a source node. However, if there's only one node, there's no connection. So the connections object is empty. That's acceptable. \n\nI need to make sure that the parameters are correctly set. The Hubspot Trigger node's parameters include appId, maxConcurrentRequests, and credential. The user specified all three, so they are included. The typeVersion is 1, assuming that's the correct version. If the actual typeVersion is different, but since the user didn't specify, I'll go with 1. \n\nYes, this should meet the user's requirements.\n</think>\n\n<Thinking>\nStep-by-step reasoning:\n1. Goal: Create an n8n workflow that listens for Hubspot events with specific configuration parameters.\n2. Nodes: Use the \"hubspotTrigger\" node to listen for events. It requires parameters like appId, maxConcurrentRequests, and credential. No other nodes are needed since the user only specified the trigger.\n3. Connections: No connections are required because the workflow only contains a single trigger node.\n</Thinking>\n\n<Json>\n{\n  \"name\": \"Hubspot Event Listener\",\n  \"nodes\": [\n    {\n      \"parameters\": {\n        \"appId\": \"dghert3\",\n        \"maxConcurrentRequests\": 5,\n        \"credential\": \"hubspot_trigger\"\n      },\n      \"name\": \"HubspotTriggerNode\",\n      \"type\": \"hubspotTrigger\",\n      \"typeVersion\": 1,\n      \"position\": [200, 100]\n    }\n  ],\n  \"connections\": {},\n  \"active\": false\n}\n</Json>", "True", "False", "10.0", "{'json_parsed': True, 'top_level_keys_present': 4, 'workflow_name_valid': True, 'num_nodes': 1, 'num_nodes_with_valid_type': 0, 'num_nodes_with_valid_version': 0, 'num_nodes_with_valid_structure': 0, 'num_nodes_with_parameters': 1, 'num_connections': 0, 'num_connections_with_valid_structure': 0, 'num_connections_with_valid_target_node': 0, 'active_field_boolean': True}", "True", "4", "True", "1", "0", "0", "1", "1", "0", "0", "0", "True"]], "shape": {"columns": 19, "rows": 5}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>prompt</th>\n", "      <th>generated_json</th>\n", "      <th>generated_text</th>\n", "      <th>json_valid</th>\n", "      <th>n8n_valid</th>\n", "      <th>llm_score</th>\n", "      <th>evaluation_results</th>\n", "      <th>json_parsed</th>\n", "      <th>top_level_keys_present</th>\n", "      <th>workflow_name_valid</th>\n", "      <th>num_nodes</th>\n", "      <th>num_nodes_with_valid_type</th>\n", "      <th>num_nodes_with_valid_version</th>\n", "      <th>num_nodes_with_valid_structure</th>\n", "      <th>num_nodes_with_parameters</th>\n", "      <th>num_connections</th>\n", "      <th>num_connections_with_valid_structure</th>\n", "      <th>num_connections_with_valid_target_node</th>\n", "      <th>active_field_boolean</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Create an n8n workflow that sends a daily vega...</td>\n", "      <td>{\\n  \"name\": \"Daily Vegan Recipe to Telegram U...</td>\n", "      <td>assistant\\n&lt;think&gt;\\nOkay, let's tackle this us...</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>6.0</td>\n", "      <td>{'json_parsed': True, 'top_level_keys_present'...</td>\n", "      <td>True</td>\n", "      <td>4</td>\n", "      <td>True</td>\n", "      <td>11</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>11</td>\n", "      <td>11</td>\n", "      <td>10</td>\n", "      <td>10</td>\n", "      <td>10</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Create an n8n workflow that is triggered by a ...</td>\n", "      <td>{\\n  \"name\": \"WebhookToHTMLTable\",\\n  \"nodes\":...</td>\n", "      <td>assistant\\n&lt;think&gt;\\nOkay, let's tackle this us...</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>5.0</td>\n", "      <td>{'json_parsed': True, 'top_level_keys_present'...</td>\n", "      <td>True</td>\n", "      <td>4</td>\n", "      <td>True</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>4</td>\n", "      <td>4</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Generate an n8n workflow that listens for a we...</td>\n", "      <td>{\\n  \"name\": \"Webhook to Notion and Discord\",\\...</td>\n", "      <td>assistant\\n&lt;think&gt;\\nOkay, let's tackle this us...</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>3.0</td>\n", "      <td>{'json_parsed': True, 'top_level_keys_present'...</td>\n", "      <td>True</td>\n", "      <td>4</td>\n", "      <td>True</td>\n", "      <td>6</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>6</td>\n", "      <td>6</td>\n", "      <td>5</td>\n", "      <td>5</td>\n", "      <td>5</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Create an n8n workflow that triggers every min...</td>\n", "      <td>{\\n  \"name\": \"Python to Discord and Spreadshee...</td>\n", "      <td>assistant\\n&lt;think&gt;\\nOkay, let's tackle this us...</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>10.0</td>\n", "      <td>{'json_parsed': True, 'top_level_keys_present'...</td>\n", "      <td>True</td>\n", "      <td>4</td>\n", "      <td>True</td>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>4</td>\n", "      <td>4</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Generate an n8n workflow that uses a Hubspot T...</td>\n", "      <td>{\\n  \"name\": \"Hubspot Event Listener\",\\n  \"nod...</td>\n", "      <td>assistant\\n&lt;think&gt;\\nOkay, let's tackle this us...</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>10.0</td>\n", "      <td>{'json_parsed': True, 'top_level_keys_present'...</td>\n", "      <td>True</td>\n", "      <td>4</td>\n", "      <td>True</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>True</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                              prompt  \\\n", "0  Create an n8n workflow that sends a daily vega...   \n", "1  Create an n8n workflow that is triggered by a ...   \n", "2  Generate an n8n workflow that listens for a we...   \n", "3  Create an n8n workflow that triggers every min...   \n", "4  Generate an n8n workflow that uses a Hubspot T...   \n", "\n", "                                      generated_json  \\\n", "0  {\\n  \"name\": \"Daily Vegan Recipe to Telegram U...   \n", "1  {\\n  \"name\": \"WebhookToHTMLTable\",\\n  \"nodes\":...   \n", "2  {\\n  \"name\": \"Webhook to Notion and Discord\",\\...   \n", "3  {\\n  \"name\": \"Python to Discord and Spreadshee...   \n", "4  {\\n  \"name\": \"Hubspot Event Listener\",\\n  \"nod...   \n", "\n", "                                      generated_text  json_valid  n8n_valid  \\\n", "0  assistant\\n<think>\\nOkay, let's tackle this us...        True      False   \n", "1  assistant\\n<think>\\nOkay, let's tackle this us...        True      False   \n", "2  assistant\\n<think>\\nOkay, let's tackle this us...        True      False   \n", "3  assistant\\n<think>\\nOkay, let's tackle this us...        True      False   \n", "4  assistant\\n<think>\\nOkay, let's tackle this us...        True      False   \n", "\n", "   llm_score                                 evaluation_results  json_parsed  \\\n", "0        6.0  {'json_parsed': True, 'top_level_keys_present'...         True   \n", "1        5.0  {'json_parsed': True, 'top_level_keys_present'...         True   \n", "2        3.0  {'json_parsed': True, 'top_level_keys_present'...         True   \n", "3       10.0  {'json_parsed': True, 'top_level_keys_present'...         True   \n", "4       10.0  {'json_parsed': True, 'top_level_keys_present'...         True   \n", "\n", "   top_level_keys_present  workflow_name_valid  num_nodes  \\\n", "0                       4                 True         11   \n", "1                       4                 True          4   \n", "2                       4                 True          6   \n", "3                       4                 True          4   \n", "4                       4                 True          1   \n", "\n", "   num_nodes_with_valid_type  num_nodes_with_valid_version  \\\n", "0                          0                             0   \n", "1                          0                             0   \n", "2                          0                             0   \n", "3                          0                             0   \n", "4                          0                             0   \n", "\n", "   num_nodes_with_valid_structure  num_nodes_with_parameters  num_connections  \\\n", "0                              11                         11               10   \n", "1                               4                          4                3   \n", "2                               6                          6                5   \n", "3                               4                          4                3   \n", "4                               1                          1                0   \n", "\n", "   num_connections_with_valid_structure  \\\n", "0                                    10   \n", "1                                     3   \n", "2                                     5   \n", "3                                     3   \n", "4                                     0   \n", "\n", "   num_connections_with_valid_target_node  active_field_boolean  \n", "0                                      10                  True  \n", "1                                       3                  True  \n", "2                                       5                  True  \n", "3                                       3                  True  \n", "4                                       0                  True  "]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": 38, "id": "f9419ccf", "metadata": {}, "outputs": [], "source": ["df[\"percent_node_with_valid_type\"] = df[\"num_nodes_with_valid_type\"] / df[\"num_nodes\"] * 100\n", "df[\"percent_node_with_valid_version\"] = df[\"num_nodes_with_valid_version\"] / df[\"num_nodes\"] * 100\n", "df[\"percent_node_with_valid_structure\"] = df[\"num_nodes_with_valid_structure\"] / df[\"num_nodes\"] * 100\n", "df[\"percent_connections_with_valid_structure\"] = df[\"num_connections_with_valid_structure\"] / df[\"num_connections\"] * 100\n", "df[\"percent_connections_with_valid_target_node\"] = df[\"num_connections_with_valid_target_node\"] / df[\"num_connections\"] * 100\n", "df[\"percent_node_with_parameters\"] = df[\"num_nodes_with_parameters\"] / df[\"num_nodes\"] * 100"]}, {"cell_type": "code", "execution_count": 39, "id": "298bdacb", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='json_valid', ylabel='Count'>"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"json_valid\")"]}, {"cell_type": "code", "execution_count": 40, "id": "aeaeeef2", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='llm_score', ylabel='Count'>"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"llm_score\", bins=20)"]}, {"cell_type": "code", "execution_count": 41, "id": "8825d978", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.int64(18)"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["(df[\"llm_score\"]>=7).sum()"]}, {"cell_type": "code", "execution_count": 42, "id": "c1ecdd0c", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='top_level_keys_present', ylabel='Count'>"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"top_level_keys_present\")"]}, {"cell_type": "code", "execution_count": 43, "id": "3b2bfd01", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='workflow_name_valid', ylabel='Count'>"]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"workflow_name_valid\")"]}, {"cell_type": "code", "execution_count": 44, "id": "a18db11e", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_type', ylabel='Count'>"]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_type\")"]}, {"cell_type": "code", "execution_count": 45, "id": "3c7c398e", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_version', ylabel='Count'>"]}, "execution_count": 45, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_version\")"]}, {"cell_type": "code", "execution_count": 46, "id": "4c46bdcf", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_structure', ylabel='Count'>"]}, "execution_count": 46, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_structure\")"]}, {"cell_type": "code", "execution_count": 47, "id": "44883d67", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_parameters', ylabel='Count'>"]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_parameters\")"]}, {"cell_type": "code", "execution_count": 48, "id": "048db9e6", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_connections_with_valid_structure', ylabel='Count'>"]}, "execution_count": 48, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_connections_with_valid_structure\")"]}, {"cell_type": "code", "execution_count": 49, "id": "24225a83", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_connections_with_valid_target_node', ylabel='Count'>"]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_connections_with_valid_target_node\")"]}, {"cell_type": "code", "execution_count": 50, "id": "4ed22d8e", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='active_field_boolean', ylabel='Count'>"]}, "execution_count": 50, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAjMAAAGxCAYAAACXwjeMAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjAsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvlHJYcgAAAAlwSFlzAAAPYQAAD2EBqD+naQAAKUtJREFUeJzt3Xt4VPWdx/HPQMIQMAmESxIghCABCUikBJEochGCYaUgdovFIrToAyJQSpGVohCoJa0sKVtAlF0bwIJgRVh9ZIEIhIuAC2kit5RrENDENFxyQRgk+e0fPswaEy6JSWZ+8f16nnke5pwzZ75zUPP2zJmMwxhjBAAAYKk6nh4AAADg+yBmAACA1YgZAABgNWIGAABYjZgBAABWI2YAAIDViBkAAGA1YgYAAFjNx9MDVLeSkhJ98cUX8vf3l8Ph8PQ4AADgDhhjVFhYqBYtWqhOnVufe6n1MfPFF18oLCzM02MAAIBKOHv2rFq1anXLbTwaM0uWLNGSJUt0+vRpSVKnTp00c+ZMxcfHS/qmymbPnq2lS5fq4sWL6tGjhxYvXqxOnTrd8XP4+/tL+uZgBAQEVPlrAAAAVa+goEBhYWHun+O34tGYadWqlf7whz+oXbt2kqTly5dryJAhSk9PV6dOnfTqq68qKSlJy5YtU/v27fXKK69owIABOnr06B29OEnut5YCAgKIGQAALHMnl4g4vO2LJoOCgjRv3jz98pe/VIsWLTR58mT927/9myTJ5XIpODhYf/zjHzV27Ng72l9BQYECAwOVn59PzAAAYImK/Pz2mk8zFRcXa/Xq1bp8+bJ69uyprKws5eTkKC4uzr2N0+lU7969tXv37pvux+VyqaCgoNQNAADUXh6PmYMHD+quu+6S0+nUuHHjtG7dOkVFRSknJ0eSFBwcXGr74OBg97ryJCYmKjAw0H3j4l8AAGo3j8dMhw4dlJGRob179+q5557TqFGjdOTIEff6775XZoy55ftn06dPV35+vvt29uzZapsdAAB4nsc/ml2vXj33BcAxMTHat2+f/uM//sN9nUxOTo5CQ0Pd2+fm5pY5W/NtTqdTTqezeocGAABew+NnZr7LGCOXy6WIiAiFhIQoJSXFve7atWvavn27YmNjPTghAADwJh49M/Pb3/5W8fHxCgsLU2FhoVavXq3U1FRt3LhRDodDkydP1ty5cxUZGanIyEjNnTtXDRo00IgRIzw5NgAA8CIejZkvv/xSI0eOVHZ2tgIDA9WlSxdt3LhRAwYMkCRNmzZNV65c0fjx492/NG/z5s13/DtmAABA7ed1v2emqvF7ZgAAsI+Vv2cGAACgMogZAABgNWIGAABYjZgBAABWI2YAAIDVPP4bgG135swZ5eXleXoMAAA8omnTpmrdurVHZyBmvoczZ87onns66sqVrzw9CgAAHuHn10D/+EemR4OGmPke8vLydOXKV+rxy1kKCG3j6XEAAKhRBdmn9clfZisvL4+YsV1AaBsFte7g6TEAAPhB4gJgAABgNWIGAABYjZgBAABWI2YAAIDViBkAAGA1YgYAAFiNmAEAAFYjZgAAgNWIGQAAYDViBgAAWI2YAQAAViNmAACA1YgZAABgNWIGAABYjZgBAABWI2YAAIDViBkAAGA1YgYAAFiNmAEAAFYjZgAAgNWIGQAAYDViBgAAWI2YAQAAViNmAACA1YgZAABgNWIGAABYjZgBAABWI2YAAIDViBkAAGA1YgYAAFiNmAEAAFYjZgAAgNWIGQAAYDViBgAAWI2YAQAAViNmAACA1YgZAABgNWIGAABYjZgBAABWI2YAAIDViBkAAGA1j8ZMYmKiunfvLn9/fzVv3lxDhw7V0aNHS20zevRoORyOUrcHHnjAQxMDAABv49GY2b59u55//nnt3btXKSkpun79uuLi4nT58uVS2z366KPKzs523zZs2OChiQEAgLfx8eSTb9y4sdT95ORkNW/eXGlpaXr44Yfdy51Op0JCQmp6PAAAYAGvumYmPz9fkhQUFFRqeWpqqpo3b6727dvr2WefVW5u7k334XK5VFBQUOoGAABqL6+JGWOMpkyZooceekidO3d2L4+Pj9fKlSu1detWzZ8/X/v27VO/fv3kcrnK3U9iYqICAwPdt7CwsJp6CQAAwAM8+jbTt02YMEEHDhzQrl27Si0fPny4+8+dO3dWTEyMwsPD9eGHH2rYsGFl9jN9+nRNmTLFfb+goICgAQCgFvOKmJk4caLef/997dixQ61atbrltqGhoQoPD9fx48fLXe90OuV0OqtjTAAA4IU8GjPGGE2cOFHr1q1TamqqIiIibvuY8+fP6+zZswoNDa2BCQEAgLfz6DUzzz//vP76179q1apV8vf3V05OjnJycnTlyhVJUlFRkaZOnao9e/bo9OnTSk1N1eDBg9W0aVM9/vjjnhwdAAB4CY+emVmyZIkkqU+fPqWWJycna/To0apbt64OHjyoFStW6NKlSwoNDVXfvn21Zs0a+fv7e2BiAADgbTz+NtOt+Pn5adOmTTU0DQAAsJHXfDQbAACgMogZAABgNWIGAABYjZgBAABWI2YAAIDViBkAAGA1YgYAAFiNmAEAAFYjZgAAgNWIGQAAYDViBgAAWI2YAQAAViNmAACA1YgZAABgNWIGAABYjZgBAABWI2YAAIDViBkAAGA1YgYAAFiNmAEAAFYjZgAAgNWIGQAAYDViBgAAWI2YAQAAViNmAACA1YgZAABgNWIGAABYjZgBAABWI2YAAIDViBkAAGA1YgYAAFiNmAEAAFYjZgAAgNWIGQAAYDViBgAAWI2YAQAAViNmAACA1YgZAABgNWIGAABYjZgBAABWI2YAAIDViBkAAGA1YgYAAFiNmAEAAFYjZgAAgNWIGQAAYDViBgAAWI2YAQAAViNmAACA1TwaM4mJierevbv8/f3VvHlzDR06VEePHi21jTFGCQkJatGihfz8/NSnTx8dPnzYQxMDAABv49GY2b59u55//nnt3btXKSkpun79uuLi4nT58mX3Nq+++qqSkpK0aNEi7du3TyEhIRowYIAKCws9ODkAAPAWPp588o0bN5a6n5ycrObNmystLU0PP/ywjDFasGCBZsyYoWHDhkmSli9fruDgYK1atUpjx471xNgAAMCLeNU1M/n5+ZKkoKAgSVJWVpZycnIUFxfn3sbpdKp3797avXu3R2YEAADexaNnZr7NGKMpU6booYceUufOnSVJOTk5kqTg4OBS2wYHB+uzzz4rdz8ul0sul8t9v6CgoJomBgAA3sBrzsxMmDBBBw4c0Ntvv11mncPhKHXfGFNm2Q2JiYkKDAx038LCwqplXgAA4B28ImYmTpyo999/X9u2bVOrVq3cy0NCQiT9/xmaG3Jzc8ucrblh+vTpys/Pd9/Onj1bfYMDAACP82jMGGM0YcIEvffee9q6dasiIiJKrY+IiFBISIhSUlLcy65du6bt27crNja23H06nU4FBASUugEAgNrLo9fMPP/881q1apX++7//W/7+/u4zMIGBgfLz85PD4dDkyZM1d+5cRUZGKjIyUnPnzlWDBg00YsQIT44OAAC8hEdjZsmSJZKkPn36lFqenJys0aNHS5KmTZumK1euaPz48bp48aJ69OihzZs3y9/fv4anBQAA3sijMWOMue02DodDCQkJSkhIqP6BAACAdbziAmAAAIDKImYAAIDViBkAAGA1YgYAAFiNmAEAAFYjZgAAgNWIGQAAYDViBgAAWI2YAQAAViNmAACA1YgZAABgNWIGAABYjZgBAABWI2YAAIDViBkAAGA1YgYAAFiNmAEAAFYjZgAAgNWIGQAAYDViBgAAWI2YAQAAViNmAACA1YgZAABgNWIGAABYjZgBAABWI2YAAIDViBkAAGA1YgYAAFiNmAEAAFYjZgAAgNWIGQAAYDViBgAAWI2YAQAAVqtUzLRt21bnz58vs/zSpUtq27bt9x4KAADgTlUqZk6fPq3i4uIyy10ulz7//PPvPRQAAMCd8qnIxu+//777z5s2bVJgYKD7fnFxsbZs2aI2bdpU2XAAAAC3U6GYGTp0qCTJ4XBo1KhRpdb5+vqqTZs2mj9/fpUNBwAAcDsVipmSkhJJUkREhPbt26emTZtWy1AAAAB3qkIxc0NWVlZVzwEAAFAplYoZSdqyZYu2bNmi3Nxc9xmbG/7yl79878EAAADuRKViZvbs2ZozZ45iYmIUGhoqh8NR1XMBAADckUrFzOuvv65ly5Zp5MiRVT0PAABAhVTq98xcu3ZNsbGxVT0LAABAhVUqZp555hmtWrWqqmcBAACosEq9zXT16lUtXbpUH330kbp06SJfX99S65OSkqpkOAAAgNupVMwcOHBA9913nyTp0KFDpdZxMTAAAKhJlYqZbdu2VfUcAAAAlVKpa2YAAAC8RaXOzPTt2/eWbydt3bq10gMBAABURKVi5sb1Mjd8/fXXysjI0KFDh8p8ASUAAEB1qlTM/OlPfyp3eUJCgoqKiu54Pzt27NC8efOUlpam7OxsrVu3zv3N3JI0evRoLV++vNRjevToob1791ZmbAAAUAtV6TUzP//5zyv0vUyXL19WdHS0Fi1adNNtHn30UWVnZ7tvGzZsqIpRAQBALVHpL5osz549e1S/fv073j4+Pl7x8fG33MbpdCokJOT7jgYAAGqpSsXMsGHDSt03xig7O1v79+/Xyy+/XCWD3ZCamqrmzZurUaNG6t27t37/+9+refPmVfocAADAXpWKmcDAwFL369Spow4dOmjOnDmKi4urksGkb87c/Ou//qvCw8OVlZWll19+Wf369VNaWpqcTme5j3G5XHK5XO77BQUFVTYPAADwPpWKmeTk5Kqeo1zDhw93/7lz586KiYlReHi4PvzwwzJnh25ITEzU7Nmza2Q+AADged/rmpm0tDRlZmbK4XAoKipKXbt2raq5yhUaGqrw8HAdP378pttMnz5dU6ZMcd8vKChQWFhYtc4FAAA8p1Ixk5ubqyeffFKpqalq1KiRjDHKz89X3759tXr1ajVr1qyq55QknT9/XmfPnlVoaOhNt3E6nTd9CwoAANQ+lfpo9sSJE1VQUKDDhw/rwoULunjxog4dOqSCggJNmjTpjvdTVFSkjIwMZWRkSJKysrKUkZGhM2fOqKioSFOnTtWePXt0+vRppaamavDgwWratKkef/zxyowNAABqoUqdmdm4caM++ugjdezY0b0sKipKixcvrtAFwPv371ffvn3d92+8PTRq1CgtWbJEBw8e1IoVK3Tp0iWFhoaqb9++WrNmjfz9/SszNgAAqIUqFTMlJSXy9fUts9zX11clJSV3vJ8+ffrIGHPT9Zs2barMeAAA4AekUm8z9evXT7/61a/0xRdfuJd9/vnn+vWvf61HHnmkyoYDAAC4nUrFzKJFi1RYWKg2bdro7rvvVrt27RQREaHCwkItXLiwqmcEAAC4qUq9zRQWFqa///3vSklJ0T/+8Q8ZYxQVFaX+/ftX9XwAAAC3VKEzM1u3blVUVJT7t+oOGDBAEydO1KRJk9S9e3d16tRJO3furJZBAQAAylOhmFmwYIGeffZZBQQElFkXGBiosWPHKikpqcqGAwAAuJ0Kxcynn36qRx999Kbr4+LilJaW9r2HAgAAuFMVipkvv/yy3I9k3+Dj46N//vOf33soAACAO1WhmGnZsqUOHjx40/UHDhy45VcNAAAAVLUKxcygQYM0c+ZMXb16tcy6K1euaNasWXrssceqbDgAAIDbqdBHs1966SW99957at++vSZMmKAOHTrI4XAoMzNTixcvVnFxsWbMmFFdswIAAJRRoZgJDg7W7t279dxzz2n69OnuryJwOBwaOHCgXnvtNQUHB1fLoAAAAOWp8C/NCw8P14YNG3Tx4kWdOHFCxhhFRkaqcePG1TEfAADALVXqNwBLUuPGjdW9e/eqnAUAAKDCKvXdTAAAAN6CmAEAAFYjZgAAgNWIGQAAYDViBgAAWI2YAQAAViNmAACA1YgZAABgNWIGAABYjZgBAABWI2YAAIDViBkAAGA1YgYAAFiNmAEAAFYjZgAAgNWIGQAAYDViBgAAWI2YAQAAViNmAACA1YgZAABgNWIGAABYjZgBAABWI2YAAIDViBkAAGA1YgYAAFiNmAEAAFYjZgAAgNWIGQAAYDViBgAAWI2YAQAAViNmAACA1YgZAABgNWIGAABYjZgBAABWI2YAAIDViBkAAGA1YgYAAFjNozGzY8cODR48WC1atJDD4dD69etLrTfGKCEhQS1atJCfn5/69Omjw4cPe2ZYAADglTwaM5cvX1Z0dLQWLVpU7vpXX31VSUlJWrRokfbt26eQkBANGDBAhYWFNTwpAADwVj6efPL4+HjFx8eXu84YowULFmjGjBkaNmyYJGn58uUKDg7WqlWrNHbs2JocFQAAeCmvvWYmKytLOTk5iouLcy9zOp3q3bu3du/efdPHuVwuFRQUlLoBAIDay2tjJicnR5IUHBxcanlwcLB7XXkSExMVGBjovoWFhVXrnAAAwLO8NmZucDgcpe4bY8os+7bp06crPz/ffTt79mx1jwgAADzIo9fM3EpISIikb87QhIaGupfn5uaWOVvzbU6nU06ns9rnAwAA3sFrz8xEREQoJCREKSkp7mXXrl3T9u3bFRsb68HJAACAN/HomZmioiKdOHHCfT8rK0sZGRkKCgpS69atNXnyZM2dO1eRkZGKjIzU3Llz1aBBA40YMcKDUwMAAG/i0ZjZv3+/+vbt674/ZcoUSdKoUaO0bNkyTZs2TVeuXNH48eN18eJF9ejRQ5s3b5a/v7+nRgYAAF7GozHTp08fGWNuut7hcCghIUEJCQk1NxQAALCK114zAwAAcCeIGQAAYDViBgAAWI2YAQAAViNmAACA1YgZAABgNWIGAABYjZgBAABWI2YAAIDViBkAAGA1YgYAAFiNmAEAAFYjZgAAgNWIGQAAYDViBgAAWI2YAQAAViNmAACA1YgZAABgNWIGAABYjZgBAABWI2YAAIDViBkAAGA1YgYAAFiNmAEAAFYjZgAAgNWIGQAAYDViBgAAWI2YAQAAViNmAACA1YgZAABgNWIGAABYjZgBAABWI2YAAIDViBkAAGA1YgYAAFiNmAEAAFYjZgAAgNWIGQAAYDViBgAAWI2YAQAAViNmAACA1YgZAABgNWIGAABYjZgBAABWI2YAAIDViBkAAGA1YgYAAFiNmAEAAFYjZgAAgNW8OmYSEhLkcDhK3UJCQjw9FgAA8CI+nh7gdjp16qSPPvrIfb9u3boenAYAAHgbr48ZHx8fzsYAAICb8uq3mSTp+PHjatGihSIiIvTkk0/q1KlTnh4JAAB4Ea8+M9OjRw+tWLFC7du315dffqlXXnlFsbGxOnz4sJo0aVLuY1wul1wul/t+QUFBTY0LAAA8wKvPzMTHx+uJJ57Qvffeq/79++vDDz+UJC1fvvymj0lMTFRgYKD7FhYWVlPjAgAAD/DqmPmuhg0b6t5779Xx48dvus306dOVn5/vvp09e7YGJwQAADXNq99m+i6Xy6XMzEz16tXrpts4nU45nc4anAoAAHiSV5+ZmTp1qrZv366srCx98skn+slPfqKCggKNGjXK06MBAAAv4dVnZs6dO6ef/exnysvLU7NmzfTAAw9o7969Cg8P9/RoAADAS3h1zKxevdrTIwAAAC/n1W8zAQAA3A4xAwAArEbMAAAAqxEzAADAasQMAACwGjEDAACsRswAAACrETMAAMBqxAwAALAaMQMAAKxGzAAAAKsRMwAAwGrEDAAAsBoxAwAArEbMAAAAqxEzAADAasQMAACwGjEDAACsRswAAACrETMAAMBqxAwAALAaMQMAAKxGzAAAAKsRMwAAwGrEDAAAsBoxAwAArEbMAAAAqxEzAADAasQMAACwGjEDAACsRswAAACrETMAAMBqxAwAALAaMQMAAKxGzAAAAKsRMwAAwGrEDAAAsBoxAwAArEbMAAAAqxEzAADAasQMAACwGjEDAACsRswAAACrETMAAMBqxAwAALAaMQMAAKxGzAAAAKsRMwAAwGrEDAAAsBoxAwAArGZFzLz22muKiIhQ/fr11a1bN+3cudPTIwEAAC/h9TGzZs0aTZ48WTNmzFB6erp69eql+Ph4nTlzxtOjAQAAL+D1MZOUlKQxY8bomWeeUceOHbVgwQKFhYVpyZIlnh4NAAB4Aa+OmWvXriktLU1xcXGllsfFxWn37t0emgoAAHgTH08PcCt5eXkqLi5WcHBwqeXBwcHKyckp9zEul0sul8t9Pz8/X5JUUFBQ5fMVFRVJki58dlTXXVeqfP8AAHizgpxvLvkoKiqq8p+zN/ZnjLnttl4dMzc4HI5S940xZZbdkJiYqNmzZ5dZHhYWVi2zSVLaX/9QbfsGAMDb9e7du9r2XVhYqMDAwFtu49Ux07RpU9WtW7fMWZjc3NwyZ2tumD59uqZMmeK+X1JSogsXLqhJkyY3DaAfkoKCAoWFhens2bMKCAjw9Di1Fse5ZnCcawbHuWZwnEszxqiwsFAtWrS47bZeHTP16tVTt27dlJKSoscff9y9PCUlRUOGDCn3MU6nU06ns9SyRo0aVeeYVgoICOBflhrAca4ZHOeawXGuGRzn/3e7MzI3eHXMSNKUKVM0cuRIxcTEqGfPnlq6dKnOnDmjcePGeXo0AADgBbw+ZoYPH67z589rzpw5ys7OVufOnbVhwwaFh4d7ejQAAOAFvD5mJGn8+PEaP368p8eoFZxOp2bNmlXmrThULY5zzeA41wyOc83gOFeew9zJZ54AAAC8lFf/0jwAAIDbIWYAAIDViBkAAGA1YqaWee211xQREaH69eurW7du2rlz5y23d7lcmjFjhsLDw+V0OnX33XfrL3/5Sw1Na7eKHuuVK1cqOjpaDRo0UGhoqH7xi1/o/PnzNTStnXbs2KHBgwerRYsWcjgcWr9+/W0fs337dnXr1k3169dX27Zt9frrr1f/oJar6HF+7733NGDAADVr1kwBAQHq2bOnNm3aVDPDWqwy/zzf8PHHH8vHx0f33Xdftc1nM2KmFlmzZo0mT56sGTNmKD09Xb169VJ8fLzOnDlz08f89Kc/1ZYtW/Tmm2/q6NGjevvtt3XPPffU4NR2quix3rVrl55++mmNGTNGhw8f1t/+9jft27dPzzzzTA1PbpfLly8rOjpaixYtuqPts7KyNGjQIPXq1Uvp6en67W9/q0mTJmnt2rXVPKndKnqcd+zYoQEDBmjDhg1KS0tT3759NXjwYKWnp1fzpHar6HG+IT8/X08//bQeeeSRapqsFjCoNe6//34zbty4Usvuuece8+KLL5a7/f/8z/+YwMBAc/78+ZoYr1ap6LGeN2+eadu2ballf/7zn02rVq2qbcbaRpJZt27dLbeZNm2aueeee0otGzt2rHnggQeqcbLa5U6Oc3mioqLM7Nmzq36gWqoix3n48OHmpZdeMrNmzTLR0dHVOpetODNTS1y7dk1paWmKi4srtTwuLk67d+8u9zHvv/++YmJi9Oqrr6ply5Zq3769pk6dqitX+AbwW6nMsY6NjdW5c+e0YcMGGWP05Zdf6t1339W//Mu/1MTIPxh79uwp8/cycOBA7d+/X19//bWHpqr9SkpKVFhYqKCgIE+PUuskJyfr5MmTmjVrlqdH8WpW/NI83F5eXp6Ki4vLfAFncHBwmS/qvOHUqVPatWuX6tevr3Xr1ikvL0/jx4/XhQsXuG7mFipzrGNjY7Vy5UoNHz5cV69e1fXr1/XjH/9YCxcurImRfzBycnLK/Xu5fv268vLyFBoa6qHJarf58+fr8uXL+ulPf+rpUWqV48eP68UXX9TOnTvl48OP61vhzEwt891vBjfG3PTbwktKSuRwOLRy5Urdf//9GjRokJKSkrRs2TLOztyBihzrI0eOaNKkSZo5c6bS0tK0ceNGZWVl8R1j1aC8v5fylqNqvP3220pISNCaNWvUvHlzT49TaxQXF2vEiBGaPXu22rdv7+lxvB6pV0s0bdpUdevWLXNmIDc3t8z/qd4QGhqqli1blvpW0o4dO8oYo3PnzikyMrJaZ7ZVZY51YmKiHnzwQb3wwguSpC5duqhhw4bq1auXXnnlFc4YVJGQkJBy/158fHzUpEkTD01Ve61Zs0ZjxozR3/72N/Xv39/T49QqhYWF2r9/v9LT0zVhwgRJ3/wPqDFGPj4+2rx5s/r16+fhKb0HZ2ZqiXr16qlbt25KSUkptTwlJUWxsbHlPubBBx/UF198oaKiIveyY8eOqU6dOmrVqlW1zmuzyhzrr776SnXqlP7XrW7dupL+/8wBvr+ePXuW+XvZvHmzYmJi5Ovr66Gpaqe3335bo0eP1qpVq7j2qxoEBATo4MGDysjIcN/GjRunDh06KCMjQz169PD0iN7Fgxcfo4qtXr3a+Pr6mjfffNMcOXLETJ482TRs2NCcPn3aGGPMiy++aEaOHOnevrCw0LRq1cr85Cc/MYcPHzbbt283kZGR5plnnvHUS7BGRY91cnKy8fHxMa+99po5efKk2bVrl4mJiTH333+/p16CFQoLC016erpJT083kkxSUpJJT083n332mTGm7HE+deqUadCggfn1r39tjhw5Yt58803j6+tr3n33XU+9BCtU9DivWrXK+Pj4mMWLF5vs7Gz37dKlS556CVao6HH+Lj7NdHPETC2zePFiEx4eburVq2d+9KMfme3bt7vXjRo1yvTu3bvU9pmZmaZ///7Gz8/PtGrVykyZMsV89dVXNTy1nSp6rP/85z+bqKgo4+fnZ0JDQ81TTz1lzp07V8NT22Xbtm1GUpnbqFGjjDHlH+fU1FTTtWtXU69ePdOmTRuzZMmSmh/cMhU9zr17977l9ihfZf55/jZi5ub41mwAAGA1rpkBAABWI2YAAIDViBkAAGA1YgYAAFiNmAEAAFYjZgAAgNWIGQAAYDViBgAAWI2YAX7gHA6H1q9fX2PPt379erVr105169bV5MmTtWzZMjVq1KhC+7jdzKdPn5bD4VBGRsZt95WamiqHw6FLly5VaIaKqszrBHBn+NZs4AciISFB69evL/MDPjs7W40bN66xOcaOHatf/OIXmjRpkvz9/eXj46NBgwbV2PMDqH2IGeAHLiQkpMaeq6ioSLm5uRo4cKBatGjhXu7n51djMwCofXibCbDIxo0b9dBDD6lRo0Zq0qSJHnvsMZ08edK9/ty5c3ryyScVFBSkhg0bKiYmRp988omWLVum2bNn69NPP5XD4ZDD4dCyZcsklX7LpmfPnnrxxRdLPec///lP+fr6atu2bZKka9euadq0aWrZsqUaNmyoHj16KDU19bazp6amyt/fX5LUr18/ORwOpaamlvv2ywcffKBu3bqpfv36atu2rWbPnq3r16/fdN//+7//q65du6p+/fqKiYlRenr6bef5ro8//ljR0dGqX7++evTooYMHD5Zav3btWnXq1ElOp1Nt2rTR/PnzS62/ePGinn76aTVu3FgNGjRQfHy8jh8/fsvnvN3rTEpK0r333quGDRsqLCxM48ePV1FRkXv9jWO3adMmdezYUXfddZceffRRZWdnV/j1A1bz9DddArhz7777rlm7dq05duyYSU9PN4MHDzb33nuvKS4uNoWFhaZt27amV69eZufOneb48eNmzZo1Zvfu3earr74yv/nNb0ynTp1Mdna2yc7Odn87uiSzbt06Y4wxCxcuNK1btzYlJSXu51y4cKFp2bKlKS4uNsYYM2LECBMbG2t27NhhTpw4YebNm2ecTqc5duzYLWd3uVzm6NGjRpJZu3atyc7ONi6XyyQnJ5vAwED3dhs3bjQBAQFm2bJl5uTJk2bz5s2mTZs2JiEhwb3Nt2cuKioyzZo1M8OHDzeHDh0yH3zwgWnbtq2RZNLT0297TG98k3HHjh3N5s2bzYEDB8xjjz1m2rRpY65du2aMMWb//v2mTp06Zs6cOebo0aMmOTnZ+Pn5meTkZPd+fvzjH5uOHTuaHTt2mIyMDDNw4EDTrl079z4q8zr/9Kc/ma1bt5pTp06ZLVu2mA4dOpjnnnvOvT45Odn4+vqa/v37m3379pm0tDTTsWNHM2LEiNu+bqA2IWYAi+Xm5hpJ5uDBg+aNN94w/v7+5vz58+VuO2vWLBMdHV1m+bfDIDc31/j4+JgdO3a41/fs2dO88MILxhhjTpw4YRwOh/n8889L7eORRx4x06dPv+28Fy9eNJLMtm3b3Mu++0O+V69eZu7cuaUe99Zbb5nQ0NByZ37jjTdMUFCQuXz5snv9kiVLKhwzq1evdi87f/688fPzM2vWrDHGfBNwAwYMKPW4F154wURFRRljjDl27JiRZD7++GP3+ry8POPn52feeeedSr/O73rnnXdMkyZN3PeTk5ONJHPixAn3ssWLF5vg4ODbvm6gNuGaGcAiJ0+e1Msvv6y9e/cqLy9PJSUlkqQzZ84oIyNDXbt2VVBQUKX336xZMw0YMEArV65Ur169lJWVpT179mjJkiWSpL///e8yxqh9+/alHudyudSkSZPKv7BvSUtL0759+/T73//evay4uFhXr17VV199pQYNGpTaPjMzU9HR0aWW9+zZs8LP++3HBAUFqUOHDsrMzHQ/x5AhQ0pt/+CDD2rBggUqLi5WZmamfHx81KNHD/f6Jk2alNpHZV7ntm3bNHfuXB05ckQFBQW6fv26rl69qsuXL6thw4aSpAYNGujuu+927yM0NFS5ubkVfv2AzYgZwCKDBw9WWFiY/vM//1MtWrRQSUmJOnfurGvXrlXZRbRPPfWUfvWrX2nhwoVatWqVOnXqpOjoaElSSUmJ6tatq7S0NNWtW7fU4+66664qef6SkhLNnj1bw4YNK7Oufv36ZZYZY6rkecvjcDjcz3Hjz+U9781mKO9xN9zudX722WcaNGiQxo0bp9/97ncKCgrSrl27NGbMGH399dfubX19fcvMXJ3HBPBGxAxgifPnzyszM1NvvPGGevXqJUnatWuXe32XLl30X//1X7pw4UK5Z2fq1aun4uLi2z7P0KFDNXbsWG3cuFGrVq3SyJEj3eu6du2q4uJi5ebmumeoaj/60Y909OhRtWvX7o62j4qK0ltvvaUrV664g27v3r0Vft69e/eqdevWkr65mPfYsWO655573M/x7WMtSbt371b79u1Vt25dRUVF6fr16/rkk08UGxsr6Zu/r2PHjqljx46Vep379+/X9evXNX/+fNWp881nNd55550Kvy7gh4BPMwGWaNy4sZo0aaKlS5fqxIkT2rp1q6ZMmeJe/7Of/UwhISEaOnSoPv74Y506dUpr167Vnj17JElt2rRRVlaWMjIylJeXJ5fLVe7zNGzYUEOGDNHLL7+szMxMjRgxwr2uffv2euqpp/T000/rvffeU1ZWlvbt26c//vGP2rBhQ5W8zpkzZ2rFihVKSEjQ4cOHlZmZqTVr1uill14qd/sRI0aoTp06GjNmjI4cOaINGzbo3//93yv8vHPmzNGWLVt06NAhjR49Wk2bNtXQoUMlSb/5zW+0ZcsW/e53v9OxY8e0fPlyLVq0SFOnTpUkRUZGasiQIXr22We1a9cuffrpp/r5z3+uli1blnl76k5f5913363r169r4cKFOnXqlN566y29/vrrFX5dwA+CB6/XAVBBKSkppmPHjsbpdJouXbqY1NTUUhfDnj592jzxxBMmICDANGjQwMTExJhPPvnEGGPM1atXzRNPPGEaNWpkJLk/ifPtx9/w4YcfGknm4YcfLjPDtWvXzMyZM02bNm2Mr6+vCQkJMY8//rg5cODAbee/kwuAjfnmkz6xsbHGz8/PBAQEmPvvv98sXbrUvf67M+/Zs8dER0ebevXqmfvuu8+sXbu2whcAf/DBB6ZTp06mXr16pnv37iYjI6PUdu+++66Jiooyvr6+pnXr1mbevHml1l+4cMGMHDnSBAYGGj8/PzNw4MBSn/CqzOtMSkoyoaGh7v2tWLHCSDIXL1686T7XrVtn+E87fmgcxvDmKgAAsBdvMwEAAKsRMwCqTHx8vO66665yb3PnzvXITOPGjbvpTOPGjfPITACqFm8zAagyn3/+ua5cuVLuuqCgoO/1O3AqKzc3VwUFBeWuCwgIUPPmzWt4IgBVjZgBAABW420mAABgNWIGAABYjZgBAABWI2YAAIDViBkAAGA1YgYAAFiNmAEAAFYjZgAAgNX+DzLsgwgfbfbbAAAAAElFTkSuQmCC", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"active_field_boolean\")"]}, {"cell_type": "code", "execution_count": null, "id": "9bb20feb", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}