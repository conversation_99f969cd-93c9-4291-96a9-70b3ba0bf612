import json
from typing import Any, Dict, <PERSON>, <PERSON>ple


def load_and_evaluate_json_file(file_path: str) -> None:
    """Load JSON file, evaluate each entry, and add evaluation results."""
    try:
        # Load the JSON file
        with open(file_path, "r") as f:
            data = json.load(f)

        # Process each entry
        for i, entry in enumerate(data):
            if "generated_json" in entry:
                # Parse the generated JSON string
                try:
                    generated_workflow = json.loads(entry["generated_json"])
                    # Run evaluation
                    evaluation_results = evaluate_n8n_workflow(generated_workflow)
                    # Add evaluation results to the entry
                    entry.update(evaluation_results)
                    print(
                        f"Entry {i+1}: Evaluated entry with prompt: {entry.get('prompt', 'Unknown')[:50]}..."
                    )
                    print(f"Entry {i+1}: Evaluation results: {evaluation_results}")
                except json.JSONDecodeError as e:
                    print(f"Entry {i+1}: Failed to parse generated_json: {e}")
                    entry["evaluation_results"] = {"error": "Invalid JSON"}
            else:
                print(f"Entry {i+1}: No 'generated_json' field found")

        # Save back to the same file
        with open(file_path, "w") as f:
            json.dump(data, f, indent=2)

        print(f"Successfully updated {file_path} with evaluation results")

    except Exception as e:
        print(f"Error processing file: {e}")


def load_node_types_and_versions():
    """Parse the n8n_nodes_list.txt file to extract node types and their valid versions."""
    node_types = set()
    node_versions = {}

    try:
        with open("n8n_nodes_list.txt", "r") as f:
            for line in f:
                line = line.strip()
                if line.startswith("- ") and " : " in line:
                    # Parse format: "- node_type : version1,version2,version3"
                    parts = line[2:].split(
                        " : ", 1
                    )  # Remove "- " prefix and split on " : "
                    if len(parts) == 2:
                        node_type = parts[0].strip()
                        versions_str = parts[1].strip()

                        # Parse versions (comma-separated)
                        versions = [v.strip() for v in versions_str.split(",")]

                        node_types.add(node_type)
                        node_versions[node_type] = versions

    except FileNotFoundError:
        print("Warning: n8n_nodes_list.txt not found, falling back to empty lists")

    return node_types, node_versions


VALID_N8N_NODE_TYPES, VALID_N8N_NODE_VERSIONS = load_node_types_and_versions()


def evaluate_n8n_workflow(workflow_json_string: str) -> Dict[str, Any]:
    """
    Modular evaluation function for n8n workflow validation.
    Returns a dictionary of interpretable evaluation metrics.

    Args:
        workflow_json_string: JSON string or dict of the workflow to evaluate

    Returns:
        Dictionary of evaluation metrics.
    """
    metrics = {
        "json_parsed": False,
        "top_level_keys_present": 0,
        "workflow_name_valid": False,
        "num_nodes": 0,
        "num_nodes_with_valid_type": 0,
        "num_nodes_with_valid_version": 0,
        "num_nodes_with_valid_structure": 0,
        "num_nodes_with_parameters": 0,
        "num_connections": 0,
        "num_connections_with_valid_structure": 0,
        "num_connections_with_valid_target_node": 0,
        "active_field_boolean": False,
    }

    try:
        workflow = (
            json.loads(workflow_json_string)
            if isinstance(workflow_json_string, str)
            else workflow_json_string
        )
        metrics["json_parsed"] = True
    except Exception:
        return metrics

    if not isinstance(workflow, dict):
        return metrics

    # Top-level keys
    required_keys = ["name", "nodes", "connections", "active"]
    metrics["top_level_keys_present"] = sum(
        1 for key in required_keys if key in workflow
    )

    # Workflow name
    name = workflow.get("name")
    if isinstance(name, str) and name.strip():
        metrics["workflow_name_valid"] = True

    # Nodes
    nodes = workflow.get("nodes", [])
    node_names = set()
    node_ids = set()

    if isinstance(nodes, list):
        metrics["num_nodes"] = len(nodes)

        for node in nodes:
            if validate_node_structure(node) >= 1.0:
                metrics["num_nodes_with_valid_structure"] += 1

            node_type = node.get("type")
            if node_type in VALID_N8N_NODE_TYPES:
                metrics["num_nodes_with_valid_type"] += 1

            version = node.get("typeVersion")
            if validate_node_version(node_type, version) >= 1.0:
                metrics["num_nodes_with_valid_version"] += 1

            if isinstance(node.get("parameters"), dict):
                metrics["num_nodes_with_parameters"] += 1

            # Collect identifiers
            name = node.get("name")
            node_id = node.get("id")
            if isinstance(name, str) and name.strip():
                node_names.add(name)
            if isinstance(node_id, str) and node_id.strip():
                node_ids.add(node_id)

    node_identifiers = node_names.union(node_ids)

    # Connections
    connections = workflow.get("connections", {})
    if isinstance(connections, dict):
        (
            valid_structure,
            valid_targets,
            total_connections,
        ) = validate_connections_structure(connections, node_identifiers)

        metrics["num_connections"] = total_connections
        metrics["num_connections_with_valid_structure"] = valid_structure
        metrics["num_connections_with_valid_target_node"] = valid_targets

    # Active field
    if isinstance(workflow.get("active"), bool):
        metrics["active_field_boolean"] = True

    return metrics


def validate_node_structure(node: Dict[str, Any]) -> float:
    """Check whether a node contains required fields with correct types."""
    if not isinstance(node, dict):
        return 0.0

    score = 0.0
    required_fields = ["name", "type", "typeVersion", "position"]

    for field in required_fields:
        if field in node:
            if (
                field == "position"
                and isinstance(node[field], list)
                and len(node[field]) >= 2
            ):
                score += 0.25
            elif field == "typeVersion" and isinstance(node[field], (int, float)):
                score += 0.25
            elif (
                field in ["name", "type"]
                and isinstance(node[field], str)
                and node[field].strip()
            ):
                score += 0.25

    return score


def validate_node_version(node_type: str, node_type_version: Any) -> float:
    """Check if the version is valid for the given node type."""
    if not isinstance(node_type, str):
        return 0.0

    if node_type not in VALID_N8N_NODE_TYPES:
        return 0.0

    valid_versions = VALID_N8N_NODE_VERSIONS[node_type]
    version_str = str(node_type_version) if node_type_version is not None else ""

    if version_str in valid_versions:
        return 1.0
    elif isinstance(node_type_version, (int, float, str)):
        return 0.3

    return 0.0


def validate_connections_structure(
    connections: Dict[str, Any], node_names: Set[str]
) -> Tuple[int, int, int]:
    """
    Validate the connections structure.
    Returns:
        num_connections_with_valid_structure,
        num_connections_with_valid_target_node,
        total_connections
    """
    if not isinstance(connections, dict):
        return 0, 0, 0

    valid_structure = 0
    valid_target = 0
    total_connections = 0

    for source_node, connection_data in connections.items():
        if not isinstance(connection_data, dict):
            continue

        for connection_type, connection_list in connection_data.items():
            if not isinstance(connection_list, list):
                continue

            for connection_group in connection_list:
                if not isinstance(connection_group, list):
                    continue

                for connection in connection_group:
                    total_connections += 1
                    if isinstance(connection, dict):
                        if all(k in connection for k in ("node", "type", "index")):
                            valid_structure += 1
                            target = connection.get("node")
                            if isinstance(target, str) and target in node_names:
                                valid_target += 1

    return valid_structure, valid_target, total_connections


if __name__ == "__main__":
    # Load and evaluate the specified file
    load_and_evaluate_json_file(
        "sample_similarity_jsons.json"
    )
