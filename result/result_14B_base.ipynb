{"cells": [{"cell_type": "code", "execution_count": 1, "id": "ef677f77", "metadata": {}, "outputs": [], "source": ["import json\n", "import pandas as pd\n", "import plotly.express as px\n", "import seaborn as sns"]}, {"cell_type": "code", "execution_count": 2, "id": "4773c4da", "metadata": {}, "outputs": [], "source": ["df = json.load(open(\"qwen3-14b-base.json\", \"r\"))"]}, {"cell_type": "code", "execution_count": 3, "id": "dabd736f", "metadata": {}, "outputs": [], "source": ["df = pd.DataFrame(df)"]}, {"cell_type": "code", "execution_count": 4, "id": "174e52c7", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.int64(29)"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"json_valid\"].sum()"]}, {"cell_type": "code", "execution_count": 5, "id": "bd1e4e91", "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['prompt', 'generated_json', 'generated_text', 'json_valid', 'n8n_valid',\n", "       'llm_score'],\n", "      dtype='object')"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["df.columns"]}, {"cell_type": "code", "execution_count": 8, "id": "67814b04", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.int64(18)"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["(df[\"llm_score\"]>=7).sum()"]}, {"cell_type": "code", "execution_count": 6, "id": "d3d78394", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='llm_score', ylabel='Count'>"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"llm_score\", bins=10)"]}, {"cell_type": "code", "execution_count": 7, "id": "0110dd14", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='llm_score', ylabel='Count'>"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df[df[\"json_valid\"]==True], x=\"llm_score\", bins=10)"]}, {"cell_type": "code", "execution_count": null, "id": "3dcc4418", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}