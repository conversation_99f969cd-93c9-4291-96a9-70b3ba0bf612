{"cells": [{"cell_type": "code", "execution_count": 2, "id": "ef677f77", "metadata": {}, "outputs": [], "source": ["import json\n", "import pandas as pd\n", "import plotly.express as px\n", "import seaborn as sns"]}, {"cell_type": "code", "execution_count": 3, "id": "4773c4da", "metadata": {}, "outputs": [], "source": ["df = json.load(open(\"qwen3-14b-phase1.json\", \"r\"))"]}, {"cell_type": "code", "execution_count": 4, "id": "dabd736f", "metadata": {}, "outputs": [], "source": ["df = pd.DataFrame(df)"]}, {"cell_type": "code", "execution_count": 5, "id": "174e52c7", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.int64(20)"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["df[\"json_valid\"].sum()"]}, {"cell_type": "code", "execution_count": 6, "id": "bd1e4e91", "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['prompt', 'generated_json', 'generated_text', 'json_valid', 'n8n_valid',\n", "       'llm_score'],\n", "      dtype='object')"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["df.columns"]}, {"cell_type": "code", "execution_count": 7, "id": "b2b85b32", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.int64(17)"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["(df[\"llm_score\"]>=7).sum()"]}, {"cell_type": "code", "execution_count": 8, "id": "d3d78394", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='llm_score', ylabel='Count'>"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAjMAAAGxCAYAAACXwjeMAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjAsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvlHJYcgAAAAlwSFlzAAAPYQAAD2EBqD+naQAAJCBJREFUeJzt3XlwlPXhx/HPkoRNwCQQaC5JSBAkEE4JOgJKKBiLQFUqVpCjHh2QM6RFiKAcFqJ0RFqj2DgVbBVlpirSemBE5CioIRhFRCmKJOUwjdIs4VggeX5/OOzPlASSsNnn+cb3a2Zn2Gd3k487tHnPsxvWZVmWJQAAAEM1s3sAAADApSBmAACA0YgZAABgNGIGAAAYjZgBAABGI2YAAIDRiBkAAGA0YgYAABgt2O4Bja2qqkqHDh1SeHi4XC6X3XMAAEAdWJalY8eOKT4+Xs2aXfjcS5OPmUOHDikhIcHuGQAAoAFKSkrUrl27C96nycdMeHi4pO+fjIiICJvXAACAuvB4PEpISPD9HL+QJh8z515aioiIIGYAADBMXd4iwhuAAQCA0YgZAABgNGIGAAAYjZgBAABGI2YAAIDRiBkAAGA0YgYAABiNmAEAAEYjZgAAgNGIGQAAYDRiBgAAGI2YAQAARiNmAACA0YgZAABgtGC7BwAAgO8VFxerrKzM7hn10rZtWyUmJtq6gZgBAMABiouLlZLSRSdPnrB7Sr2EhbXQ55/vsTVoiBkAABygrKxMJ0+e0DV3z1dEXJLdc+rEc/hrffDsQpWVlREzAADgexFxSYpK7Gz3DKPwBmAAAGA0YgYAABiNmAEAAEYjZgAAgNGIGQAAYDRiBgAAGI2YAQAARiNmAACA0YgZAABgNGIGAAAYjZgBAABGI2YAAIDRiBkAAGA0YgYAABiNmAEAAEYjZgAAgNFsjZnNmzdrxIgRio+Pl8vl0tq1a2u978SJE+VyubR8+fKA7QMAAM5na8wcP35cPXv2VG5u7gXvt3btWn3wwQeKj48P0DIAAGCKYDu/+dChQzV06NAL3ufgwYOaOnWq1q9fr2HDhgVoGQAAMIWtMXMxVVVVGjdunGbNmqXU1NQ6Pcbr9crr9fquezyexpoHAAAcwNFvAH700UcVHBys6dOn1/kxOTk5ioyM9F0SEhIacSEAALCbY2OmsLBQf/jDH7Rq1Sq5XK46Py47O1vl5eW+S0lJSSOuBAAAdnNszGzZskWlpaVKTExUcHCwgoODdeDAAf3mN79RUlJSrY9zu92KiIiodgEAAE2XY98zM27cOA0ZMqTasRtvvFHjxo3TXXfdZdMqAADgNLbGTEVFhfbt2+e7vn//fhUVFSkqKkqJiYlq06ZNtfuHhIQoNjZWnTt3DvRUAADgULbGzI4dOzRo0CDf9aysLEnShAkTtGrVKptWAQAAk9gaM+np6bIsq873//rrrxtvDAAAMJJj3wAMAABQF8QMAAAwGjEDAACMRswAAACjETMAAMBoxAwAADAaMQMAAIxGzAAAAKMRMwAAwGjEDAAAMBoxAwAAjEbMAAAAoxEzAADAaMQMAAAwGjEDAACMRswAAACjETMAAMBoxAwAADAaMQMAAIxGzAAAAKMRMwAAwGjEDAAAMBoxAwAAjEbMAAAAoxEzAADAaMQMAAAwGjEDAACMRswAAACjETMAAMBoxAwAADAaMQMAAIxGzAAAAKMRMwAAwGjEDAAAMBoxAwAAjEbMAAAAo9kaM5s3b9aIESMUHx8vl8ultWvX+m47c+aMZs+ere7du6tly5aKj4/X+PHjdejQIfsGAwAAx7E1Zo4fP66ePXsqNzf3vNtOnDihnTt36sEHH9TOnTv1yiuvaO/evfr5z39uw1IAAOBUwXZ+86FDh2ro0KE13hYZGan8/Pxqx5544gldffXVKi4uVmJiYiAmAgAAh7M1ZuqrvLxcLpdLrVq1qvU+Xq9XXq/Xd93j8QRgGQAAsIsxbwA+deqU5syZozFjxigiIqLW++Xk5CgyMtJ3SUhICOBKAAAQaEbEzJkzZ3THHXeoqqpKTz311AXvm52drfLyct+lpKQkQCsBAIAdHP8y05kzZ3T77bdr//79evfddy94VkaS3G633G53gNYBAAC7OTpmzoXMv/71L23cuFFt2rSxexIAAHAYW2OmoqJC+/bt813fv3+/ioqKFBUVpfj4eN12223auXOn/vGPf6iyslJHjhyRJEVFRal58+Z2zQYAAA5ia8zs2LFDgwYN8l3PysqSJE2YMEELFizQunXrJEm9evWq9riNGzcqPT09UDMBAICD2Roz6enpsiyr1tsvdBsAAIBkyG8zAQAA1IaYAQAARiNmAACA0YgZAABgNGIGAAAYjZgBAABGI2YAAIDRiBkAAGA0YgYAABiNmAEAAEYjZgAAgNGIGQAAYDRiBgAAGI2YAQAARiNmAACA0YgZAABgNGIGAAAYjZgBAABGI2YAAIDRiBkAAGA0YgYAABiNmAEAAEYjZgAAgNGIGQAAYDRiBgAAGI2YAQAARiNmAACA0YgZAABgNGIGAAAYjZgBAABGI2YAAIDRiBkAAGA0YgYAABiNmAEAAEYjZgAAgNFsjZnNmzdrxIgRio+Pl8vl0tq1a6vdblmWFixYoPj4eIWFhSk9PV27d++2ZywAAHAkW2Pm+PHj6tmzp3Jzc2u8fenSpVq2bJlyc3NVUFCg2NhY3XDDDTp27FiAlwIAAKcKtvObDx06VEOHDq3xNsuytHz5cs2dO1cjR46UJD333HOKiYnR6tWrNXHixEBOBQAADuXY98zs379fR44cUUZGhu+Y2+3WwIEDtW3bNhuXAQAAJ7H1zMyFHDlyRJIUExNT7XhMTIwOHDhQ6+O8Xq+8Xq/vusfjaZyBAADAERx7ZuYcl8tV7bplWecd+6GcnBxFRkb6LgkJCY09EQAA2MixMRMbGyvp/8/QnFNaWnre2Zofys7OVnl5ue9SUlLSqDsBAIC9HBszycnJio2NVX5+vu/Y6dOntWnTJvXr16/Wx7ndbkVERFS7AACApsvW98xUVFRo3759vuv79+9XUVGRoqKilJiYqMzMTC1ZskSdOnVSp06dtGTJErVo0UJjxoyxcTUAAHASW2Nmx44dGjRokO96VlaWJGnChAlatWqV7r//fp08eVKTJ0/W0aNHdc011+jtt99WeHi4XZMBAIDD2Boz6enpsiyr1ttdLpcWLFigBQsWBG4UAAAwimPfMwMAAFAXxAwAADAaMQMAAIxGzAAAAKMRMwAAwGjEDAAAMBoxAwAAjEbMAAAAoxEzAADAaMQMAAAwGjEDAACMRswAAACjETMAAMBoxAwAADAaMQMAAIxGzAAAAKMF2z3AdMXFxSorK7N7Rr20bdtWiYmJds8AAMAviJlLUFxcrJSULjp58oTdU+olLKyFPv98D0EDAGgSiJlLUFZWppMnT+iau+crIi7J7jl14jn8tT54dqHKysqIGQBAk0DM+EFEXJKiEjvbPQMAgB8l3gAMAACMRswAAACjETMAAMBoxAwAADAaMQMAAIzWoJjp0KGDvv322/OO//e//1WHDh0ueRQAAEBdNShmvv76a1VWVp533Ov16uDBg5c8CgAAoK7q9e/MrFu3zvfn9evXKzIy0ne9srJSGzZsUFJSkt/GAQAAXEy9YuaWW26RJLlcLk2YMKHabSEhIUpKStJjjz3mt3EAAAAXU6+YqaqqkiQlJyeroKBAbdu2bZRRAAAAddWgjzPYv3+/v3cAAAA0SIM/m2nDhg3asGGDSktLfWdsznn22WcveRgAAEBdNChmFi5cqEWLFiktLU1xcXFyuVz+3gUAAFAnDYqZp59+WqtWrdK4ceP8vQcAAKBeGvTvzJw+fVr9+vXz9xYAAIB6a1DM3HvvvVq9erW/twAAANRbg15mOnXqlPLy8vTOO++oR48eCgkJqXb7smXL/DLu7NmzWrBggV544QUdOXJEcXFx+tWvfqV58+apWTM+VgoAADQwZj755BP16tVLkvTpp59Wu82fbwZ+9NFH9fTTT+u5555TamqqduzYobvuukuRkZGaMWOG374PAAAwV4NiZuPGjf7eUaPt27fr5ptv1rBhwyRJSUlJevHFF7Vjx46AfH8AAOB8jn6tZsCAAdqwYYP27t0rSfr444+1detW3XTTTTYvAwAATtGgMzODBg264MtJ7777boMH/dDs2bNVXl6ulJQUBQUFqbKyUosXL9bo0aNrfYzX65XX6/Vd93g8ftkCAACcqUExc+79MuecOXNGRUVF+vTTT8/7AMpLsWbNGj3//PNavXq1UlNTVVRUpMzMTMXHx9f6fXJycrRw4UK/bQAAAM7WoJh5/PHHazy+YMECVVRUXNKgH5o1a5bmzJmjO+64Q5LUvXt3HThwQDk5ObXGTHZ2trKysnzXPR6PEhIS/LYJAAA4i1/fMzN27Fi/fi7TiRMnzvsV7KCgoPM+C+qH3G63IiIiql0AAEDT1eAPmqzJ9u3bFRoa6revN2LECC1evFiJiYlKTU3VRx99pGXLlunuu+/22/cAAABma1DMjBw5stp1y7J0+PBh7dixQw8++KBfhknSE088oQcffFCTJ09WaWmp4uPjNXHiRD300EN++x4AAMBsDYqZyMjIatebNWumzp07a9GiRcrIyPDLMEkKDw/X8uXLtXz5cr99TQAA0LQ0KGZWrlzp7x0AAAANcknvmSksLNSePXvkcrnUtWtX9e7d21+7AAAA6qRBMVNaWqo77rhD7733nlq1aiXLslReXq5BgwbppZde0k9+8hN/7wQAAKhRg341e9q0afJ4PNq9e7e+++47HT16VJ9++qk8Ho+mT5/u740AAAC1atCZmbfeekvvvPOOunTp4jvWtWtXPfnkk359AzAAAMDFNOjMTFVVlUJCQs47HhIScsF/0A4AAMDfGhQzP/3pTzVjxgwdOnTId+zgwYOaOXOmBg8e7LdxAAAAF9OgmMnNzdWxY8eUlJSkK664Qh07dlRycrKOHTumJ554wt8bAQAAatWg98wkJCRo586dys/P1+effy7LstS1a1cNGTLE3/sAAAAuqF5nZt5991117dpVHo9HknTDDTdo2rRpmj59uvr27avU1FRt2bKlUYYCAADUpF4xs3z5cv3617+u8ZOoIyMjNXHiRC1btsxv4wAAAC6mXjHz8ccf62c/+1mtt2dkZKiwsPCSRwEAANRVvWLmm2++qfFXss8JDg7Wf/7zn0seBQAAUFf1ipnLL79cu3btqvX2Tz75RHFxcZc8CgAAoK7qFTM33XSTHnroIZ06deq8206ePKn58+dr+PDhfhsHAABwMfX61ex58+bplVde0ZVXXqmpU6eqc+fOcrlc2rNnj5588klVVlZq7ty5jbUVAADgPPWKmZiYGG3btk333XefsrOzZVmWJMnlcunGG2/UU089pZiYmEYZCgAAUJN6/6N57du31xtvvKGjR49q3759sixLnTp1UuvWrRtjHwAAwAU16F8AlqTWrVurb9++/twCAABQbw36bCYAAACnIGYAAIDRiBkAAGA0YgYAABiNmAEAAEYjZgAAgNGIGQAAYDRiBgAAGI2YAQAARiNmAACA0YgZAABgNGIGAAAYjZgBAABGI2YAAIDRiBkAAGA0YgYAABiNmAEAAEZzfMwcPHhQY8eOVZs2bdSiRQv16tVLhYWFds8CAAAOEWz3gAs5evSo+vfvr0GDBunNN99UdHS0vvzyS7Vq1cruaQAAwCEcHTOPPvqoEhIStHLlSt+xpKQk+wYBAADHcfTLTOvWrVNaWppGjRql6Oho9e7dW88884zdswAAgIM4Oma++uorrVixQp06ddL69es1adIkTZ8+XX/5y19qfYzX65XH46l2AQAATZejX2aqqqpSWlqalixZIknq3bu3du/erRUrVmj8+PE1PiYnJ0cLFy4M5EwAAGAjR5+ZiYuLU9euXasd69Kli4qLi2t9THZ2tsrLy32XkpKSxp4JAABs5OgzM/3799cXX3xR7djevXvVvn37Wh/jdrvldrsbexoAAHAIR5+ZmTlzpt5//30tWbJE+/bt0+rVq5WXl6cpU6bYPQ0AADiEo2Omb9++evXVV/Xiiy+qW7duevjhh7V8+XLdeeeddk8DAAAO4eiXmSRp+PDhGj58uN0zAACAQzn6zAwAAMDFEDMAAMBoxAwAADAaMQMAAIxGzAAAAKMRMwAAwGjEDAAAMBoxAwAAjEbMAAAAoxEzAADAaMQMAAAwGjEDAACMRswAAACjETMAAMBoxAwAADAaMQMAAIxGzAAAAKMRMwAAwGjEDAAAMBoxAwAAjEbMAAAAoxEzAADAaMQMAAAwGjEDAACMRswAAACjETMAAMBoxAwAADAaMQMAAIxGzAAAAKMRMwAAwGjEDAAAMBoxAwAAjEbMAAAAoxEzAADAaMQMAAAwGjEDAACMZlTM5OTkyOVyKTMz0+4pAADAIYyJmYKCAuXl5alHjx52TwEAAA5iRMxUVFTozjvv1DPPPKPWrVvbPQcAADiIETEzZcoUDRs2TEOGDLF7CgAAcJhguwdczEsvvaSdO3eqoKCgTvf3er3yer2+6x6Pp7GmAQAAB3D0mZmSkhLNmDFDzz//vEJDQ+v0mJycHEVGRvouCQkJjbwSAADYydExU1hYqNLSUvXp00fBwcEKDg7Wpk2b9Mc//lHBwcGqrKw87zHZ2dkqLy/3XUpKSmxYDgAAAsXRLzMNHjxYu3btqnbsrrvuUkpKimbPnq2goKDzHuN2u+V2uwM1EQAA2MzRMRMeHq5u3bpVO9ayZUu1adPmvOMAAODHydEvMwEAAFyMo8/M1OS9996zewIAAHAQzswAAACjETMAAMBoxAwAADAaMQMAAIxGzAAAAKMRMwAAwGjEDAAAMBoxAwAAjEbMAAAAoxEzAADAaMQMAAAwGjEDAACMRswAAACjETMAAMBoxAwAADAaMQMAAIxGzAAAAKMRMwAAwGjEDAAAMBoxAwAAjEbMAAAAoxEzAADAaMQMAAAwGjEDAACMRswAAACjETMAAMBoxAwAADAaMQMAAIxGzAAAAKMRMwAAwGjEDAAAMBoxAwAAjEbMAAAAoxEzAADAaMQMAAAwGjEDAACM5uiYycnJUd++fRUeHq7o6Gjdcsst+uKLL+yeBQAAHMTRMbNp0yZNmTJF77//vvLz83X27FllZGTo+PHjdk8DAAAOEWz3gAt56623ql1fuXKloqOjVVhYqOuvv96mVQAAwEkcfWbmf5WXl0uSoqKibF4CAACcwtFnZn7IsixlZWVpwIAB6tatW63383q98nq9vusejycQ8wAAgE2MOTMzdepUffLJJ3rxxRcveL+cnBxFRkb6LgkJCQFaCAAA7GBEzEybNk3r1q3Txo0b1a5duwveNzs7W+Xl5b5LSUlJgFYCAAA7OPplJsuyNG3aNL366qt67733lJycfNHHuN1uud3uAKwDAABO4OiYmTJlilavXq3XXntN4eHhOnLkiCQpMjJSYWFhNq8DAABO4OiXmVasWKHy8nKlp6crLi7Od1mzZo3d0wAAgEM4+syMZVl2TwAAAA7n6DMzAAAAF0PMAAAAoxEzAADAaMQMAAAwGjEDAACMRswAAACjETMAAMBoxAwAADAaMQMAAIxGzAAAAKMRMwAAwGjEDAAAMBoxAwAAjEbMAAAAoxEzAADAaMQMAAAwWrDdA4C6KC4uVllZmd0z6sXr9crtdts9o17atm2rxMREu2c0efx9DgzTNu/Zs8fuCcYiZuB4xcXFSknpopMnT9g9pX5cLsmy7F5RL2FhLfT553sImkbE3+cAMnGzpDPe03ZPMA4xA8crKyvTyZMndM3d8xURl2T3nDo5vGu7Pl2Xp15jZusnySl2z6kTz+Gv9cGzC1VWVkbMNCL+PgeGyZvPnj1r9xTjEDMwRkRckqISO9s9o048h7+WJF0WnWjMZgQWf58bl8mbUX+8ARgAABiNmAEAAEYjZgAAgNGIGQAAYDRiBgAAGI2YAQAARiNmAACA0YgZAABgNGIGAAAYjZgBAABGI2YAAIDRiBkAAGA0YgYAABiNmAEAAEYjZgAAgNGIGQAAYDQjYuapp55ScnKyQkND1adPH23ZssXuSQAAwCEcHzNr1qxRZmam5s6dq48++kjXXXedhg4dquLiYrunAQAAB3B8zCxbtkz33HOP7r33XnXp0kXLly9XQkKCVqxYYfc0AADgAI6OmdOnT6uwsFAZGRnVjmdkZGjbtm02rQIAAE4SbPeACykrK1NlZaViYmKqHY+JidGRI0dqfIzX65XX6/VdLy8vlyR5PB6/76uoqJAkfXfgC531nvT7128MniPfvzxXWFjo2+90X3zxhSTDnufDByRJ5Qf/pZBgl81r6sbEvxuS1KxZM1VVVdk9o874+xwYbA6Mc/+/UVFR4fefs+e+nmVZF7+z5WAHDx60JFnbtm2rdvx3v/ud1blz5xofM3/+fEsSFy5cuHDhwqUJXEpKSi7aC44+M9O2bVsFBQWddxamtLT0vLM152RnZysrK8t3vaqqSt99953atGkjl8u/pevxeJSQkKCSkhJFRET49Wvj//E8BwbPc2DwPAcGz3NgNObzbFmWjh07pvj4+Ive19Ex07x5c/Xp00f5+fm69dZbfcfz8/N188031/gYt9stt9td7VirVq0ac6YiIiL4H0sA8DwHBs9zYPA8BwbPc2A01vMcGRlZp/s5OmYkKSsrS+PGjVNaWpquvfZa5eXlqbi4WJMmTbJ7GgAAcADHx8wvf/lLffvtt1q0aJEOHz6sbt266Y033lD79u3tngYAABzA8TEjSZMnT9bkyZPtnnEet9ut+fPnn/eyFvyL5zkweJ4Dg+c5MHieA8Mpz7PLsuryO08AAADO5Oh/NA8AAOBiiBkAAGA0YgYAABiNmGmgp556SsnJyQoNDVWfPn20ZcsWuyc1KTk5Oerbt6/Cw8MVHR2tW265xffPwKPx5OTkyOVyKTMz0+4pTdLBgwc1duxYtWnTRi1atFCvXr1UWFho96wm5ezZs5o3b56Sk5MVFhamDh06aNGiRUZ95IUTbd68WSNGjFB8fLxcLpfWrl1b7XbLsrRgwQLFx8crLCxM6enp2r17d8D2ETMNsGbNGmVmZmru3Ln66KOPdN1112no0KEqLi62e1qTsWnTJk2ZMkXvv/++8vPzdfbsWWVkZOj48eN2T2uyCgoKlJeXpx49etg9pUk6evSo+vfvr5CQEL355pv67LPP9NhjjzX6P+r5Y/Poo4/q6aefVm5urvbs2aOlS5fq97//vZ544gm7pxnt+PHj6tmzp3Jzc2u8fenSpVq2bJlyc3NVUFCg2NhY3XDDDTp27FhgBl7yByj9CF199dXWpEmTqh1LSUmx5syZY9Oipq+0tNSSZG3atMnuKU3SsWPHrE6dOln5+fnWwIEDrRkzZtg9qcmZPXu2NWDAALtnNHnDhg2z7r777mrHRo4caY0dO9amRU2PJOvVV1/1Xa+qqrJiY2OtRx55xHfs1KlTVmRkpPX0008HZBNnZurp9OnTKiwsVEZGRrXjGRkZ2rZtm02rmr5zn34eFRVl85KmacqUKRo2bJiGDBli95Qma926dUpLS9OoUaMUHR2t3r1765lnnrF7VpMzYMAAbdiwQXv37pUkffzxx9q6datuuukmm5c1Xfv379eRI0eq/Vx0u90aOHBgwH4uGvGP5jlJWVmZKisrz/ugy5iYmPM+EBP+YVmWsrKyNGDAAHXr1s3uOU3OSy+9pJ07d6qgoMDuKU3aV199pRUrVigrK0sPPPCAPvzwQ02fPl1ut1vjx4+3e16TMXv2bJWXlyslJUVBQUGqrKzU4sWLNXr0aLunNVnnfvbV9HPxwIEDAdlAzDTQ/34Ct2VZfv9Ubnxv6tSp+uSTT7R161a7pzQ5JSUlmjFjht5++22FhobaPadJq6qqUlpampYsWSJJ6t27t3bv3q0VK1YQM360Zs0aPf/881q9erVSU1NVVFSkzMxMxcfHa8KECXbPa9Ls/LlIzNRT27ZtFRQUdN5ZmNLS0vOqFJdu2rRpWrdunTZv3qx27drZPafJKSwsVGlpqfr06eM7VllZqc2bNys3N1der1dBQUE2Lmw64uLi1LVr12rHunTpopdfftmmRU3TrFmzNGfOHN1xxx2SpO7du+vAgQPKyckhZhpJbGyspO/P0MTFxfmOB/LnIu+ZqafmzZurT58+ys/Pr3Y8Pz9f/fr1s2lV02NZlqZOnapXXnlF7777rpKTk+2e1CQNHjxYu3btUlFRke+SlpamO++8U0VFRYSMH/Xv3/+8f15g7969fGiun504cULNmlX/0RYUFMSvZjei5ORkxcbGVvu5ePr0aW3atClgPxc5M9MAWVlZGjdunNLS0nTttdcqLy9PxcXFmjRpkt3TmowpU6Zo9erVeu211xQeHu47ExYZGamwsDCb1zUd4eHh570PqWXLlmrTpg3vT/KzmTNnql+/flqyZIluv/12ffjhh8rLy1NeXp7d05qUESNGaPHixUpMTFRqaqo++ugjLVu2THfffbfd04xWUVGhffv2+a7v379fRUVFioqKUmJiojIzM7VkyRJ16tRJnTp10pIlS9SiRQuNGTMmMAMD8jtTTdCTTz5ptW/f3mrevLl11VVX8SvDfiapxsvKlSvtntbk8avZjefvf/+71a1bN8vtdlspKSlWXl6e3ZOaHI/HY82YMcNKTEy0QkNDrQ4dOlhz5861vF6v3dOMtnHjxhr/P3nChAmWZX3/69nz58+3YmNjLbfbbV1//fXWrl27AraPT80GAABG4z0zAADAaMQMAAAwGjEDAACMRswAAACjETMAAMBoxAwAADAaMQMAAIxGzAAAAKMRMwAuWXp6ujIzMyVJSUlJWr58ua17APy4EDMAAMBoxAwA1MKyLJ09e9buGQAugpgB0KhcLpf+9Kc/afjw4WrRooW6dOmi7du3a9++fUpPT1fLli117bXX6ssvv6zT1/v44481aNAghYeHKyIiQn369NGOHTt8t//zn//UwIED1aJFC7Vu3Vo33nijjh49Kknyer2aPn26oqOjFRoaqgEDBqigoMD32Pfee08ul0vr169XWlqa3G63tmzZIsuytHTpUnXo0EFhYWHq2bOn/va3v/n3iQLQYMQMgEb38MMPa/z48SoqKlJKSorGjBmjiRMnKjs72xciU6dOrdPXuvPOO9WuXTsVFBSosLBQc+bMUUhIiCSpqKhIgwcPVmpqqrZv366tW7dqxIgRqqyslCTdf//9evnll/Xcc89p586d6tixo2688UZ999131b7H/fffr5ycHO3Zs0c9evTQvHnztHLlSq1YsUK7d+/WzJkzNXbsWG3atMmPzxKABgvY53MDaLIGDhxozZgxw7Isy2rfvr31+OOP+26TZM2bN893ffv27ZYk689//rPv2IsvvmiFhobW6XuFh4dbq1atqvG20aNHW/3796/xtoqKCiskJMR64YUXfMdOnz5txcfHW0uXLrUsy7I2btxoSbLWrl1b7XGhoaHWtm3bqn29e+65xxo9enSdNgNoXME2txSAH4EePXr4/hwTEyNJ6t69e7Vjp06dksfjUURExAW/VlZWlu6991799a9/1ZAhQzRq1ChdccUVkr4/MzNq1KgaH/fll1/qzJkz6t+/v+9YSEiIrr76au3Zs6fafdPS0nx//uyzz3Tq1CndcMMN1e5z+vRp9e7d+4JbAQQGMQOg0Z17GUj6/j00tR2rqqq66NdasGCBxowZo9dff11vvvmm5s+fr5deekm33nqrwsLCan2cZVnVvtcPj//vsZYtW/r+fG7T66+/rssvv7za/dxu90X3Amh8vGcGgHGuvPJKzZw5U2+//bZGjhyplStXSvr+DNCGDRtqfEzHjh3VvHlzbd261XfszJkz2rFjh7p06VLr9+ratavcbreKi4vVsWPHapeEhAT//ocBaBDOzAAwxsmTJzVr1izddtttSk5O1r///W8VFBToF7/4hSQpOztb3bt31+TJkzVp0iQ1b95cGzdu1KhRo9S2bVvdd999mjVrlqKiopSYmKilS5fqxIkTuueee2r9nuHh4frtb3+rmTNnqqqqSgMGDJDH49G2bdt02WWXacKECYH6zwdQC2IGgDGCgoL07bffavz48frmm2/Utm1bjRw5UgsXLpT0/Rmbt99+Ww888ICuvvpqhYWF6ZprrtHo0aMlSY888oiqqqo0btw4HTt2TGlpaVq/fr1at259we/78MMPKzo6Wjk5Ofrqq6/UqlUrXXXVVXrggQca/b8ZwMW5rHMvJAMAABiI98wAAACjETMAHCU1NVWXXXZZjZcXXnjB7nkAHIiXmQA4yoEDB3TmzJkab4uJiVF4eHiAFwFwOmIGAAAYjZeZAACA0YgZAABgNGIGAAAYjZgBAABGI2YAAIDRiBkAAGA0YgYAABiNmAEAAEb7P5W5SENgRS7tAAAAAElFTkSuQmCC", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"llm_score\", bins=10)"]}, {"cell_type": "code", "execution_count": 9, "id": "0110dd14", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='llm_score', ylabel='Count'>"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df[df[\"json_valid\"]==True], x=\"llm_score\", bins=10)"]}, {"cell_type": "code", "execution_count": null, "id": "3dcc4418", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}