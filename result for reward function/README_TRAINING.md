# LLM Score Prediction Training Dataset

This repository contains a merged dataset and training scripts for building a model to predict LLM scores based on prompts and generated JSON responses.

## Dataset Overview

The merged dataset contains **201 training samples** extracted from multiple JSON files:
- `qwen3-14B-base-*.json` (4 files)
- `qwen3-14B-sft-*.json` (4 files)

### Dataset Structure

Each training sample contains:
- **prompt**: The input prompt text
- **generated_json**: The JSON response generated by the LLM
- **llm_score**: The target score (1.0 - 10.0)
- **source_file**: Original file name
- **sample_id**: Unique identifier

### Dataset Statistics

- **Total samples**: 201
- **LLM Score range**: 1.00 - 10.00
- **Average LLM Score**: 7.01
- **Prompt length**: 115 - 703 characters (avg: 264.2)
- **Generated JSON length**: 245 - 10,252 characters (avg: 1,984.6)

## Files

### Core Scripts

1. **`merge_datasets.py`** - Merges all JSON files into a single training dataset
2. **`train_llm_score_predictor.py`** - Trains machine learning models to predict LLM scores
3. **`example_prediction.py`** - Demonstrates how to use the trained model

### Output Files

- **`merged_training_dataset.json`** - Complete merged dataset in JSON format
- **`merged_training_dataset.csv`** - Same dataset in CSV format for analysis
- **`llm_score_predictor.pkl`** - Trained model (created after training)

## Usage

### Step 1: Merge Datasets

```bash
python3 merge_datasets.py
```

This will:
- Find all `qwen3-*.json` files
- Extract prompt, generated_json, and llm_score from each
- Combine into a single dataset
- Save as both JSON and CSV formats
- Display dataset statistics

### Step 2: Train the Model

```bash
python3 train_llm_score_predictor.py
```

This will:
- Load the merged dataset
- Extract features from prompts and JSON responses
- Train multiple models (Random Forest, Linear Regression)
- Compare performance metrics
- Save the best model as `llm_score_predictor.pkl`

### Step 3: Use the Trained Model

```bash
python3 example_prediction.py
```

This demonstrates:
- Loading the trained model
- Predicting scores for example workflows
- Interactive mode for testing your own prompts

## Feature Engineering

The model extracts various features from the input data:

### Prompt Features
- Length and word count
- Presence of keywords (webhook, API, database, etc.)
- Complexity indicators
- TF-IDF text features

### JSON Features
- JSON validity
- Structural features (nodes, connections)
- Node types and parameters
- Connection complexity
- TF-IDF text features from JSON content

## Model Performance

The training script compares multiple models and reports:
- **MSE** (Mean Squared Error)
- **MAE** (Mean Absolute Error)
- **R²** (R-squared score)
- **RMSE** (Root Mean Squared Error)

## Programmatic Usage

You can use the trained model in your own code:

```python
from train_llm_score_predictor import predict_llm_score

# Predict score for new data
prompt = "Create an n8n workflow that..."
generated_json = '{"name": "My Workflow", ...}'

score = predict_llm_score(prompt, generated_json)
print(f"Predicted LLM Score: {score:.2f}/10.0")
```

## Requirements

```bash
pip install scikit-learn numpy
```

## Dataset Quality

The merger script automatically:
- Skips incomplete samples (missing required fields)
- Reports statistics about data quality
- Handles encoding issues
- Validates JSON structure

## Extending the Dataset

To add more training data:
1. Place new JSON files in the same directory
2. Ensure they follow the same structure (prompt, generated_json, llm_score)
3. Re-run `merge_datasets.py`
4. Re-train the model with `train_llm_score_predictor.py`

## Model Interpretation

Score ranges and their meanings:
- **8.0-10.0**: Excellent - High quality, well-structured workflows
- **6.0-7.9**: Good - Solid workflows with minor issues
- **4.0-5.9**: Fair - Functional but needs improvement
- **1.0-3.9**: Poor - Significant issues present

## Next Steps

1. **Collect more data**: Add more diverse training samples
2. **Feature engineering**: Experiment with additional features
3. **Model tuning**: Optimize hyperparameters
4. **Validation**: Test on held-out datasets
5. **Deployment**: Integrate into production systems

## Troubleshooting

### Common Issues

1. **"No module named 'sklearn'"**: Install scikit-learn
2. **"No training data available"**: Run `merge_datasets.py` first
3. **"Model file not found"**: Train the model first
4. **Low model performance**: Consider collecting more diverse training data

### Data Quality Issues

The merger script reports skipped samples. Common reasons:
- Missing `llm_score` field
- Empty `prompt` or `generated_json`
- Malformed JSON structure

## Contributing

To improve the model:
1. Add more training data
2. Experiment with new features
3. Try different algorithms
4. Improve data preprocessing
5. Add validation metrics

---

This training pipeline provides a foundation for building LLM score prediction models. The modular design allows for easy experimentation and improvement.
