# JSON Files Comparison Tool

This tool loads two JSON files, identifies common numerical attributes, and creates bar graphs/histograms to compare the distributions between the two files.

## Features

- **Automatic attribute detection**: Finds all common numerical attributes between two JSON files
- **Smart visualization**: Uses histograms for continuous data and bar charts for discrete data
- **Two comparison styles**: 
  - Side-by-side separate plots for detailed comparison
  - Overlaid plots for direct visual comparison
- **Statistical information**: Shows mean, standard deviation, and count for each attribute
- **High-quality output**: Saves plots as PNG files with 300 DPI resolution

## Usage

### Basic Usage (with default files)
```bash
python compare_json_files.py
```
This will compare `qwen3-14B-base-mean.json` and `qwen3-14B-sft-mean.json` by default.

### Custom Files
```bash
python compare_json_files.py file1.json file2.json
```

### Example with your files
```bash
python compare_json_files.py qwen3-14B-base-0.json qwen3-14B-sft-0.json
```

## Output

The script creates a `plots/` directory containing:

1. **Comparison plots** (`*_comparison.png`): Side-by-side plots showing distributions for each file separately
2. **Side-by-side plots** (`*_side_by_side.png`): Overlaid plots for direct comparison

## Supported Data Types

- Integer and float values
- Handles missing/null values gracefully
- Works with nested JSON structures (extracts top-level numerical attributes)

## Requirements

```bash
pip install matplotlib numpy pandas
```

## Example Output

For each common numerical attribute, you'll get:
- Distribution plots with appropriate binning
- Statistical summaries (mean, std, count)
- Color-coded comparisons (blue for first file, coral for second file)
- Grid lines and proper labeling

## File Structure

```
your_directory/
├── compare_json_files.py    # Main script
├── file1.json              # Your first JSON file
├── file2.json              # Your second JSON file
└── plots/                  # Generated plots directory
    ├── attribute1_comparison.png
    ├── attribute1_side_by_side.png
    ├── attribute2_comparison.png
    └── ...
```

## Common Attributes Found

The script automatically detected these common numerical attributes in your sample files:
- json_valid
- llm_score
- num_connections
- num_connections_with_valid_structure
- num_connections_with_valid_target_node
- num_nodes
- num_nodes_with_parameters
- num_nodes_with_valid_structure
- num_nodes_with_valid_type
- num_nodes_with_valid_version
- percent_connections_with_valid_structure
- percent_connections_with_valid_target_node
- percent_node_with_parameters
- percent_node_with_valid_structure
- percent_node_with_valid_type
- percent_node_with_valid_version
- top_level_keys_present
- tries
