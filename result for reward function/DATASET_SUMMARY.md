# LLM Score Training Dataset - Summary

## Overview

Successfully merged and analyzed **201 training samples** from 8 JSON files to create a comprehensive dataset for training LLM score prediction models.

## Dataset Composition

### Source Files Processed
- `qwen3-14B-base-0.json`: 29 samples (avg score: 7.38)
- `qwen3-14B-base-1.json`: 19 samples (avg score: 5.16)
- `qwen3-14B-base-2.json`: 27 samples (avg score: 6.02)
- `qwen3-14B-base-3.json`: 20 samples (avg score: 5.90)
- `qwen3-14B-sft-0.json`: 27 samples (avg score: 7.74)
- `qwen3-14B-sft-1.json`: 28 samples (avg score: 8.07)
- `qwen3-14B-sft-2.json`: 24 samples (avg score: 7.42)
- `qwen3-14B-sft-3.json`: 27 samples (avg score: 7.52)

### Data Quality
- **Total samples**: 201
- **Average LLM score**: 7.01/10.0
- **Score range**: 1.0 - 10.0
- **JSON validity**: 100% (all samples have valid JSON)
- **Skipped samples**: 37 (due to missing fields)

## Score Distribution Analysis

| Score Range | Category | Count | Percentage |
|-------------|----------|-------|------------|
| 8.0-10.0 | Excellent | 98 | 48.8% |
| 6.0-7.9 | Good | 37 | 18.4% |
| 4.0-5.9 | Fair | 30 | 14.9% |
| 1.0-3.9 | Poor | 36 | 17.9% |

## Key Findings

### 1. Workflow Complexity Impact
- **High complexity** (≥5 nodes): Average score 5.95
- **Low complexity** (<5 nodes): Average score 7.67
- **Insight**: Simpler workflows tend to score higher

### 2. Feature Analysis by Score Group

#### Excellent Workflows (8-10 points)
- Average JSON length: 1,451 characters
- Average nodes: 3.8
- Average connections: 2.5
- Average prompt length: 239 characters

#### Good Workflows (6-7.9 points)
- Average JSON length: 2,824 characters
- Average nodes: 6.6
- Average connections: 5.2
- Average prompt length: 308 characters

#### Fair Workflows (4-5.9 points)
- Average JSON length: 2,762 characters
- Average nodes: 7.4
- Average connections: 5.3
- Average prompt length: 320 characters

#### Poor Workflows (1-3.9 points)
- Average JSON length: 1,925 characters
- Average nodes: 4.9
- Average connections: 3.8
- Average prompt length: 242 characters

### 3. Common Node Types

#### High-Scoring Workflows
1. `n8n-nodes-base.manualTrigger` (27 occurrences)
2. `n8n-nodes-base.webhook` (22 occurrences)
3. `function` (14 occurrences)
4. `n8n-nodes-base.switch` (11 occurrences)
5. `n8n-nodes-base.set` (9 occurrences)

#### Low-Scoring Workflows
1. `function` (22 occurrences)
2. `httpRequest` (9 occurrences)
3. `set` (8 occurrences)
4. `n8n-nodes-base.if` (8 occurrences)

### 4. Model Performance by Source
- **Best performing**: `qwen3-14B-sft-1.json` (8.07 avg)
- **Worst performing**: `qwen3-14B-base-1.json` (5.16 avg)
- **SFT models** generally outperform base models

## Files Generated

### Core Dataset Files
- **`merged_training_dataset.json`**: Complete dataset (1,409 lines)
- **`merged_training_dataset.csv`**: CSV format for analysis (6,949 lines)

### Analysis and Training Scripts
- **`merge_datasets.py`**: Dataset merger script
- **`simple_analysis.py`**: Analysis script (no external dependencies)
- **`train_llm_score_predictor.py`**: ML training script (requires scikit-learn)
- **`example_prediction.py`**: Usage demonstration script

### Documentation
- **`README_TRAINING.md`**: Comprehensive usage guide
- **`DATASET_SUMMARY.md`**: This summary document

## Key Insights for Model Training

### Important Features to Consider
1. **JSON validity** (100% in this dataset)
2. **Workflow complexity** (node count, connection count)
3. **Prompt characteristics** (length, keywords)
4. **Node type patterns**
5. **Source model type** (SFT vs base)

### Training Recommendations
1. **Balance complexity**: Simpler workflows score higher
2. **Focus on structure**: Well-structured JSON with proper connections
3. **Consider prompt quality**: Clear, specific prompts perform better
4. **Use ensemble methods**: Combine multiple features for better prediction
5. **Cross-validate**: Test across different source files

## Usage Instructions

### Quick Start
```bash
# 1. Merge datasets (already done)
python3 merge_datasets.py

# 2. Run analysis
python3 simple_analysis.py

# 3. Train model (requires scikit-learn)
python3 train_llm_score_predictor.py

# 4. Test predictions
python3 example_prediction.py
```

### Programmatic Usage
```python
# Load the dataset
import json
with open('merged_training_dataset.json', 'r') as f:
    data = json.load(f)

# Each sample contains:
# - prompt: Input text
# - generated_json: LLM response
# - llm_score: Target score (1.0-10.0)
# - source_file: Original file
# - sample_id: Unique identifier
```

## Next Steps

1. **Expand dataset**: Collect more diverse training samples
2. **Feature engineering**: Experiment with additional features
3. **Model optimization**: Tune hyperparameters and try different algorithms
4. **Validation**: Test on held-out data from different sources
5. **Production deployment**: Integrate into evaluation pipelines

## Dataset Statistics Summary

- **Total training samples**: 201
- **Average score**: 7.01/10.0
- **Score standard deviation**: ~2.1
- **Prompt length range**: 115-703 characters
- **JSON length range**: 245-10,252 characters
- **Node count range**: 0-11 nodes
- **Connection count range**: 0-10 connections

This dataset provides a solid foundation for training LLM score prediction models with good coverage across different score ranges and workflow complexities.
