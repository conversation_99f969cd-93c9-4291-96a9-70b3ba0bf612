import json
import os
from collections import defaultdict

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd


def load_json_files(file1_path, file2_path):
    """Load two JSON files and return their data"""
    with open(file1_path, "r") as f:
        data1 = json.load(f)

    with open(file2_path, "r") as f:
        data2 = json.load(f)

    return data1, data2


def get_numerical_attributes(data):
    """Extract all numerical attributes from the data"""
    numerical_attrs = set()

    for item in data:
        if isinstance(item, dict):
            for key, value in item.items():
                if isinstance(value, (int, float)) and value is not None:
                    numerical_attrs.add(key)

    return numerical_attrs


def find_common_numerical_attributes(data1, data2):
    """Find common numerical attributes between two datasets"""
    attrs1 = get_numerical_attributes(data1)
    attrs2 = get_numerical_attributes(data2)

    return attrs1.intersection(attrs2)


def extract_values_for_attribute(data, attribute):
    """Extract all values for a specific attribute from the data"""
    values = []
    for item in data:
        if isinstance(item, dict) and attribute in item:
            value = item[attribute]
            if isinstance(value, (int, float)) and value is not None:
                values.append(value)

    return values


def create_comparison_plots(
    data1, data2, common_attrs, file1_name, file2_name, output_dir="plots"
):
    """Create bar graphs/histograms for common numerical attributes"""

    # Create output directory if it doesn't exist
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # Set up the plotting style
    plt.style.use("default")

    for attr in sorted(common_attrs):
        values1 = extract_values_for_attribute(data1, attr)
        values2 = extract_values_for_attribute(data2, attr)

        if not values1 or not values2:
            continue

        # Create figure with subplots
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        fig.suptitle(f"Comparison of {attr}", fontsize=16, fontweight="bold")

        # Determine if we should use histogram or bar chart
        unique_vals1 = len(set(values1))
        unique_vals2 = len(set(values2))

        if unique_vals1 > 10 or unique_vals2 > 10:
            # Use histogram for continuous data
            bins = min(20, max(unique_vals1, unique_vals2))

            ax1.hist(values1, bins=bins, alpha=0.7, color="skyblue", edgecolor="black")
            ax1.set_title(f"{file1_name}")
            ax1.set_xlabel(attr)
            ax1.set_ylabel("Count")
            ax1.grid(True, alpha=0.3)

            ax2.hist(
                values2, bins=bins, alpha=0.7, color="lightcoral", edgecolor="black"
            )
            ax2.set_title(f"{file2_name}")
            ax2.set_xlabel(attr)
            ax2.set_ylabel("Count")
            ax2.grid(True, alpha=0.3)

        else:
            # Use bar chart for discrete data
            from collections import Counter

            counter1 = Counter(values1)
            counter2 = Counter(values2)

            # Get all unique values
            all_values = sorted(set(values1 + values2))

            counts1 = [counter1.get(val, 0) for val in all_values]
            counts2 = [counter2.get(val, 0) for val in all_values]

            x_pos = np.arange(len(all_values))

            ax1.bar(x_pos, counts1, alpha=0.7, color="skyblue", edgecolor="black")
            ax1.set_title(f"{file1_name}")
            ax1.set_xlabel(attr)
            ax1.set_ylabel("Count")
            ax1.set_xticks(x_pos)
            ax1.set_xticklabels([str(val) for val in all_values], rotation=45)
            ax1.grid(True, alpha=0.3)

            ax2.bar(x_pos, counts2, alpha=0.7, color="lightcoral", edgecolor="black")
            ax2.set_title(f"{file2_name}")
            ax2.set_xlabel(attr)
            ax2.set_ylabel("Count")
            ax2.set_xticks(x_pos)
            ax2.set_xticklabels([str(val) for val in all_values], rotation=45)
            ax2.grid(True, alpha=0.3)

        # Add statistics
        stats1 = f"Mean: {np.mean(values1):.2f}\nStd: {np.std(values1):.2f}\nCount: {len(values1)}"
        stats2 = f"Mean: {np.mean(values2):.2f}\nStd: {np.std(values2):.2f}\nCount: {len(values2)}"

        ax1.text(
            0.02,
            0.98,
            stats1,
            transform=ax1.transAxes,
            verticalalignment="top",
            bbox=dict(boxstyle="round", facecolor="white", alpha=0.8),
        )
        ax2.text(
            0.02,
            0.98,
            stats2,
            transform=ax2.transAxes,
            verticalalignment="top",
            bbox=dict(boxstyle="round", facecolor="white", alpha=0.8),
        )

        plt.tight_layout()

        # Save the plot
        filename = f"{attr.replace('/', '_').replace(' ', '_')}_comparison.png"
        filepath = os.path.join(output_dir, filename)
        plt.savefig(filepath, dpi=300, bbox_inches="tight")
        plt.show()

        print(f"Saved plot for '{attr}' to {filepath}")


def create_side_by_side_comparison(
    data1, data2, common_attrs, file1_name, file2_name, output_dir="plots"
):
    """Create side-by-side bar charts for better comparison"""

    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    for attr in sorted(common_attrs):
        values1 = extract_values_for_attribute(data1, attr)
        values2 = extract_values_for_attribute(data2, attr)

        if not values1 or not values2:
            continue

        # Create side-by-side comparison
        fig, ax = plt.subplots(figsize=(12, 6))

        # Determine if we should use histogram or bar chart
        unique_vals1 = len(set(values1))
        unique_vals2 = len(set(values2))

        if unique_vals1 > 10 or unique_vals2 > 10:
            # Use histogram with side-by-side bars
            bins = np.linspace(
                min(min(values1), min(values2)), max(max(values1), max(values2)), 20
            )

            ax.hist(
                [values1, values2],
                bins=bins,
                alpha=0.7,
                color=["skyblue", "lightcoral"],
                label=[file1_name, file2_name],
                edgecolor="black",
            )
        else:
            # Use bar chart with side-by-side bars
            from collections import Counter

            counter1 = Counter(values1)
            counter2 = Counter(values2)

            all_values = sorted(set(values1 + values2))
            counts1 = [counter1.get(val, 0) for val in all_values]
            counts2 = [counter2.get(val, 0) for val in all_values]

            x_pos = np.arange(len(all_values))
            width = 0.35

            ax.bar(
                x_pos - width / 2,
                counts1,
                width,
                alpha=0.7,
                color="skyblue",
                label=file1_name,
                edgecolor="black",
            )
            ax.bar(
                x_pos + width / 2,
                counts2,
                width,
                alpha=0.7,
                color="lightcoral",
                label=file2_name,
                edgecolor="black",
            )

            ax.set_xticks(x_pos)
            ax.set_xticklabels([str(val) for val in all_values], rotation=45)

        ax.set_title(f"Comparison of {attr}", fontsize=14, fontweight="bold")
        ax.set_xlabel(attr)
        ax.set_ylabel("Count")
        ax.legend()
        ax.grid(True, alpha=0.3)

        plt.tight_layout()

        # Save the plot
        filename = f"{attr.replace('/', '_').replace(' ', '_')}_side_by_side.png"
        filepath = os.path.join(output_dir, filename)
        plt.savefig(filepath, dpi=300, bbox_inches="tight")
        plt.show()

        print(f"Saved side-by-side plot for '{attr}' to {filepath}")


def main():
    import sys

    # Check if file paths are provided as command line arguments
    if len(sys.argv) == 3:
        file1_path = sys.argv[1]
        file2_path = sys.argv[2]
    else:
        # Default file paths if no arguments provided
        file1_path = "qwen3-14B-base-mean.json"
        file2_path = "qwen3-14B-sft-mean.json"
        print("Usage: python compare_json_files.py <file1.json> <file2.json>")
        print(f"Using default files: {file1_path} and {file2_path}")

    # Extract file names for labels
    file1_name = os.path.splitext(os.path.basename(file1_path))[0]
    file2_name = os.path.splitext(os.path.basename(file2_path))[0]

    print(f"Loading data from {file1_path} and {file2_path}...")

    try:
        data1, data2 = load_json_files(file1_path, file2_path)
        print(f"Loaded {len(data1)} records from {file1_name}")
        print(f"Loaded {len(data2)} records from {file2_name}")

        # Find common numerical attributes
        common_attrs = find_common_numerical_attributes(data1, data2)
        print(f"\nFound {len(common_attrs)} common numerical attributes:")
        for attr in sorted(common_attrs):
            print(f"  - {attr}")

        if not common_attrs:
            print("No common numerical attributes found!")
            return

        # Create comparison plots
        print("\nCreating comparison plots...")
        create_comparison_plots(data1, data2, common_attrs, file1_name, file2_name)

        print("\nCreating side-by-side comparison plots...")
        create_side_by_side_comparison(
            data1, data2, common_attrs, file1_name, file2_name
        )

        print("\nAll plots have been generated and saved in the 'plots' directory!")

    except FileNotFoundError as e:
        print(f"Error: Could not find file - {e}")
    except json.JSONDecodeError as e:
        print(f"Error: Invalid JSON format - {e}")
    except Exception as e:
        print(f"Error: {e}")


if __name__ == "__main__":
    main()
if __name__ == "__main__":
    main()
