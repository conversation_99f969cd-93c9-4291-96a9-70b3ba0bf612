#!/usr/bin/env python3

import json


# Copy the necessary functions and data from reward.py to avoid import issues
def load_node_types_and_versions():
    """Parse the n8n_nodes_list.txt file to extract node types and their valid versions."""
    node_types = set()
    node_versions = {}

    try:
        with open("n8n_nodes_list.txt", "r") as f:
            for line in f:
                line = line.strip()
                if line.startswith("- ") and " : " in line:
                    # Parse format: "- node_type : version1,version2,version3"
                    parts = line[2:].split(
                        " : ", 1
                    )  # Remove "- " prefix and split on " : "
                    if len(parts) == 2:
                        node_type = parts[0].strip()
                        versions_str = parts[1].strip()

                        # Parse versions (comma-separated)
                        versions = [v.strip() for v in versions_str.split(",")]

                        node_types.add(node_type)
                        node_versions[node_type] = versions

    except FileNotFoundError:
        print("Warning: n8n_nodes_list.txt not found, falling back to empty lists")

    return node_types, node_versions


VALID_N8N_NODE_TYPES, VALID_N8N_NODE_VERSIONS = load_node_types_and_versions()


def valid_node_type_and_version(n8n_json: str):
    """
    Validates that all nodes in the n8n workflow JSON have valid node types and versions.

    Args:
        n8n_json: JSON string representing an n8n workflow

    Returns:
        float: Score between 0.0 and 1.0 representing the percentage of nodes with valid types and versions
    """
    try:
        # Parse the JSON
        workflow = json.loads(n8n_json) if isinstance(n8n_json, str) else n8n_json

        if not isinstance(workflow, dict):
            return 0.0

        # Get the nodes array
        nodes = workflow.get("nodes", [])
        if not isinstance(nodes, list) or len(nodes) == 0:
            return 0.0

        valid_nodes = 0
        total_nodes = len(nodes)

        for node in nodes:
            if not isinstance(node, dict):
                continue

            node_type = node.get("type")
            node_version = node.get("typeVersion")

            # Check if node type is valid
            if not isinstance(node_type, str) or node_type not in VALID_N8N_NODE_TYPES:
                continue

            # Check if version is valid for this node type
            valid_versions = VALID_N8N_NODE_VERSIONS.get(node_type, [])
            version_str = str(node_version) if node_version is not None else ""

            if version_str in valid_versions:
                valid_nodes += 1

        # Return the percentage of valid nodes
        return valid_nodes / total_nodes if total_nodes > 0 else 0.0

    except (json.JSONDecodeError, TypeError, AttributeError):
        return 0.0


def test_valid_node_type_and_version():
    """Test the valid_node_type_and_version function with various scenarios."""

    # Test case 1: Valid workflow with all valid nodes
    valid_workflow = {
        "name": "Test Workflow",
        "nodes": [
            {
                "name": "Manual Trigger",
                "type": "n8n-nodes-base.manualTrigger",
                "typeVersion": 1,
                "position": [250, 300],
            },
            {
                "name": "HTTP Request",
                "type": "n8n-nodes-base.httpRequest",
                "typeVersion": 4.2,
                "position": [450, 300],
            },
        ],
        "connections": {},
        "active": False,
    }

    score1 = valid_node_type_and_version(json.dumps(valid_workflow))
    print(f"Test 1 - Valid workflow: {score1} (expected: 1.0)")

    # Test case 2: Workflow with invalid node type
    invalid_type_workflow = {
        "name": "Test Workflow",
        "nodes": [
            {
                "name": "Invalid Node",
                "type": "invalid-node-type",
                "typeVersion": 1,
                "position": [250, 300],
            },
            {
                "name": "Valid Node",
                "type": "n8n-nodes-base.manualTrigger",
                "typeVersion": 1,
                "position": [450, 300],
            },
        ],
        "connections": {},
        "active": False,
    }

    score2 = valid_node_type_and_version(json.dumps(invalid_type_workflow))
    print(f"Test 2 - Invalid node type: {score2} (expected: 0.5)")

    # Test case 3: Workflow with invalid version
    invalid_version_workflow = {
        "name": "Test Workflow",
        "nodes": [
            {
                "name": "Invalid Version",
                "type": "n8n-nodes-base.manualTrigger",
                "typeVersion": 999,  # Invalid version
                "position": [250, 300],
            }
        ],
        "connections": {},
        "active": False,
    }

    score3 = valid_node_type_and_version(json.dumps(invalid_version_workflow))
    print(f"Test 3 - Invalid version: {score3} (expected: 0.0)")

    # Test case 4: Empty workflow
    empty_workflow = {
        "name": "Empty Workflow",
        "nodes": [],
        "connections": {},
        "active": False,
    }

    score4 = valid_node_type_and_version(json.dumps(empty_workflow))
    print(f"Test 4 - Empty workflow: {score4} (expected: 0.0)")

    # Test case 5: Invalid JSON
    score5 = valid_node_type_and_version("invalid json")
    print(f"Test 5 - Invalid JSON: {score5} (expected: 0.0)")

    # Test case 6: Mixed valid/invalid nodes
    mixed_workflow = {
        "name": "Mixed Workflow",
        "nodes": [
            {
                "name": "Valid Node 1",
                "type": "n8n-nodes-base.manualTrigger",
                "typeVersion": 1,
                "position": [250, 300],
            },
            {
                "name": "Invalid Type",
                "type": "invalid-type",
                "typeVersion": 1,
                "position": [350, 300],
            },
            {
                "name": "Invalid Version",
                "type": "n8n-nodes-base.httpRequest",
                "typeVersion": 999,
                "position": [450, 300],
            },
            {
                "name": "Valid Node 2",
                "type": "n8n-nodes-base.slack",
                "typeVersion": 2.3,
                "position": [550, 300],
            },
        ],
        "connections": {},
        "active": False,
    }

    score6 = valid_node_type_and_version(json.dumps(mixed_workflow))
    print(f"Test 6 - Mixed workflow: {score6} (expected: 0.5)")


if __name__ == "__main__":
    test_valid_node_type_and_version()
