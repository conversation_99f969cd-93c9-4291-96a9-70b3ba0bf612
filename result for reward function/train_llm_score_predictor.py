#!/usr/bin/env python3
"""
Training script for LLM Score Prediction Model
Uses the merged dataset to train a model that can predict LLM scores based on prompt and generated JSON.
"""

import json
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.ensemble import RandomForestRegressor
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.preprocessing import StandardScaler
import pickle
import re

def load_training_data(filepath: str = "merged_training_dataset.json"):
    """Load the merged training dataset."""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            data = json.load(f)
        print(f"Loaded {len(data)} training samples")
        return data
    except Exception as e:
        print(f"Error loading training data: {e}")
        return []

def extract_json_features(json_string: str):
    """Extract features from the generated JSON string."""
    try:
        # Parse JSON to get structural features
        json_obj = json.loads(json_string)
        
        features = {
            'json_length': len(json_string),
            'json_valid': 1,  # If we reach here, JSON is valid
            'num_nodes': len(json_obj.get('nodes', [])),
            'num_connections': len(json_obj.get('connections', {})),
            'has_name': 1 if json_obj.get('name') else 0,
            'has_active_field': 1 if 'active' in json_obj else 0,
        }
        
        # Count nodes with parameters
        nodes_with_params = sum(1 for node in json_obj.get('nodes', []) if node.get('parameters'))
        features['nodes_with_parameters'] = nodes_with_params
        
        # Count different node types
        node_types = set()
        for node in json_obj.get('nodes', []):
            if node.get('type'):
                node_types.add(node['type'])
        features['unique_node_types'] = len(node_types)
        
        # Connection complexity
        total_connections = 0
        for conn_list in json_obj.get('connections', {}).values():
            if isinstance(conn_list, dict):
                for output_type, connections in conn_list.items():
                    if isinstance(connections, list):
                        total_connections += len(connections)
        features['total_connections'] = total_connections
        
        return features
        
    except json.JSONDecodeError:
        # JSON is invalid
        return {
            'json_length': len(json_string),
            'json_valid': 0,
            'num_nodes': 0,
            'num_connections': 0,
            'has_name': 0,
            'has_active_field': 0,
            'nodes_with_parameters': 0,
            'unique_node_types': 0,
            'total_connections': 0,
        }

def extract_prompt_features(prompt: str):
    """Extract features from the prompt text."""
    features = {
        'prompt_length': len(prompt),
        'prompt_word_count': len(prompt.split()),
        'has_webhook': 1 if 'webhook' in prompt.lower() else 0,
        'has_api': 1 if 'api' in prompt.lower() else 0,
        'has_database': 1 if any(db in prompt.lower() for db in ['database', 'airtable', 'notion', 'google sheet']) else 0,
        'has_trigger': 1 if 'trigger' in prompt.lower() else 0,
        'has_schedule': 1 if 'schedule' in prompt.lower() else 0,
        'complexity_score': len(re.findall(r'\b(and|then|also|additionally|finally)\b', prompt.lower())),
    }
    return features

def prepare_features(data):
    """Prepare feature vectors and target values."""
    X_prompt = []
    X_json_features = []
    y = []
    
    # Extract text features using TF-IDF
    prompts = [item['prompt'] for item in data]
    json_texts = [item['generated_json'] for item in data]
    
    # TF-IDF for prompts
    prompt_vectorizer = TfidfVectorizer(max_features=1000, stop_words='english', ngram_range=(1, 2))
    X_prompt_tfidf = prompt_vectorizer.fit_transform(prompts)
    
    # TF-IDF for JSON (treating as text)
    json_vectorizer = TfidfVectorizer(max_features=500, stop_words='english', ngram_range=(1, 1))
    X_json_tfidf = json_vectorizer.fit_transform(json_texts)
    
    # Extract structured features
    structured_features = []
    for item in data:
        prompt_features = extract_prompt_features(item['prompt'])
        json_features = extract_json_features(item['generated_json'])
        
        # Combine features
        combined_features = {**prompt_features, **json_features}
        structured_features.append(list(combined_features.values()))
        
        y.append(item['llm_score'])
    
    # Combine all features
    X_structured = np.array(structured_features)
    X_combined = np.hstack([X_prompt_tfidf.toarray(), X_json_tfidf.toarray(), X_structured])
    
    return X_combined, np.array(y), prompt_vectorizer, json_vectorizer

def train_models(X, y):
    """Train multiple models and compare performance."""
    # Split data
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
    
    # Scale features
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    models = {
        'Random Forest': RandomForestRegressor(n_estimators=100, random_state=42),
        'Linear Regression': LinearRegression(),
    }
    
    results = {}
    trained_models = {}
    
    for name, model in models.items():
        print(f"\nTraining {name}...")
        
        # Train model
        model.fit(X_train_scaled, y_train)
        
        # Make predictions
        y_pred = model.predict(X_test_scaled)
        
        # Calculate metrics
        mse = mean_squared_error(y_test, y_pred)
        mae = mean_absolute_error(y_test, y_pred)
        r2 = r2_score(y_test, y_pred)
        
        results[name] = {
            'MSE': mse,
            'MAE': mae,
            'R2': r2,
            'RMSE': np.sqrt(mse)
        }
        
        trained_models[name] = model
        
        print(f"  MSE: {mse:.4f}")
        print(f"  MAE: {mae:.4f}")
        print(f"  R²: {r2:.4f}")
        print(f"  RMSE: {np.sqrt(mse):.4f}")
    
    return trained_models, results, scaler, X_test, y_test

def save_model(model, scaler, prompt_vectorizer, json_vectorizer, model_name="best_model"):
    """Save the trained model and preprocessing components."""
    model_data = {
        'model': model,
        'scaler': scaler,
        'prompt_vectorizer': prompt_vectorizer,
        'json_vectorizer': json_vectorizer
    }
    
    with open(f"{model_name}.pkl", 'wb') as f:
        pickle.dump(model_data, f)
    
    print(f"Model saved as {model_name}.pkl")

def predict_llm_score(prompt, generated_json, model_path="best_model.pkl"):
    """Predict LLM score for a new prompt and generated JSON."""
    try:
        with open(model_path, 'rb') as f:
            model_data = pickle.load(f)
        
        model = model_data['model']
        scaler = model_data['scaler']
        prompt_vectorizer = model_data['prompt_vectorizer']
        json_vectorizer = model_data['json_vectorizer']
        
        # Extract features
        prompt_features = extract_prompt_features(prompt)
        json_features = extract_json_features(generated_json)
        
        # Vectorize text
        prompt_tfidf = prompt_vectorizer.transform([prompt])
        json_tfidf = json_vectorizer.transform([generated_json])
        
        # Combine features
        structured_features = np.array([list({**prompt_features, **json_features}.values())])
        X = np.hstack([prompt_tfidf.toarray(), json_tfidf.toarray(), structured_features])
        
        # Scale and predict
        X_scaled = scaler.transform(X)
        prediction = model.predict(X_scaled)[0]
        
        return max(1.0, min(10.0, prediction))  # Clamp between 1 and 10
        
    except Exception as e:
        print(f"Error making prediction: {e}")
        return None

def main():
    """Main training function."""
    print("=== LLM Score Prediction Model Training ===")
    
    # Load data
    data = load_training_data()
    if not data:
        print("No training data available. Please run merge_datasets.py first.")
        return
    
    print(f"Training on {len(data)} samples")
    print(f"Score range: {min(item['llm_score'] for item in data):.1f} - {max(item['llm_score'] for item in data):.1f}")
    
    # Prepare features
    print("\nPreparing features...")
    X, y, prompt_vectorizer, json_vectorizer = prepare_features(data)
    print(f"Feature vector size: {X.shape[1]}")
    
    # Train models
    print("\nTraining models...")
    trained_models, results, scaler, X_test, y_test = train_models(X, y)
    
    # Find best model
    best_model_name = max(results.keys(), key=lambda k: results[k]['R2'])
    best_model = trained_models[best_model_name]
    
    print(f"\n=== Best Model: {best_model_name} ===")
    print(f"R² Score: {results[best_model_name]['R2']:.4f}")
    print(f"RMSE: {results[best_model_name]['RMSE']:.4f}")
    
    # Save best model
    save_model(best_model, scaler, prompt_vectorizer, json_vectorizer, "llm_score_predictor")
    
    print("\n=== Training Complete ===")
    print("You can now use predict_llm_score() to predict scores for new samples!")

if __name__ == "__main__":
    main()
