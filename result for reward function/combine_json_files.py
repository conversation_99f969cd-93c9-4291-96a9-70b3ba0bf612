import json
import os
import glob

def read_and_combine_json_files():
    """
    Read all JSON files in the current directory and combine them into a single file
    with '-----------------------------------' as separator between samples
    and 'key : value' format for each field.
    """
    
    # Get all JSON files in the current directory
    json_files = glob.glob("*.json")
    
    combined_data = []
    
    for json_file in json_files:
        print(f"Processing {json_file}...")
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            # If data is a list, extend combined_data
            if isinstance(data, list):
                combined_data.extend(data)
            # If data is a dict, append it as a single item
            elif isinstance(data, dict):
                combined_data.append(data)
                
        except Exception as e:
            print(f"Error processing {json_file}: {e}")
            continue
    
    # Write combined data to output file
    output_file = "combined_json_data.txt"
    
    with open(output_file, 'w', encoding='utf-8') as f:
        for i, item in enumerate(combined_data):
            if i > 0:  # Add separator before each item except the first
                f.write("\n-----------------------------------\n\n")
            
            # Write each key-value pair in the specified format
            if isinstance(item, dict):
                for key, value in item.items():
                    # Convert value to string and handle multiline values
                    if isinstance(value, (dict, list)):
                        value_str = json.dumps(value, indent=2, ensure_ascii=False)
                    else:
                        value_str = str(value)
                    
                    f.write(f"{key} : {value_str}\n")
            else:
                f.write(f"data : {json.dumps(item, indent=2, ensure_ascii=False)}\n")
    
    print(f"\nCombined {len(combined_data)} items from {len(json_files)} JSON files")
    print(f"Output saved to: {output_file}")

if __name__ == "__main__":
    read_and_combine_json_files()
