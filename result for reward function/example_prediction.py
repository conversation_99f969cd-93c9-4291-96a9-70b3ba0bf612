#!/usr/bin/env python3
"""
Example script showing how to use the trained LLM score prediction model.
"""

import json
from train_llm_score_predictor import predict_llm_score

def main():
    """Demonstrate model usage with example data."""
    print("=== LLM Score Prediction Example ===")
    
    # Example 1: A simple webhook workflow
    prompt1 = "Create an n8n workflow that is triggered by a webhook and sends an email notification."
    
    generated_json1 = """{
  "name": "Webhook to Email",
  "nodes": [
    {
      "parameters": {
        "method": "POST"
      },
      "name": "Webhook",
      "type": "webhook",
      "typeVersion": 1,
      "position": [100, 100]
    },
    {
      "parameters": {
        "to": "<EMAIL>",
        "subject": "Webhook Triggered",
        "text": "A webhook was triggered with data: {{$json}}"
      },
      "name": "Send Email",
      "type": "email",
      "typeVersion": 1,
      "position": [300, 100]
    }
  ],
  "connections": {
    "Webhook": {
      "main": [
        [
          { "node": "Send Email", "type": "main", "index": 0 }
        ]
      ]
    }
  },
  "active": false
}"""
    
    # Example 2: A more complex workflow with multiple integrations
    prompt2 = "Create an n8n workflow that reads data from Google Sheets, processes it with a function, stores results in Airtable, and sends a Slack notification."
    
    generated_json2 = """{
  "name": "Google Sheets to Airtable with Slack",
  "nodes": [
    {
      "parameters": {
        "sheetId": "1234567890",
        "range": "A1:Z100"
      },
      "name": "Google Sheets",
      "type": "googleSheets",
      "typeVersion": 1,
      "position": [100, 100]
    },
    {
      "parameters": {
        "functionCode": "return items.map(item => ({ ...item.json, processed: true }));"
      },
      "name": "Process Data",
      "type": "function",
      "typeVersion": 1,
      "position": [300, 100]
    },
    {
      "parameters": {
        "baseId": "appXXXXXXXXXX",
        "tableName": "ProcessedData"
      },
      "name": "Airtable",
      "type": "airtable",
      "typeVersion": 1,
      "position": [500, 100]
    },
    {
      "parameters": {
        "channel": "#general",
        "text": "Data processing complete. {{$json.length}} records processed."
      },
      "name": "Slack",
      "type": "slack",
      "typeVersion": 1,
      "position": [700, 100]
    }
  ],
  "connections": {
    "Google Sheets": {
      "main": [
        [
          { "node": "Process Data", "type": "main", "index": 0 }
        ]
      ]
    },
    "Process Data": {
      "main": [
        [
          { "node": "Airtable", "type": "main", "index": 0 }
        ]
      ]
    },
    "Airtable": {
      "main": [
        [
          { "node": "Slack", "type": "main", "index": 0 }
        ]
      ]
    }
  },
  "active": false
}"""
    
    # Example 3: Invalid JSON (should get lower score)
    prompt3 = "Create a simple n8n workflow with a timer trigger."
    
    generated_json3 = """{
  "name": "Timer Workflow",
  "nodes": [
    {
      "parameters": {
        "interval": "daily"
      },
      "name": "Timer",
      "type": "cron",
      "typeVersion": 1,
      "position": [100, 100]
    }
  ],
  "connections": {
    // Missing closing bracket - invalid JSON
}"""
    
    examples = [
        (prompt1, generated_json1, "Simple webhook to email"),
        (prompt2, generated_json2, "Complex multi-integration workflow"),
        (prompt3, generated_json3, "Invalid JSON example"),
    ]
    
    for i, (prompt, generated_json, description) in enumerate(examples, 1):
        print(f"\n--- Example {i}: {description} ---")
        print(f"Prompt: {prompt[:100]}...")
        
        try:
            predicted_score = predict_llm_score(prompt, generated_json)
            if predicted_score is not None:
                print(f"Predicted LLM Score: {predicted_score:.2f}/10.0")
                
                # Provide interpretation
                if predicted_score >= 8.0:
                    interpretation = "Excellent"
                elif predicted_score >= 6.0:
                    interpretation = "Good"
                elif predicted_score >= 4.0:
                    interpretation = "Fair"
                else:
                    interpretation = "Poor"
                
                print(f"Quality Assessment: {interpretation}")
            else:
                print("Failed to predict score")
                
        except Exception as e:
            print(f"Error: {e}")
    
    print("\n=== Interactive Mode ===")
    print("You can now test your own prompts and generated JSON!")
    print("(Press Ctrl+C to exit)")
    
    try:
        while True:
            print("\n" + "="*50)
            prompt = input("Enter your prompt: ").strip()
            if not prompt:
                break
                
            print("Enter your generated JSON (end with empty line):")
            json_lines = []
            while True:
                line = input()
                if not line:
                    break
                json_lines.append(line)
            
            generated_json = "\n".join(json_lines)
            if not generated_json.strip():
                print("No JSON provided, skipping...")
                continue
            
            predicted_score = predict_llm_score(prompt, generated_json)
            if predicted_score is not None:
                print(f"\nPredicted LLM Score: {predicted_score:.2f}/10.0")
                
                if predicted_score >= 8.0:
                    interpretation = "Excellent - High quality workflow"
                elif predicted_score >= 6.0:
                    interpretation = "Good - Solid workflow with minor issues"
                elif predicted_score >= 4.0:
                    interpretation = "Fair - Functional but needs improvement"
                else:
                    interpretation = "Poor - Significant issues present"
                
                print(f"Quality Assessment: {interpretation}")
            else:
                print("Failed to predict score")
                
    except KeyboardInterrupt:
        print("\n\nGoodbye!")

if __name__ == "__main__":
    main()
