#!/usr/bin/env python3
"""
Script to merge and combine JSON datasets for training a model to predict LLM scores.
Extracts prompt, generated_json, and llm_score attributes from multiple JSON files.
"""

import csv
import glob
import json
import os
from typing import Any, Dict, List


def load_json_file(filepath: str) -> List[Dict[str, Any]]:
    """Load a JSON file and return its contents."""
    try:
        with open(filepath, "r", encoding="utf-8") as f:
            data = json.load(f)
        return data
    except Exception as e:
        print(f"Error loading {filepath}: {e}")
        return []


def extract_training_data(
    data: List[Dict[str, Any]], source_file: str
) -> List[Dict[str, Any]]:
    """Extract prompt, generated_json, and llm_score from the data."""
    training_samples = []

    for i, item in enumerate(data):
        try:
            # Extract the required fields
            sample = {
                "prompt": item.get("prompt", ""),
                "generated_json": item.get("generated_json", ""),
                "llm_score": item.get("llm_score", 0.0),
                "source_file": source_file,
                "sample_id": f"{source_file}_{i}",
            }

            # Only add if all required fields are present
            if sample["prompt"] and sample["generated_json"] and "llm_score" in item:
                training_samples.append(sample)
            else:
                print(f"Skipping incomplete sample {i} from {source_file}")

        except Exception as e:
            print(f"Error processing sample {i} from {source_file}: {e}")
            continue

    return training_samples


def merge_all_datasets(json_pattern: str = "*.json") -> List[Dict[str, Any]]:
    """Merge all JSON datasets matching the pattern."""
    all_training_data = []

    # Find all JSON files matching the pattern
    json_files = glob.glob(json_pattern)
    json_files = [
        f for f in json_files if not f.endswith("-mean.json")
    ]  # Exclude mean files

    print(f"Found {len(json_files)} JSON files to process:")
    for file in json_files:
        print(f"  - {file}")

    # Process each file
    for filepath in json_files:
        print(f"\nProcessing {filepath}...")
        data = load_json_file(filepath)

        if data:
            filename = os.path.basename(filepath)
            training_samples = extract_training_data(data, filename)
            all_training_data.extend(training_samples)
            print(f"  Extracted {len(training_samples)} samples")
        else:
            print(f"  No data found in {filepath}")

    return all_training_data


def save_merged_dataset(
    training_data: List[Dict[str, Any]],
    output_file: str = "merged_training_dataset.json",
):
    """Save the merged dataset to a JSON file."""
    try:
        with open(output_file, "w", encoding="utf-8") as f:
            json.dump(training_data, f, indent=2, ensure_ascii=False)
        print(f"\nMerged dataset saved to {output_file}")
        print(f"Total samples: {len(training_data)}")
    except Exception as e:
        print(f"Error saving merged dataset: {e}")


def save_as_csv(
    training_data: List[Dict[str, Any]],
    output_file: str = "merged_training_dataset.csv",
):
    """Save the merged dataset as a CSV file for easier analysis."""
    try:
        if not training_data:
            print("No data to save as CSV")
            return

        # Get all fieldnames from the first item
        fieldnames = list(training_data[0].keys())

        with open(output_file, "w", newline="", encoding="utf-8") as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(training_data)

        print(f"Dataset also saved as CSV: {output_file}")
    except Exception as e:
        print(f"Error saving CSV: {e}")


def print_dataset_statistics(training_data: List[Dict[str, Any]]):
    """Print statistics about the merged dataset."""
    if not training_data:
        print("No training data to analyze.")
        return

    print(f"\n=== Dataset Statistics ===")
    print(f"Total samples: {len(training_data)}")

    # Score distribution
    scores = [item["llm_score"] for item in training_data]
    print(f"LLM Score range: {min(scores):.2f} - {max(scores):.2f}")
    print(f"Average LLM Score: {sum(scores)/len(scores):.2f}")

    # Source file distribution
    source_counts = {}
    for item in training_data:
        source = item["source_file"]
        source_counts[source] = source_counts.get(source, 0) + 1

    print(f"\nSamples per source file:")
    for source, count in sorted(source_counts.items()):
        print(f"  {source}: {count} samples")

    # Prompt length statistics
    prompt_lengths = [len(item["prompt"]) for item in training_data]
    print(f"\nPrompt length statistics:")
    print(f"  Min: {min(prompt_lengths)} characters")
    print(f"  Max: {max(prompt_lengths)} characters")
    print(f"  Average: {sum(prompt_lengths)/len(prompt_lengths):.1f} characters")

    # Generated JSON length statistics
    json_lengths = [len(item["generated_json"]) for item in training_data]
    print(f"\nGenerated JSON length statistics:")
    print(f"  Min: {min(json_lengths)} characters")
    print(f"  Max: {max(json_lengths)} characters")
    print(f"  Average: {sum(json_lengths)/len(json_lengths):.1f} characters")


def main():
    """Main function to merge all datasets."""
    print("=== JSON Dataset Merger for LLM Score Training ===")

    # Merge all datasets
    training_data = merge_all_datasets("qwen3-*.json")

    if training_data:
        # Print statistics
        print_dataset_statistics(training_data)

        # Save merged dataset
        save_merged_dataset(training_data)
        save_as_csv(training_data)

        print(f"\n=== Merge Complete ===")
        print(f"The merged dataset contains {len(training_data)} training samples")
        print(
            f"Each sample has: prompt, generated_json, llm_score, source_file, sample_id"
        )
        print(f"You can now use this dataset to train a model to predict LLM scores!")

    else:
        print("No training data was extracted. Please check your JSON files.")


if __name__ == "__main__":
    main()
    main()
