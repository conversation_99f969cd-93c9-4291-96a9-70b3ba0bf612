{"cells": [{"cell_type": "code", "execution_count": 1, "id": "cf9d20bd", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import seaborn as sns"]}, {"cell_type": "code", "execution_count": 4, "id": "2a29a094", "metadata": {}, "outputs": [], "source": ["df = pd.read_json(\"sample_similarity_jsons.json\")"]}, {"cell_type": "code", "execution_count": 5, "id": "55ed4ffb", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "ground_truth_json", "rawType": "object", "type": "string"}, {"name": "generated_json", "rawType": "object", "type": "string"}, {"name": "json_parsed", "rawType": "float64", "type": "float"}, {"name": "top_level_keys_present", "rawType": "float64", "type": "float"}, {"name": "workflow_name_valid", "rawType": "float64", "type": "float"}, {"name": "num_nodes", "rawType": "float64", "type": "float"}, {"name": "num_nodes_with_valid_type", "rawType": "float64", "type": "float"}, {"name": "num_nodes_with_valid_version", "rawType": "float64", "type": "float"}, {"name": "num_nodes_with_valid_structure", "rawType": "float64", "type": "float"}, {"name": "num_nodes_with_parameters", "rawType": "float64", "type": "float"}, {"name": "num_connections", "rawType": "float64", "type": "float"}, {"name": "num_connections_with_valid_structure", "rawType": "float64", "type": "float"}, {"name": "num_connections_with_valid_target_node", "rawType": "float64", "type": "float"}, {"name": "active_field_boolean", "rawType": "float64", "type": "float"}, {"name": "evaluation_results", "rawType": "object", "type": "unknown"}], "ref": "9d9a7e29-aff6-4daf-b1d2-8bad63738c19", "rows": [["0", "{\"name\": \"Get Reactions Telegram\", \"nodes\": [{\"parameters\": {\"url\": \"={{ $json.api_check }}\", \"options\": {}}, \"name\": \"HTTP Request\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 4.2, \"position\": [1420, 140]}, {\"parameters\": {\"mode\": \"runOnceForEachItem\", \"jsCode\": \"function generateApiUrl(telegramLink) {\\n    const apiBase = \\\"http://54.169.0.232:3000/api/message?\\\";\\n    \\n    // S\\u1eed d\\u1ee5ng regex \\u0111\\u1ec3 l\\u1ea5y ph\\u1ea7n peer v\\u00e0 id cu\\u1ed1i c\\u00f9ng\\n    const match = telegramLink.match(/t\\\\.me\\\\/([\\\\w_]+)(?:\\\\/\\\\d+)*\\\\/(\\\\d+)/);\\n    if (!match) return \\\"Invalid Telegram link\\\";\\n\\n    const peer = match[1];\\n    const id = match[2];\\n\\n    // T\\u1ea1o URL API ho\\u00e0n ch\\u1ec9nh\\n    return `${apiBase}peer=${peer}&id=${id}`;\\n}\\n\\nreturn {\\n  api_check: generateApiUrl($json['Link message telegram'])\\n}\"}, \"name\": \"Code\", \"type\": \"n8n-nodes-base.code\", \"typeVersion\": 2, \"position\": [1040, 260]}, {\"parameters\": {\"operation\": \"update\", \"documentId\": {\"__rl\": true, \"value\": \"1kdbl6o-T_HzCUfbnripELKrqKBXNpzOrzDIdsL3iRqo\", \"mode\": \"list\", \"cachedResultName\": \"Get Reactions Social\", \"cachedResultUrl\": \"https://docs.google.com/spreadsheets/d/1kdbl6o-T_HzCUfbnripELKrqKBXNpzOrzDIdsL3iRqo/edit?usp=drivesdk\"}, \"sheetName\": {\"__rl\": true, \"value\": 867241290, \"mode\": \"list\", \"cachedResultName\": \"Get reactions telegram\", \"cachedResultUrl\": \"https://docs.google.com/spreadsheets/d/1kdbl6o-T_HzCUfbnripELKrqKBXNpzOrzDIdsL3iRqo/edit#gid=867241290\"}, \"columns\": {\"mappingMode\": \"defineBelow\", \"value\": {\"Total ReactionEmoji\": \"={{ $json.totalReactions }}\", \"views\": \"={{ $json.views }}\", \"title\": \"={{ $json.title }}\", \"Link message telegram\": \"={{ $('Google Sheets Trigger').item.json['Link message telegram'] }}\", \"reactions\": \"={{ $json.reactions }}\"}, \"matchingColumns\": [\"Link message telegram\"], \"schema\": [{\"id\": \"Link message telegram\", \"displayName\": \"Link message telegram\", \"required\": false, \"defaultMatch\": false, \"display\": true, \"type\": \"string\", \"canBeUsedToMatch\": true, \"removed\": false}, {\"id\": \"Check\", \"displayName\": \"Check\", \"required\": false, \"defaultMatch\": false, \"display\": true, \"type\": \"string\", \"canBeUsedToMatch\": true, \"removed\": true}, {\"id\": \"views\", \"displayName\": \"views\", \"required\": false, \"defaultMatch\": false, \"display\": true, \"type\": \"string\", \"canBeUsedToMatch\": true, \"removed\": false}, {\"id\": \"title\", \"displayName\": \"title\", \"required\": false, \"defaultMatch\": false, \"display\": true, \"type\": \"string\", \"canBeUsedToMatch\": true, \"removed\": false}, {\"id\": \"reactions\", \"displayName\": \"reactions\", \"required\": false, \"defaultMatch\": false, \"display\": true, \"type\": \"string\", \"canBeUsedToMatch\": true, \"removed\": false}, {\"id\": \"Total ReactionEmoji\", \"displayName\": \"Total ReactionEmoji\", \"required\": false, \"defaultMatch\": false, \"display\": true, \"type\": \"string\", \"canBeUsedToMatch\": true, \"removed\": false}, {\"id\": \"row_number\", \"displayName\": \"row_number\", \"required\": false, \"defaultMatch\": false, \"display\": true, \"type\": \"string\", \"canBeUsedToMatch\": true, \"readOnly\": true, \"removed\": true}]}, \"options\": {}}, \"name\": \"Google Sheets\", \"type\": \"n8n-nodes-base.googleSheets\", \"typeVersion\": 4.5, \"position\": [1820, 140]}, {\"parameters\": {\"assignments\": {\"assignments\": [{\"id\": \"013084b1-35b1-4a9f-a612-b0e507bf7334\", \"name\": \"totalReactions\", \"value\": \"={{ $json.totalReactions }}\", \"type\": \"number\"}, {\"id\": \"8db7b2dc-9950-495a-a7c8-b832c5207748\", \"name\": \"reactions\", \"value\": \"={{ $json[\\\"reactions\\\"].map(r => r.count + \\\" \\\" + r.reaction.emoticon).join(\\\", \\\") }}\", \"type\": \"string\"}, {\"id\": \"5f53b01c-22b9-4c18-907c-a8293c313471\", \"name\": \"views\", \"value\": \"={{ $json.views }}\", \"type\": \"number\"}, {\"id\": \"3dd331e5-b937-411c-9cdd-82698fc9cb54\", \"name\": \"title\", \"value\": \"={{ $json.title ? $json.title:\\\"Kh\\u00f4ng c\\u00f3 title\\\" }}\", \"type\": \"string\"}]}, \"options\": {}}, \"name\": \"Edit Fields\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 3.4, \"position\": [1620, 140]}, {\"parameters\": {\"conditions\": {\"options\": {\"caseSensitive\": true, \"leftValue\": \"\", \"typeValidation\": \"strict\", \"version\": 2}, \"conditions\": [{\"id\": \"7adb0f16-8200-4cd9-8dd2-a69a5ec1876b\", \"leftValue\": \"={{ $json.api_check }}\", \"rightValue\": \"Invalid Telegram link\", \"operator\": {\"type\": \"string\", \"operation\": \"notEquals\"}}], \"combinator\": \"and\"}, \"options\": {}}, \"name\": \"If\", \"type\": \"n8n-nodes-base.if\", \"typeVersion\": 2.2, \"position\": [1240, 260]}, {\"parameters\": {\"conditions\": {\"options\": {\"caseSensitive\": true, \"leftValue\": \"\", \"typeValidation\": \"strict\", \"version\": 2}, \"conditions\": [{\"id\": \"ea25b040-16f6-4be6-a11c-a928ea45c75e\", \"leftValue\": \"={{ $json.Check }}\", \"rightValue\": \"Check\", \"operator\": {\"type\": \"string\", \"operation\": \"equals\", \"name\": \"filter.operator.equals\"}}, {\"id\": \"ea762a87-567b-4d2c-a5de-88b49956c49f\", \"leftValue\": \"={{ $json['Link message telegram'] }}\", \"rightValue\": \"\", \"operator\": {\"type\": \"string\", \"operation\": \"exists\", \"singleValue\": true}}, {\"id\": \"31669864-b708-4d8c-ae47-cd4a8402e366\", \"leftValue\": \"={{ $json['Link message telegram'] }}\", \"rightValue\": \"https://t.me/\", \"operator\": {\"type\": \"string\", \"operation\": \"startsWith\"}}], \"combinator\": \"and\"}, \"options\": {}}, \"name\": \"If1\", \"type\": \"n8n-nodes-base.if\", \"typeVersion\": 2.2, \"position\": [880, 400]}, {\"parameters\": {\"pollTimes\": {\"item\": [{\"mode\": \"everyMinute\"}]}, \"documentId\": {\"__rl\": true, \"value\": \"1kdbl6o-T_HzCUfbnripELKrqKBXNpzOrzDIdsL3iRqo\", \"mode\": \"list\", \"cachedResultName\": \"Get Reactions Social\", \"cachedResultUrl\": \"https://docs.google.com/spreadsheets/d/1kdbl6o-T_HzCUfbnripELKrqKBXNpzOrzDIdsL3iRqo/edit?usp=drivesdk\"}, \"sheetName\": {\"__rl\": true, \"value\": 867241290, \"mode\": \"list\", \"cachedResultName\": \"Get reactions telegram\", \"cachedResultUrl\": \"https://docs.google.com/spreadsheets/d/1kdbl6o-T_HzCUfbnripELKrqKBXNpzOrzDIdsL3iRqo/edit#gid=867241290\"}, \"options\": {}}, \"name\": \"Google Sheets Trigger\", \"type\": \"n8n-nodes-base.googleSheetsTrigger\", \"typeVersion\": 1, \"position\": [680, 400]}], \"connections\": {\"Code\": {\"main\": [[{\"node\": \"If\", \"type\": \"main\", \"index\": 0}]]}, \"HTTP Request\": {\"main\": [[{\"node\": \"Edit Fields\", \"type\": \"main\", \"index\": 0}]]}, \"Edit Fields\": {\"main\": [[{\"node\": \"Google Sheets\", \"type\": \"main\", \"index\": 0}]]}, \"If\": {\"main\": [[{\"node\": \"HTTP Request\", \"type\": \"main\", \"index\": 0}]]}, \"If1\": {\"main\": [[{\"node\": \"Code\", \"type\": \"main\", \"index\": 0}]]}, \"Google Sheets Trigger\": {\"main\": [[{\"node\": \"If1\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}", "{\"name\": \"Telegram Reaction Extractor\", \"nodes\": [{\"parameters\": {\"sheetName\": \"Telegram\", \"triggerOn\": \"rowUpdated\", \"columns\": [\"<PERSON>\", \"Reaction\", \"Count\"]}, \"name\": \"Google Sheets Trigger\", \"type\": \"n8n-nodes-base.googleSheetsTrigger\", \"typeVersion\": 1, \"position\": [0, 0]}, {\"parameters\": {\"url\": \"https://api.telegram.org/bot<bot_token>/getReactions\", \"queryParameters\": {\"query\": {\"username\": \"={{ $json.username }}\"}}, \"headerParameters\": {\"Content-Type\": \"application/json\"}}, \"name\": \"HTTP Request\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 1, \"position\": [200, 0]}, {\"parameters\": {\"columns\": {\"Reaction\": \"={{ $json.reaction }}\", \"Count\": \"={{ $json.count }}\"}, \"sheetName\": \"Telegram\", \"operation\": \"update\"}, \"name\": \"Google Sheets\", \"type\": \"n8n-nodes-base.googleSheets\", \"typeVersion\": 1, \"position\": [600, 0]}, {\"parameters\": {\"conditions\": {\"username\": {\"equals\": null}, \"reactions\": {\"equals\": null}}}, \"name\": \"Condition\", \"type\": \"n8n-nodes-base.condition\", \"typeVersion\": 1, \"position\": [400, 0]}, {\"parameters\": {\"columns\": {\"Reaction\": \"No Telegram link found\", \"Count\": \"0\"}}, \"name\": \"Set\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 1, \"position\": [600, 200]}, {\"parameters\": {\"code\": \"const username = $json['Link'].match(/https:\\\\/\\\\/t\\\\.me\\\\/([^\\\\/]+)/)[1];\\n\\nif (username) {\\n  return { username: username };\\n} else {\\n  return null;\\n}\"}, \"name\": \"Code\", \"type\": \"n8n-nodes-base.code\", \"typeVersion\": 1, \"position\": [100, 0]}, {\"parameters\": {\"code\": \"const reactions = $json['results']\\n  .map(result => ({\\n    reaction: result.reaction,\\n    count: result.count\\n  }));\\n\\nreturn { reactions: reactions };\"}, \"name\": \"Code1\", \"type\": \"n8n-nodes-base.code\", \"typeVersion\": 1, \"position\": [300, 0]}], \"connections\": {\"Google Sheets Trigger\": {\"main\": [[{\"node\": \"Code\", \"type\": \"main\", \"index\": 0}]]}, \"HTTP Request\": {\"main\": [[{\"node\": \"Code1\", \"type\": \"main\", \"index\": 0}]]}, \"Google Sheets\": {\"main\": [[{\"node\": \"Condition\", \"type\": \"main\", \"index\": 0}]]}, \"Condition\": {\"main\": [[{\"node\": \"Set\", \"type\": \"main\", \"index\": 0}], [{\"node\": \"Google Sheets\", \"type\": \"main\", \"index\": 0}]]}, \"Code\": {\"main\": [[{\"node\": \"HTTP Request\", \"type\": \"main\", \"index\": 0}]]}, \"Code1\": {\"main\": [[{\"node\": \"Condition\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}", "1.0", "4.0", "1.0", "7.0", "6.0", "6.0", "7.0", "7.0", "7.0", "7.0", "7.0", "1.0", null], ["1", "{\"name\": \"AI Intern Test - YouTube Analysis\", \"nodes\": [{\"parameters\": {\"path\": \"youtube-analysis\", \"method\": \"POST\", \"options\": {\"responseMode\": \"lastNode\"}, \"authentication\": \"none\"}, \"name\": \"Webhook\", \"type\": \"n8n-nodes-base.webhook\", \"typeVersion\": 1, \"position\": [250, 300]}, {\"parameters\": {\"functionCode\": \"const inputData = $json.body || $json;\\nconst url = inputData.url;\\n\\nif (!url) {\\n  throw new Error('URL not found in input. Please provide a valid YouTube channel URL.');\\n}\\n\\n// Handle both @username and channel ID formats\\nlet channelUsername = '';\\nlet channelId = '';\\n\\nif (url.includes('youtube.com/@')) {\\n  channelUsername = url.split('youtube.com/@')[1].split(/[/?#]/)[0];\\n  console.log('Username format detected:', channelUsername);\\n  return [{ json: { channelUsername, url, lookupNeeded: true } }];\\n} else if (url.includes('/channel/')) {\\n  channelId = url.split('/channel/')[1].split(/[/?#]/)[0];\\n  console.log('Channel ID format detected:', channelId);\\n  // Direct channelId - bypass lookup completely\\n  return [{ json: { channelId, url, lookupNeeded: false } }];\\n} else if (url.includes('/user/')) {\\n  channelUsername = url.split('/user/')[1].split(/[/?#]/)[0];\\n  return [{ json: { channelUsername, url, lookupNeeded: true } }];\\n} else if (url.includes('youtube.com/c/')) {\\n  channelUsername = url.split('youtube.com/c/')[1].split(/[/?#]/)[0];\\n  return [{ json: { channelUsername, url, lookupNeeded: true } }];\\n} else {\\n  // Assume it's a channel name or search query\\n  return [{ json: { lookupNeeded: true, query: url } }];\\n}\"}, \"name\": \"Parse Link\", \"type\": \"n8n-nodes-base.function\", \"typeVersion\": 1, \"position\": [450, 300]}, {\"parameters\": {\"functionCode\": \"// Determine if we need to convert username to channel ID\\nif ($json.channelId) {\\n  // We already have the channel ID\\n  return [{ json: { channelId: $json.channelId, lookupNeeded: false } }];\\n} else if ($json.channelUsername || $json.query) {\\n  // We need to look up the channel ID from the username\\n  return [{ json: { channelUsername: $json.channelUsername || $json.query, lookupNeeded: true } }];\\n} else {\\n  throw new Error('Neither channel ID nor username found in the parsed URL');\\n}\"}, \"name\": \"Channel ID Logic\", \"type\": \"n8n-nodes-base.function\", \"typeVersion\": 1, \"position\": [650, 300]}, {\"parameters\": {\"dataType\": \"boolean\", \"value1\": \"={{$json.lookupNeeded}}\", \"rules\": {\"rules\": [{\"value2\": true, \"operation\": \"equal\"}, {\"value2\": false, \"operation\": \"equal\"}]}}, \"name\": \"Channel Route Switch\", \"type\": \"n8n-nodes-base.switch\", \"typeVersion\": 1, \"position\": [750, 300]}, {\"parameters\": {\"url\": \"=https://www.googleapis.com/youtube/v3/search?part=snippet&type=channel&q={{$json.channelUsername || $json.query}}&key=AIzaSyBpJbjQVkDeXfcUCxwPUxWz1mt9Ss3Rxpk&maxResults=1\", \"method\": \"GET\", \"options\": {}}, \"name\": \"Lookup Channel ID\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 4.1, \"position\": [850, 200]}, {\"parameters\": {\"functionCode\": \"// Extract channel ID from search results\\nif ($json.items && $json.items.length > 0) {\\n  const channelId = $json.items[0].id.channelId;\\n  return [{ json: { channelId } }];\\n} else {\\n  throw new Error('Channel not found for the given username.');\\n}\"}, \"name\": \"Extract Channel ID\", \"type\": \"n8n-nodes-base.function\", \"typeVersion\": 1, \"position\": [1050, 200]}, {\"parameters\": {\"url\": \"=https://www.googleapis.com/youtube/v3/channels?part=snippet,statistics&id={{$json.channelId}}&key=AIzaSyBpJbjQVkDeXfcUCxwPUxWz1mt9Ss3Rxpk\", \"method\": \"GET\", \"options\": {}}, \"name\": \"Get Channel Info\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 4.1, \"position\": [1250, 300]}, {\"parameters\": {\"url\": \"=https://www.googleapis.com/youtube/v3/search?part=id&channelId={{$json.channelId}}&order=date&maxResults=10&type=video&key=AIzaSyBpJbjQVkDeXfcUCxwPUxWz1mt9Ss3Rxpk\", \"method\": \"GET\", \"options\": {}}, \"name\": \"Get Recent Videos\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 4.1, \"position\": [1250, 450]}, {\"parameters\": {\"functionCode\": \"// Extract video IDs\\nif (!$json.items || $json.items.length === 0) {\\n  return [{ json: { videoIds: '', error: 'No videos found' } }];\\n}\\n\\nconst ids = $json.items.map(i => i.id.videoId).join(',');\\nreturn [{ json: { videoIds: ids } }];\"}, \"name\": \"Collect IDs\", \"type\": \"n8n-nodes-base.function\", \"typeVersion\": 1, \"position\": [1450, 450]}, {\"parameters\": {\"url\": \"=https://www.googleapis.com/youtube/v3/videos?part=snippet,statistics&id={{$json.videoIds}}&key=AIzaSyBpJbjQVkDeXfcUCxwPUxWz1mt9Ss3Rxpk\", \"method\": \"GET\", \"options\": {}}, \"name\": \"Get Video Stats\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 4.1, \"position\": [1650, 450]}, {\"parameters\": {\"functionCode\": \"// Combine channel and video data\\nconst channelData = $items('Get Channel Info')[0].json;\\nconst videoData = $json;\\n\\nif (!channelData.items || channelData.items.length === 0) {\\n  throw new Error('No channel data found');\\n}\\n\\nconst ch = channelData.items[0];\\nconst vids = videoData.items || [];\\n\\n// Basic stats\\nconst stats = {\\n  name: ch.snippet.title,\\n  id: ch.id,\\n  publishedAt: ch.snippet.publishedAt,\\n  subscribers: parseInt(ch.statistics.subscriberCount) || 0,\\n  videoCount: parseInt(ch.statistics.videoCount) || 0,\\n  viewCount: parseInt(ch.statistics.viewCount) || 0\\n};\\n\\n// Content metrics\\nlet content = { avgLikes: 0, postingFreqDays: 0, engagement: 0 };\\n\\nif (vids.length > 0) {\\n  const totalLikes = vids.reduce((sum, v) => sum + (parseInt(v.statistics.likeCount) || 0), 0);\\n  const totalViews = vids.reduce((sum, v) => sum + (parseInt(v.statistics.viewCount) || 0), 0);\\n  const avgLikes = Math.round(totalLikes / vids.length);\\n  const avgViews = Math.round(totalViews / vids.length);\\n  \\n  // Calculate posting frequency\\n  const dates = vids.map(v => new Date(v.snippet.publishedAt));\\n  const oldestDate = new Date(Math.min(...dates));\\n  const freqDays = ((Date.now() - oldestDate) / (1000 * 60 * 60 * 24)) / vids.length;\\n  \\n  // Average engagement rate (likes per view)\\n  const engagement = (totalLikes / totalViews * 100).toFixed(2);\\n  \\n  content = {\\n    avgLikes,\\n    avgViews,\\n    postingFreqDays: freqDays.toFixed(1),\\n    engagement: engagement + '%'\\n  };\\n}\\n\\n// Extract common tags and topics\\nconst allTags = vids.flatMap(v => v.snippet.tags || []);\\nconst tagCount = {};\\nallTags.forEach(tag => {\\n  tagCount[tag] = (tagCount[tag] || 0) + 1;\\n});\\n\\n// Sort tags by frequency\\nconst topTags = Object.entries(tagCount)\\n  .sort((a, b) => b[1] - a[1])\\n  .slice(0, 5)\\n  .map(([tag]) => tag);\\n\\ncontent.topTags = topTags;\\n\\n// Final output\\nreturn [{ json: { stats, content, recentVideos: vids.slice(0, 5).map(v => ({ \\n  title: v.snippet.title,\\n  publishedAt: v.snippet.publishedAt,\\n  views: v.statistics.viewCount,\\n  likes: v.statistics.likeCount,\\n  url: https://www.youtube.com/watch?v=${v.id}\\n})) } }];\"}, \"name\": \"Compute Metrics\", \"type\": \"n8n-nodes-base.function\", \"typeVersion\": 1, \"position\": [1850, 350]}, {\"parameters\": {\"url\": \"https://openrouter.ai/api/v1/chat/completions\", \"authentication\": \"predefinedCredentialType\", \"nodeCredentialType\": \"httpHeaderAuth\", \"method\": \"POST\", \"sendBody\": true, \"specifyBody\": \"json\", \"jsonBody\": {\"model\": \"openai/gpt-3.5-turbo\", \"messages\": [{\"role\": \"system\", \"content\": \"You are a YouTube analytics expert who provides insightful channel analysis reports.\"}, {\"role\": \"user\", \"content\": \"={{Generate a comprehensive YouTube channel analysis report using the following data: Basic stats: Subscribers: ${$json.stats.subscribers}, Views: ${$json.stats.viewCount}, Videos: ${$json.stats.videoCount}. Content metrics: AvgViews: ${$json.content.avgViews}, AvgLikes: ${$json.content.avgLikes}, Engagement: ${$json.content.engagement}. Provide a well-structured analysis that includes: 1. Channel overview & performance summary 2. Content strategy analysis 3. Audience engagement assessment 4. Competitive positioning within their niche 5. Growth trends and opportunities.}}\"}], \"temperature\": 0.3, \"max_tokens\": 1000}, \"options\": {\"redirect\": {\"followRedirects\": true}}}, \"name\": \"Analysis Report LLM\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 4.1, \"position\": [2050, 250]}, {\"parameters\": {\"url\": \"https://openrouter.ai/api/v1/chat/completions\", \"authentication\": \"predefinedCredentialType\", \"nodeCredentialType\": \"httpHeaderAuth\", \"method\": \"POST\", \"sendBody\": true, \"specifyBody\": \"json\", \"jsonBody\": {\"model\": \"openai/gpt-3.5-turbo\", \"messages\": [{\"role\": \"system\", \"content\": \"You are a YouTube content strategist who specializes in creating viral content ideas.\"}, {\"role\": \"user\", \"content\": \"Based on this YouTube channel's stats and metrics, suggest 5 specific viral content ideas that would work well for this creator.\\n\\nChannel stats: {{$json.stats}}\\nContent metrics: {{$json.content}}\\nRecent videos: {{$json.recentVideos}}\"}], \"temperature\": 0.7, \"max_tokens\": 1000}, \"options\": {\"redirect\": {\"followRedirects\": true}}}, \"name\": \"Prompt Suggestions LLM\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 4.1, \"position\": [2050, 400]}, {\"parameters\": {\"url\": \"https://openrouter.ai/api/v1/chat/completions\", \"authentication\": \"predefinedCredentialType\", \"nodeCredentialType\": \"httpHeaderAuth\", \"method\": \"POST\", \"sendBody\": true, \"specifyBody\": \"json\", \"jsonBody\": {\"model\": \"openai/gpt-3.5-turbo\", \"messages\": [{\"role\": \"system\", \"content\": \"You are an expert YouTube copywriter who can create engaging video titles and descriptions.\"}, {\"role\": \"user\", \"content\": \"Based on the recent videos from this YouTube channel, create 5 fresh title and description combinations that maintain the channel's style but could improve click-through rates.\\n\\nRecent videos: {{$json.recentVideos}}\\n\\nFor each recommendation, provide:\\n1. Original title\\n2. Improved title\\n3. Compelling description (2-3 sentences)\\n4. Tags to include\\n\\nMake sure the new titles and descriptions are optimized for search but still authentic to the channel's voice.\"}], \"temperature\": 0.7, \"max_tokens\": 1000}, \"options\": {\"redirect\": {\"followRedirects\": true}}}, \"name\": \"Copywriting LLM\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 4.1, \"position\": [2050, 550]}, {\"parameters\": {\"functionCode\": \"const analysis=$node['Analysis Report LLM'].json.choices[0].message.content;const ideas=$node['Prompt Suggestions LLM'].json.choices[0].message.content;const copy=$node['Copywriting LLM'].json.choices[0].message.content;const name=$node['Compute Metrics'].json.stats.name;return[{json:{channelName:name,channelAnalysis:analysis,contentIdeas:ideas,copywritingSuggestions:copy}}];\"}, \"name\": \"Compile Final Report\", \"type\": \"n8n-nodes-base.function\", \"typeVersion\": 1, \"position\": [2250, 400]}], \"connections\": {\"Webhook\": {\"main\": [[{\"node\": \"Parse Link\", \"type\": \"main\", \"index\": 0}]]}, \"Parse Link\": {\"main\": [[{\"node\": \"Channel Route Switch\", \"type\": \"main\", \"index\": 0}]]}, \"Channel ID Logic\": {\"main\": [[{\"node\": \"Channel Route Switch\", \"type\": \"main\", \"index\": 0}]]}, \"Channel Route Switch\": {\"main\": [[{\"node\": \"Lookup Channel ID\", \"type\": \"main\", \"index\": 0}], [{\"node\": \"Get Channel Info\", \"type\": \"main\", \"index\": 0}]]}, \"Lookup Channel ID\": {\"main\": [[{\"node\": \"Extract Channel ID\", \"type\": \"main\", \"index\": 0}]]}, \"Extract Channel ID\": {\"main\": [[{\"node\": \"Get Channel Info\", \"type\": \"main\", \"index\": 0}]]}, \"Get Channel Info\": {\"main\": [[{\"node\": \"Get Recent Videos\", \"type\": \"main\", \"index\": 0}]]}, \"Get Recent Videos\": {\"main\": [[{\"node\": \"Collect IDs\", \"type\": \"main\", \"index\": 0}]]}, \"Collect IDs\": {\"main\": [[{\"node\": \"Get Video Stats\", \"type\": \"main\", \"index\": 0}]]}, \"Get Video Stats\": {\"main\": [[{\"node\": \"Compute Metrics\", \"type\": \"main\", \"index\": 0}]]}, \"Compute Metrics\": {\"main\": [[{\"node\": \"Analysis Report LLM\", \"type\": \"main\", \"index\": 0}]]}, \"Analysis Report LLM\": {\"main\": [[{\"node\": \"Prompt Suggestions LLM\", \"type\": \"main\", \"index\": 0}]]}, \"Prompt Suggestions LLM\": {\"main\": [[{\"node\": \"Copywriting LLM\", \"type\": \"main\", \"index\": 0}]]}, \"Copywriting LLM\": {\"main\": [[{\"node\": \"Compile Final Report\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}", "", null, null, null, null, null, null, null, null, null, null, null, null, "{'error': 'Invalid JSON'}"], ["2", "{\"name\": \"open_node_creator_for_connection\", \"nodes\": [{\"parameters\": {}, \"name\": \"When clicking \\u2018Test workflow\\u2019\", \"type\": \"n8n-nodes-base.manualTrigger\", \"typeVersion\": 1, \"position\": [740, 520]}, {\"parameters\": {}, \"name\": \"Token Splitter\", \"type\": \"@n8n/n8n-nodes-langchain.textSplitterTokenSplitter\", \"typeVersion\": 1, \"position\": [1180, 540]}, {\"parameters\": {}, \"name\": \"Memory\", \"type\": \"@n8n/n8n-nodes-langchain.memoryXata\", \"typeVersion\": 1.2, \"position\": [940, 540]}, {\"parameters\": {}, \"name\": \"Output Parser\", \"type\": \"@n8n/n8n-nodes-langchain.outputParserStructured\", \"typeVersion\": 1.1, \"position\": [1060, 540]}, {\"parameters\": {}, \"name\": \"Tool\", \"type\": \"@n8n/n8n-nodes-langchain.toolWikipedia\", \"typeVersion\": 1, \"position\": [1300, 540]}, {\"parameters\": {\"options\": {}}, \"name\": \"Embeddings\", \"type\": \"@n8n/n8n-nodes-langchain.embeddingsHuggingFaceInference\", \"typeVersion\": 1, \"position\": [1420, 540]}, {\"parameters\": {\"tableName\": {\"__rl\": true, \"mode\": \"list\", \"value\": \"\"}, \"options\": {}}, \"name\": \"Vector Store\", \"type\": \"@n8n/n8n-nodes-langchain.vectorStoreSupabase\", \"typeVersion\": 1, \"position\": [1540, 540]}], \"connections\": {}, \"active\": false}", "{\"name\": \"My workflow\", \"nodes\": [{\"parameters\": {}, \"name\": \"manualTrigger\", \"type\": \"manualTrigger\", \"typeVersion\": 1, \"position\": [0, 0]}, {\"parameters\": {}, \"name\": \"tokenSplitter\", \"type\": \"tokenSplitter\", \"typeVersion\": 1, \"position\": [220, 0]}, {\"parameters\": {\"database\": \"my-database\", \"table\": \"my-table\"}, \"name\": \"Xata\", \"type\": \"xata\", \"typeVersion\": 1, \"position\": [440, 0]}, {\"parameters\": {}, \"name\": \"structuredOutputParser\", \"type\": \"structuredOutputParser\", \"typeVersion\": 1, \"position\": [660, 0]}, {\"parameters\": {}, \"name\": \"wikipedia\", \"type\": \"wikipedia\", \"typeVersion\": 1, \"position\": [880, 0]}, {\"parameters\": {\"modelName\": \"sentence-transformers/all-MiniLM-L6-v2\"}, \"name\": \"HuggingFaceEmbeddings\", \"type\": \"huggingFaceEmbeddings\", \"typeVersion\": 1, \"position\": [1100, 0]}, {\"parameters\": {\"supabaseUrl\": \"https://my-supabase-url.com\", \"supabaseApiKey\": \"my-supabase-api-key\"}, \"name\": \"SupabaseVectorStore\", \"type\": \"supabaseVectorStore\", \"typeVersion\": 1, \"position\": [1320, 0]}], \"connections\": {\"manualTrigger\": {\"main\": [[{\"node\": \"tokenSplitter\", \"type\": \"main\", \"index\": 0}]]}, \"tokenSplitter\": {\"main\": [[{\"node\": \"Xata\", \"type\": \"main\", \"index\": 0}]]}, \"Xata\": {\"main\": [[{\"node\": \"structuredOutputParser\", \"type\": \"main\", \"index\": 0}]]}, \"structuredOutputParser\": {\"main\": [[{\"node\": \"wikipedia\", \"type\": \"main\", \"index\": 0}]]}, \"wikipedia\": {\"main\": [[{\"node\": \"HuggingFaceEmbeddings\", \"type\": \"main\", \"index\": 0}]]}, \"HuggingFaceEmbeddings\": {\"main\": [[{\"node\": \"SupabaseVectorStore\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}", "1.0", "4.0", "1.0", "7.0", "0.0", "0.0", "7.0", "7.0", "6.0", "6.0", "6.0", "1.0", null], ["3", "{\"name\": \"Untitled Workflow\", \"nodes\": [{\"parameters\": {\"rule\": {\"interval\": [{\"triggerAtHour\": 9}]}}, \"name\": \"Every day at 06:00\", \"type\": \"n8n-nodes-base.scheduleTrigger\", \"typeVersion\": 1, \"position\": [960, 600]}, {\"parameters\": {\"query\": \"query ($filter: IssueFilter) {\\n  issues(filter: $filter, first: 100) {\\n    pageInfo {\\n      hasNextPage\\n      endCursor\\n    }\\n    nodes {\\n      id\\n      identifier\\n      url\\n      title\\n      priorityLabel\\n      createdAt\\n      completedAt\\n      state {\\n        type\\n        name\\n      }\\n      cycle {\\n        number\\n      }\\n      estimate\\n      labels { nodes { name } }\\n    }\\n  }\\n}\", \"endpoint\": \"https://api.linear.app/graphql\", \"variables\": \"={\\n  \\\"filter\\\": {\\n    \\\"team\\\": {\\n      \\\"name\\\":  {\\n        \\\"eq\\\": \\\"Adore\\\"\\n      }\\n    }\\n  }\\n}\", \"requestFormat\": \"json\", \"authentication\": \"headerAuth\"}, \"name\": \"Get all your team's tickets\", \"type\": \"n8n-nodes-base.graphql\", \"typeVersion\": 1, \"position\": [1200, 600]}, {\"parameters\": {\"color\": 7, \"width\": 256.14371825927645, \"height\": 100, \"content\": \"\\ud83d\\udc47\\ud83c\\udffd Set your team name here in the filter. \\n(Our team's name is Adore)\"}, \"name\": \"Sticky Note1\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [1200, 460]}, {\"parameters\": {\"options\": {}, \"conditions\": {\"options\": {\"leftValue\": \"\", \"caseSensitive\": true, \"typeValidation\": \"strict\"}, \"combinator\": \"and\", \"conditions\": [{\"id\": \"f5ab21aa-b2e0-4885-9278-6756c2c544f9\", \"operator\": {\"type\": \"boolean\", \"operation\": \"true\", \"singleValue\": true}, \"leftValue\": \"={{ $json.data.issues.pageInfo.hasNextPage }}\", \"rightValue\": 0}]}}, \"name\": \"if has next page\", \"type\": \"n8n-nodes-base.if\", \"typeVersion\": 2, \"position\": [1400, 780]}, {\"parameters\": {\"fields\": {\"values\": [{\"name\": \"after\", \"stringValue\": \"={{ $json.data.issues.pageInfo.endCursor }}\"}]}, \"include\": \"none\", \"options\": {}}, \"name\": \"Get end cursor\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 3.2, \"position\": [1620, 920]}, {\"parameters\": {\"query\": \"=query ($filter: IssueFilter) {\\n  issues(filter: $filter, first: 100, after: \\\"{{ $json.after }}\\\") {\\n    nodes {\\n      id\\n      identifier\\n      url\\n      title\\n      priorityLabel\\n      createdAt\\n      completedAt\\n      state {\\n        type\\n        name\\n      }\\n      cycle {\\n        number\\n      }\\n      estimate\\n      labels { nodes { name } }\\n    }\\n    pageInfo {\\n      hasNextPage\\n      endCursor\\n    }\\n  }\\n}\", \"endpoint\": \"https://api.linear.app/graphql\", \"variables\": \"={\\n  \\\"filter\\\": {\\n    \\\"team\\\": {\\n      \\\"name\\\":  {\\n        \\\"eq\\\": \\\"Adore\\\"\\n      }\\n    }\\n  }\\n}\", \"requestFormat\": \"json\", \"authentication\": \"headerAuth\"}, \"name\": \"Get next page\", \"type\": \"n8n-nodes-base.graphql\", \"typeVersion\": 1, \"position\": [1880, 920]}, {\"parameters\": {\"color\": 7, \"width\": 256.14371825927645, \"height\": 100, \"content\": \"\\ud83d\\udc48\\ud83c\\udffd Set your team name here in the filter. \\n(Our team's name is Adore)\"}, \"name\": \"Sticky Note\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [2060, 940]}, {\"parameters\": {\"options\": {}, \"fieldToSplitOut\": \"data.issues.nodes\"}, \"name\": \"Split out the tickets\", \"type\": \"n8n-nodes-base.splitOut\", \"typeVersion\": 1, \"position\": [2020, 600]}, {\"parameters\": {\"color\": 7, \"width\": 256.14371825927645, \"height\": 100, \"content\": \"\\ud83d\\udc47\\ud83c\\udffd Adjust any custom fields. Here we set labels and default estimate of 1\"}, \"name\": \"Sticky Note2\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [2240, 460]}, {\"parameters\": {\"fields\": {\"values\": [{\"name\": \"estimate\", \"type\": \"numberValue\", \"numberValue\": \"={{ $json.estimate ?? 1 }}\"}, {\"name\": \"labels\", \"stringValue\": \"={{ $json.labels.nodes.map((label) => label.name).toString() }}\"}]}, \"options\": {}}, \"name\": \"Set custom fields\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 3.2, \"position\": [2240, 600]}, {\"parameters\": {\"color\": 5, \"width\": 403.45318928152614, \"height\": 280.9004675550071, \"content\": \"### \\ud83d\\udc68\\u200d\\ud83c\\udfa4 Setup\\n1. Add Linear API header key\\n2. Add Google sheets creds\\n3. Update which teams to get tickets from in Graphql Nodes\\n4. Update which Google Sheets page to write all the tickets to. \\n **You only need to add one column, id. Google Sheets node in automatic mapping mode will handle adding the rest of the columns.**\\n5. Set any custom data on each ticket\\n6. Activate workflow \\ud83d\\ude80\"}, \"name\": \"Sticky Note3\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [740, 300]}, {\"parameters\": {\"color\": 7, \"width\": 256.14371825927645, \"height\": 100, \"content\": \"\\ud83d\\udc46\\ud83c\\udffd Update which Google sheet to write to\"}, \"name\": \"Sticky Note4\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [2700, 780]}, {\"parameters\": {\"columns\": {\"value\": {}, \"schema\": [{\"id\": \"id\", \"type\": \"string\", \"display\": true, \"removed\": false, \"required\": false, \"displayName\": \"id\", \"defaultMatch\": true, \"canBeUsedToMatch\": true}], \"mappingMode\": \"autoMapInputData\", \"matchingColumns\": [\"id\"]}, \"options\": {}, \"operation\": \"appendOrUpdate\", \"sheetName\": {\"__rl\": true, \"mode\": \"list\", \"value\": 2072772685, \"cachedResultUrl\": \"https://docs.google.com/spreadsheets/d/1pQxSIZ2dSoA8Fmr3B4EId9VOQXH1hVuOZgCHxcaKN7k/edit#gid=2072772685\", \"cachedResultName\": \"Sheet2\"}, \"documentId\": {\"__rl\": true, \"mode\": \"list\", \"value\": \"1pQxSIZ2dSoA8Fmr3B4EId9VOQXH1hVuOZgCHxcaKN7k\", \"cachedResultUrl\": \"https://docs.google.com/spreadsheets/d/1pQxSIZ2dSoA8Fmr3B4EId9VOQXH1hVuOZgCHxcaKN7k/edit?usp=drivesdk\", \"cachedResultName\": \"Adore tickets\"}}, \"name\": \"Write tickets to Sheets\", \"type\": \"n8n-nodes-base.googleSheets\", \"typeVersion\": 4.2, \"position\": [2720, 600]}, {\"parameters\": {\"mode\": \"runOnceForEachItem\", \"jsCode\": \"function flattenObject(ob) {\\n    var toReturn = {};\\n\\n    for (var i in ob) {\\n        if (!ob.hasOwnProperty(i)) continue;\\n\\n        if ((typeof ob[i]) == 'object' && ob[i] !== null) {\\n            var flatObject = flattenObject(ob[i]);\\n            for (var x in flatObject) {\\n                if (!flatObject.hasOwnProperty(x)) continue;\\n\\n                toReturn[i + '.' + x] = flatObject[x];\\n            }\\n        } else {\\n            toReturn[i] = ob[i];\\n        }\\n    }\\n    return toReturn;\\n}\\n\\nreturn flattenObject($input.item.json);\"}, \"name\": \"Flatten object to have simple fields to filter by\", \"type\": \"n8n-nodes-base.code\", \"typeVersion\": 2, \"position\": [2480, 600]}], \"connections\": {\"Get next page\": {\"main\": [[{\"node\": \"if has next page\", \"type\": \"main\", \"index\": 0}, {\"node\": \"Split out the tickets\", \"type\": \"main\", \"index\": 0}]]}, \"Get end cursor\": {\"main\": [[{\"node\": \"Get next page\", \"type\": \"main\", \"index\": 0}]]}, \"if has next page\": {\"main\": [[{\"node\": \"Get end cursor\", \"type\": \"main\", \"index\": 0}]]}, \"Set custom fields\": {\"main\": [[{\"node\": \"Flatten object to have simple fields to filter by\", \"type\": \"main\", \"index\": 0}]]}, \"Every day at 06:00\": {\"main\": [[{\"node\": \"Get all your team's tickets\", \"type\": \"main\", \"index\": 0}]]}, \"Split out the tickets\": {\"main\": [[{\"node\": \"Set custom fields\", \"type\": \"main\", \"index\": 0}]]}, \"Get all your team's tickets\": {\"main\": [[{\"node\": \"Split out the tickets\", \"type\": \"main\", \"index\": 0}, {\"node\": \"if has next page\", \"type\": \"main\", \"index\": 0}]]}, \"Flatten object to have simple fields to filter by\": {\"main\": [[{\"node\": \"Write tickets to Sheets\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}", "{\"name\": \"Get Tickets from Linear\", \"nodes\": [{\"parameters\": {\"teamId\": \"1234567890\", \"after\": \"={{ $json.pagination.next }}\"}, \"name\": \"Linear\", \"type\": \"n8n-nodes-base.linear\", \"typeVersion\": 1, \"position\": [100, 100]}, {\"parameters\": {\"fieldToSplit\": \"tickets\", \"batchSize\": 100}, \"name\": \"Loop Over Items\", \"type\": \"n8n-nodes-base.splitInBatches\", \"typeVersion\": 1, \"position\": [300, 100]}, {\"parameters\": {\"fields\": {\"custom1\": \"={{ $json.title }}\", \"custom2\": \"={{ $json.description }}\"}}, \"name\": \"Set\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 1, \"position\": [500, 100]}, {\"parameters\": {\"sheetId\": \"1aBcD\", \"range\": \"A1\", \"mode\": \"append\"}, \"name\": \"Google Sheets\", \"type\": \"n8n-nodes-base.googleSheets\", \"typeVersion\": 1, \"position\": [700, 100]}, {\"parameters\": {\"trigger\": \"schedule\", \"interval\": \"hour\"}, \"name\": \"Schedule Trigger\", \"type\": \"n8n-nodes-base.scheduleTrigger\", \"typeVersion\": 1, \"position\": [-100, 100]}], \"connections\": {\"Loop Over Items\": {\"main\": [[{\"node\": \"Linear\", \"type\": \"main\", \"index\": 0}]]}, \"Set\": {\"main\": [[{\"node\": \"Google Sheets\", \"type\": \"main\", \"index\": 0}]]}, \"Google Sheets\": {\"main\": [[{\"node\": \"Loop Over Items\", \"type\": \"main\", \"index\": 0}]]}, \"Schedule Trigger\": {\"main\": [[{\"node\": \"Loop Over Items\", \"type\": \"main\", \"index\": 0}]]}, \"Linear\": {\"main\": [[{\"node\": \"Loop Over Items\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}", "1.0", "4.0", "1.0", "5.0", "5.0", "5.0", "5.0", "5.0", "5.0", "5.0", "5.0", "1.0", null], ["4", "{\"name\": \"Untitled Workflow\", \"nodes\": [{\"parameters\": {\"topic\": \"={{ $('Creation Form').item.json.title }}\", \"authentication\": \"oAuth2\", \"additionalFields\": {\"password\": \"={{ Math.random().toString(36).slice(-4); }}\", \"startTime\": \"={{ new Date(new Date($('Creation Form').item.json.date_start).getTime() + ($('Creation Form').item.json.hour * 3600000) + ($('Creation Form').item.json.minute * 60000)).toISOString() }}\"}}, \"name\": \"Create Zoom meeting\", \"type\": \"n8n-nodes-base.zoom\", \"typeVersion\": 1, \"position\": [180, 480]}, {\"parameters\": {\"url\": \"https://api.stripe.com/v1/products\", \"method\": \"POST\", \"options\": {}, \"sendBody\": true, \"contentType\": \"form-urlencoded\", \"authentication\": \"predefinedCredentialType\", \"bodyParameters\": {\"parameters\": [{\"name\": \"name\", \"value\": \"={{ $('Creation Form').item.json.title }}\"}, {\"name\": \"default_price_data[unit_amount]\", \"value\": \"={{ $('Creation Form').item.json.price * 100 }}\"}, {\"name\": \"default_price_data[currency]\", \"value\": \"={{ $('Config').item.json.currency }}\"}]}, \"nodeCredentialType\": \"stripeApi\"}, \"name\": \"Create Stripe Product\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 4.1, \"position\": [400, 480]}, {\"parameters\": {\"options\": {}, \"assignments\": {\"assignments\": [{\"id\": \"038b54b7-9559-444e-8653-c5256a5b784e\", \"name\": \"currency\", \"type\": \"string\", \"value\": \"EUR\"}, {\"id\": \"64d1eeee-cabe-403b-a634-f3238f586f58\", \"name\": \"sheet_url\", \"type\": \"string\", \"value\": \"https://docs.google.com/spreadsheets/d/1ZliqqBNo6X0iM9yXBOiCG1e4Q7L7bQKMFmjvbSgUSnA/edit#gid=0\"}, {\"id\": \"997fe5a1-f601-458d-899c-673dff4acb04\", \"name\": \"teacher_email\", \"type\": \"string\", \"value\": \"<EMAIL>\"}]}}, \"name\": \"Config\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 3.3, \"position\": [-220, 640]}, {\"parameters\": {\"sendTo\": \"={{ $('Config').item.json.teacher_email }}\", \"message\": \"=<b>Congratulations, your event has been succesfully created \\ud83c\\udf89</b><br/><br/>\\n\\nTitle: {{ $('Creation Form').item.json.title }}<br/>\\nPrice:  {{ $('Creation Form').item.json.price }} {{ $('Config').item.json.currency }}<br/>\\nStart date: {{ $('Creation Form').item.json.date_start }}<br/><br/>\\n\\n<b>Payment link:</b><br/>\\n {{ $('Create payment link').item.json.url }}<br/>\\n<i>Start sharing this link to get subscriptions</i><br/><br/>\\n<b>Participant list:</b><br/>\\n{{ $('Config').item.json.sheet_url }}#gid={{ $('Create Stripe Product').item.json.created }}\\n<br/><br/>\\n<b>Zoom infos:</b><br/>\\nLink: {{ $('Create Zoom meeting').item.json.join_url }}<br/>\\nSession ID: {{ $('Create Zoom meeting').item.json.id }}<br/>\\nPassword: {{ $('Create Zoom meeting').item.json.password }}<br/> \", \"options\": {}, \"subject\": \"=\\ud83c\\udf89 {{ $('Creation Form').item.json.title }} has been created!\"}, \"name\": \"Send email to teacher\", \"type\": \"n8n-nodes-base.gmail\", \"typeVersion\": 2.1, \"position\": [1040, 480]}, {\"parameters\": {\"title\": \"={{ $('Creation Form').item.json.date_start }} - {{ $('Creation Form').item.json.title }} - {{ $('Create Stripe Product').item.json.created }}\", \"options\": {\"index\": 0, \"sheetId\": \"={{ $('Create Stripe Product').item.json.created }}\"}, \"operation\": \"create\", \"documentId\": {\"__rl\": true, \"mode\": \"url\", \"value\": \"={{ $('Config').item.json.sheet_url }}\"}}, \"name\": \"Create participant list\", \"type\": \"n8n-nodes-base.googleSheets\", \"typeVersion\": 4.3, \"position\": [840, 480]}, {\"parameters\": {\"columns\": {\"value\": {}, \"schema\": [{\"id\": \"city\", \"type\": \"string\", \"display\": true, \"removed\": false, \"required\": false, \"displayName\": \"city\", \"defaultMatch\": false, \"canBeUsedToMatch\": true}, {\"id\": \"email\", \"type\": \"string\", \"display\": true, \"removed\": false, \"required\": false, \"displayName\": \"email\", \"defaultMatch\": false, \"canBeUsedToMatch\": true}, {\"id\": \"name\", \"type\": \"string\", \"display\": true, \"removed\": false, \"required\": false, \"displayName\": \"name\", \"defaultMatch\": false, \"canBeUsedToMatch\": true}, {\"id\": \"country\", \"type\": \"string\", \"display\": true, \"removed\": false, \"required\": false, \"displayName\": \"country\", \"defaultMatch\": false, \"canBeUsedToMatch\": true}, {\"id\": \"postal_code\", \"type\": \"string\", \"display\": true, \"removed\": false, \"required\": false, \"displayName\": \"postal_code\", \"defaultMatch\": false, \"canBeUsedToMatch\": true}, {\"id\": \"amount\", \"type\": \"string\", \"display\": true, \"removed\": false, \"required\": false, \"displayName\": \"amount\", \"defaultMatch\": false, \"canBeUsedToMatch\": true}, {\"id\": \"currency\", \"type\": \"string\", \"display\": true, \"removed\": false, \"required\": false, \"displayName\": \"currency\", \"defaultMatch\": false, \"canBeUsedToMatch\": true}], \"mappingMode\": \"autoMapInputData\", \"matchingColumns\": []}, \"options\": {}, \"operation\": \"append\", \"sheetName\": {\"__rl\": true, \"mode\": \"id\", \"value\": \"={{ $('On payment').item.json.data.object.metadata.event_sheet_id }}\"}, \"documentId\": {\"__rl\": true, \"mode\": \"url\", \"value\": \"={{ $('Config').item.json.sheet_url }}\"}}, \"name\": \"Add participant to list\", \"type\": \"n8n-nodes-base.googleSheets\", \"typeVersion\": 4.3, \"position\": [400, 800]}, {\"parameters\": {\"sendTo\": \"={{ $('On payment').item.json.data.object.customer_details.email }}\", \"message\": \"=Dear {{ $('On payment').item.json.data.object.customer_details.name }},<br/><br/>\\n\\nWe are very happy to announce that your subscription to our event <b>{{ $json.title }}</b> starting on <b>{{ $json.start }}</b> is now confirmed.<br/><br/>\\n\\nHere are the infos you will need to participate:<br/> \\nZoom link:  {{ $('On payment').item.json.data.object.metadata.zoom_link }}<br/>\\nZoom password:{{ $('On payment').item.json.data.object.metadata.zoom_password }}<br/>\\nZoom ID: {{ $('On payment').item.json.data.object.metadata.zoom_id }}<br/><br/> \\n\\nLooking forward to see you there!<br/>\\nKind regards<br/>\", \"options\": {\"appendAttribution\": false}, \"subject\": \"Than you for your subscription \\ud83d\\ude4f\"}, \"name\": \"Send confirmation to participant\", \"type\": \"n8n-nodes-base.gmail\", \"typeVersion\": 2.1, \"position\": [620, 800]}, {\"parameters\": {\"sendTo\": \"={{ $('Config').item.json.teacher_email }}\", \"message\": \"=<b>A new participant registred for the event {{ $('Retrieve event infos').item.json.title }} ({{ $('Retrieve event infos').item.json.start }})!</b><br/><br/>\\n\\n<b>Name: {{ $('On payment').item.json.data.object.customer_details.name }}</b><br/>\\n<b>Email: {{ $('On payment').item.json.data.object.customer_details.email }}</b><br/><br/>\\n\\n<b>Participant list:</b><br/>\\n{{ $('Config').item.json.sheet_url }}#gid={{ $('On payment').item.json.data.object.metadata.event_sheet_id }} \", \"options\": {}, \"subject\": \"New participant registred \\u261d\\ufe0f\"}, \"name\": \"Notify teacher\", \"type\": \"n8n-nodes-base.gmail\", \"typeVersion\": 2.1, \"position\": [840, 800]}, {\"parameters\": {\"url\": \"https://api.stripe.com/v1/payment_links\", \"method\": \"POST\", \"options\": {}, \"sendBody\": true, \"contentType\": \"form-urlencoded\", \"authentication\": \"predefinedCredentialType\", \"bodyParameters\": {\"parameters\": [{\"name\": \"line_items[0][price]\", \"value\": \"={{ $json.default_price }}\"}, {\"name\": \"line_items[0][quantity]\", \"value\": \"1\"}, {\"name\": \"metadata[event_sheet_id]\", \"value\": \"={{ $('Create Stripe Product').item.json.created }}\"}, {\"name\": \"metadata[zoom_link]\", \"value\": \"={{ $('Create Zoom meeting').item.json.join_url }}\"}, {\"name\": \"metadata[zoom_password]\", \"value\": \"={{ $('Create Zoom meeting').item.json.password }}\"}, {\"name\": \"metadata[zoom_id]\", \"value\": \"={{ $('Create Zoom meeting').item.json.id }}\"}, {\"name\": \"metadata[title]\", \"value\": \"={{ $('Creation Form').item.json.title }}\"}, {\"name\": \"metadata[start_time]\", \"value\": \"={{ $('Create Zoom meeting').item.json.start_time }}\"}, {\"name\": \"metadata[price]\", \"value\": \"={{ $('Creation Form').item.json.price }}\"}, {\"name\": \"metadata[currency]\", \"value\": \"={{ $('Config').item.json.currency }}\"}]}, \"nodeCredentialType\": \"stripeApi\"}, \"name\": \"Create payment link\", \"type\": \"n8n-nodes-base.httpRequest\", \"typeVersion\": 4.1, \"position\": [620, 480]}, {\"parameters\": {\"options\": {}, \"assignments\": {\"assignments\": [{\"id\": \"dabd3bc2-ca92-4d99-a223-b0ad18945121\", \"name\": \"email\", \"type\": \"string\", \"value\": \"={{ $('On payment').item.json.data.object.customer_details.email }}\"}, {\"id\": \"d40709f6-ffcd-4055-a374-9044a9a5e3b2\", \"name\": \"name\", \"type\": \"string\", \"value\": \"={{ $('On payment').item.json.data.object.customer_details.name }}\"}]}}, \"name\": \"Format participant\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 3.3, \"position\": [180, 800]}, {\"parameters\": {\"options\": {}, \"assignments\": {\"assignments\": [{\"id\": \"a29943ba-b516-41a8-8f85-5bcee5eda0d1\", \"name\": \"title\", \"type\": \"string\", \"value\": \"={{ $('Creation Form').item.json.title }}\"}, {\"id\": \"bf642fde-c4c2-42b4-beed-ef65efdab55b\", \"name\": \"start\", \"type\": \"string\", \"value\": \"={{ $('Creation Form').item.json.date_start }}\"}, {\"id\": \"33f7a58e-624d-4ccc-bbea-ed3365cede20\", \"name\": \"price\", \"type\": \"number\", \"value\": \"={{ $('Creation Form').item.json.price }}\"}, {\"id\": \"c948f71e-3b12-4c6a-a1f9-ee9a511fe262\", \"name\": \"currency\", \"type\": \"string\", \"value\": \"={{ $('Config').item.json.currency }}\"}, {\"id\": \"887461ca-db0d-442e-8008-5fe6a6fbdd8f\", \"name\": \"zoom_link\", \"type\": \"string\", \"value\": \"={{ $('Create Zoom meeting').item.json.join_url }}\"}, {\"id\": \"4b2bd5e2-3bd5-443a-94a3-9ababfd9d881\", \"name\": \"zoom_id\", \"type\": \"string\", \"value\": \"={{ $('Create Zoom meeting').item.json.id }}\"}, {\"id\": \"a1cea8e2-9954-4143-b71f-5ea194a873dd\", \"name\": \"zoom_password\", \"type\": \"string\", \"value\": \"={{ $('Create Zoom meeting').item.json.password }}\"}, {\"id\": \"faa52bc6-dfbe-49e2-bc95-dae198a61293\", \"name\": \"payment_link\", \"type\": \"string\", \"value\": \"={{ $json.url }}\"}, {\"id\": \"d7f5f0f5-cc7b-436a-9ad1-0b8f410c62c6\", \"name\": \"payment_id\", \"type\": \"string\", \"value\": \"={{ $json.id }}\"}, {\"id\": \"020b22d0-f525-4120-9f8b-2fa33e88c2e1\", \"name\": \"event_sheet_id\", \"type\": \"string\", \"value\": \"={{ $json.metadata.event_sheet_id }}\"}]}}, \"name\": \"Format event\", \"type\": \"n8n-nodes-base.set\", \"typeVersion\": 3.3, \"position\": [840, 280]}, {\"parameters\": {\"columns\": {\"value\": {}, \"schema\": [], \"mappingMode\": \"autoMapInputData\", \"matchingColumns\": []}, \"options\": {}, \"operation\": \"append\", \"sheetName\": {\"__rl\": true, \"mode\": \"id\", \"value\": \"0\"}, \"documentId\": {\"__rl\": true, \"mode\": \"url\", \"value\": \"={{ $('Config').item.json.sheet_url }}\"}}, \"name\": \"Store event\", \"type\": \"n8n-nodes-base.googleSheets\", \"typeVersion\": 4.3, \"position\": [1040, 280]}, {\"parameters\": {\"path\": \"1c6fe52c-48ab-4688-b5ae-7e24361aa602\", \"options\": {}, \"formTitle\": \"Create a new meeting\", \"formFields\": {\"values\": [{\"fieldLabel\": \"title\", \"requiredField\": true}, {\"fieldType\": \"number\", \"fieldLabel\": \"price\", \"requiredField\": true}, {\"fieldType\": \"date\", \"fieldLabel\": \"date_start\", \"requiredField\": true}, {\"fieldType\": \"number\", \"fieldLabel\": \"hour\"}, {\"fieldType\": \"number\", \"fieldLabel\": \"minute\"}]}, \"responseMode\": \"lastNode\", \"formDescription\": \"This automates the creation of a Zoom Meeting and a Stripe Payment page, streamlining your event setup process.\"}, \"name\": \"Creation Form\", \"type\": \"n8n-nodes-base.formTrigger\", \"typeVersion\": 2, \"position\": [-500, 480]}, {\"parameters\": {\"events\": [\"checkout.session.completed\"]}, \"name\": \"On payment\", \"type\": \"n8n-nodes-base.stripeTrigger\", \"typeVersion\": 1, \"position\": [-500, 780]}, {\"parameters\": {\"color\": 6, \"width\": 275.01592825011585, \"height\": 468.76027109756643, \"content\": \"# Setup\\n### 1/ Add Your credentials\\n[Zoom](https://docs.n8n.io/integrations/builtin/credentials/zoom/)\\n[Google](https://docs.n8n.io/integrations/builtin/credentials/google/)\\n[Stripe](https://docs.n8n.io/integrations/builtin/credentials/stripe/)\\n\\nNote: For Google, you need to add Gmail and Google Sheet.\\n\\n### 2/ Create a [new Google Sheet](https://sheets.new/).\\nKeep this sheet blank for now; it contains your meeting and participant information. Place it wherever it fits best in your organization.\\n\\n### 3/ And fill the config node\\n# \\ud83d\\udc47\"}, \"name\": \"Sticky Note1\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [-220, 118]}, {\"parameters\": {\"color\": 6, \"width\": 372, \"height\": 200.14793114506386, \"content\": \"# Create a meeting \\ud83d\\udc49\\ud83c\\udffb\\n\\nYour journey to easy event management starts here.\\n\\nClick this node, copy the production URL, and keep it handy. It's your personal admin tool for quickly creating new meetings. Simple and efficient!\"}, \"name\": \"Sticky Note2\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [-920, 500]}, {\"parameters\": {\"color\": 6, \"width\": 519.9859025074911, \"height\": 106.11515926602786, \"content\": \"# \\ud83d\\udd8b\\ufe0f Customize\\n### Feel free to adapt email contents to your needs.\"}, \"name\": \"Sticky Note3\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [620, 660]}, {\"parameters\": {\"options\": {\"looseTypeValidation\": true}, \"conditions\": {\"options\": {\"leftValue\": \"\", \"caseSensitive\": true, \"typeValidation\": \"loose\"}, \"combinator\": \"and\", \"conditions\": [{\"id\": \"40ddf809-1602-4120-ae7e-8be61437b50d\", \"operator\": {\"type\": \"boolean\", \"operation\": \"true\", \"singleValue\": true}, \"leftValue\": \"={{ $(\\\"Creation Form\\\").isExecuted }}\", \"rightValue\": \"\"}]}}, \"name\": \"if is creation flow\", \"type\": \"n8n-nodes-base.if\", \"typeVersion\": 2, \"position\": [-20, 640]}, {\"parameters\": {\"color\": 7, \"width\": 202.64787116404852, \"height\": 85.79488430601403, \"content\": \"### Crafted by the\\n## [\\ud83e\\udd77 n8n.ninja](https://n8n.ninja)\"}, \"name\": \"Sticky Note\", \"type\": \"n8n-nodes-base.stickyNote\", \"typeVersion\": 1, \"position\": [180, 340]}, {\"parameters\": {}, \"name\": \"the end\", \"type\": \"n8n-nodes-base.noOp\", \"typeVersion\": 1, \"position\": [1040, 800]}], \"connections\": {\"Config\": {\"main\": [[{\"node\": \"if is creation flow\", \"type\": \"main\", \"index\": 0}]]}, \"On payment\": {\"main\": [[{\"node\": \"Config\", \"type\": \"main\", \"index\": 0}]]}, \"Format event\": {\"main\": [[{\"node\": \"Store event\", \"type\": \"main\", \"index\": 0}]]}, \"Creation Form\": {\"main\": [[{\"node\": \"Config\", \"type\": \"main\", \"index\": 0}]]}, \"Notify teacher\": {\"main\": [[{\"node\": \"the end\", \"type\": \"main\", \"index\": 0}]]}, \"Format participant\": {\"main\": [[{\"node\": \"Add participant to list\", \"type\": \"main\", \"index\": 0}]]}, \"Create Zoom meeting\": {\"main\": [[{\"node\": \"Create Stripe Product\", \"type\": \"main\", \"index\": 0}]]}, \"Create payment link\": {\"main\": [[{\"node\": \"Create participant list\", \"type\": \"main\", \"index\": 0}, {\"node\": \"Format event\", \"type\": \"main\", \"index\": 0}]]}, \"if is creation flow\": {\"main\": [[{\"node\": \"Create Zoom meeting\", \"type\": \"main\", \"index\": 0}], [{\"node\": \"Format participant\", \"type\": \"main\", \"index\": 0}]]}, \"Create Stripe Product\": {\"main\": [[{\"node\": \"Create payment link\", \"type\": \"main\", \"index\": 0}]]}, \"Add participant to list\": {\"main\": [[{\"node\": \"Send confirmation to participant\", \"type\": \"main\", \"index\": 0}]]}, \"Create participant list\": {\"main\": [[{\"node\": \"Send email to teacher\", \"type\": \"main\", \"index\": 0}]]}, \"Send confirmation to participant\": {\"main\": [[{\"node\": \"Notify teacher\", \"type\": \"main\", \"index\": 0}]]}}, \"active\": false}", "", null, null, null, null, null, null, null, null, null, null, null, null, "{'error': 'Invalid JSON'}"]], "shape": {"columns": 15, "rows": 5}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ground_truth_json</th>\n", "      <th>generated_json</th>\n", "      <th>json_parsed</th>\n", "      <th>top_level_keys_present</th>\n", "      <th>workflow_name_valid</th>\n", "      <th>num_nodes</th>\n", "      <th>num_nodes_with_valid_type</th>\n", "      <th>num_nodes_with_valid_version</th>\n", "      <th>num_nodes_with_valid_structure</th>\n", "      <th>num_nodes_with_parameters</th>\n", "      <th>num_connections</th>\n", "      <th>num_connections_with_valid_structure</th>\n", "      <th>num_connections_with_valid_target_node</th>\n", "      <th>active_field_boolean</th>\n", "      <th>evaluation_results</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>{\"name\": \"Get Reactions Telegram\", \"nodes\": [{...</td>\n", "      <td>{\"name\": \"Telegram Reaction Extractor\", \"nodes...</td>\n", "      <td>1.0</td>\n", "      <td>4.0</td>\n", "      <td>1.0</td>\n", "      <td>7.0</td>\n", "      <td>6.0</td>\n", "      <td>6.0</td>\n", "      <td>7.0</td>\n", "      <td>7.0</td>\n", "      <td>7.0</td>\n", "      <td>7.0</td>\n", "      <td>7.0</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>{\"name\": \"AI Intern Test - YouTube Analysis\", ...</td>\n", "      <td></td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>{'error': 'Invalid JSON'}</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>{\"name\": \"open_node_creator_for_connection\", \"...</td>\n", "      <td>{\"name\": \"My workflow\", \"nodes\": [{\"parameters...</td>\n", "      <td>1.0</td>\n", "      <td>4.0</td>\n", "      <td>1.0</td>\n", "      <td>7.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>7.0</td>\n", "      <td>7.0</td>\n", "      <td>6.0</td>\n", "      <td>6.0</td>\n", "      <td>6.0</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>{\"name\": \"Untitled Workflow\", \"nodes\": [{\"para...</td>\n", "      <td>{\"name\": \"Get Tickets from Linear\", \"nodes\": [...</td>\n", "      <td>1.0</td>\n", "      <td>4.0</td>\n", "      <td>1.0</td>\n", "      <td>5.0</td>\n", "      <td>5.0</td>\n", "      <td>5.0</td>\n", "      <td>5.0</td>\n", "      <td>5.0</td>\n", "      <td>5.0</td>\n", "      <td>5.0</td>\n", "      <td>5.0</td>\n", "      <td>1.0</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>{\"name\": \"Untitled Workflow\", \"nodes\": [{\"para...</td>\n", "      <td></td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>{'error': 'Invalid JSON'}</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                   ground_truth_json  \\\n", "0  {\"name\": \"Get Reactions Telegram\", \"nodes\": [{...   \n", "1  {\"name\": \"AI Intern Test - YouTube Analysis\", ...   \n", "2  {\"name\": \"open_node_creator_for_connection\", \"...   \n", "3  {\"name\": \"Untitled Workflow\", \"nodes\": [{\"para...   \n", "4  {\"name\": \"Untitled Workflow\", \"nodes\": [{\"para...   \n", "\n", "                                      generated_json  json_parsed  \\\n", "0  {\"name\": \"Telegram Reaction Extractor\", \"nodes...          1.0   \n", "1                                                             NaN   \n", "2  {\"name\": \"My workflow\", \"nodes\": [{\"parameters...          1.0   \n", "3  {\"name\": \"Get Tickets from Linear\", \"nodes\": [...          1.0   \n", "4                                                             NaN   \n", "\n", "   top_level_keys_present  workflow_name_valid  num_nodes  \\\n", "0                     4.0                  1.0        7.0   \n", "1                     NaN                  NaN        NaN   \n", "2                     4.0                  1.0        7.0   \n", "3                     4.0                  1.0        5.0   \n", "4                     NaN                  NaN        NaN   \n", "\n", "   num_nodes_with_valid_type  num_nodes_with_valid_version  \\\n", "0                        6.0                           6.0   \n", "1                        NaN                           NaN   \n", "2                        0.0                           0.0   \n", "3                        5.0                           5.0   \n", "4                        NaN                           NaN   \n", "\n", "   num_nodes_with_valid_structure  num_nodes_with_parameters  num_connections  \\\n", "0                             7.0                        7.0              7.0   \n", "1                             NaN                        NaN              NaN   \n", "2                             7.0                        7.0              6.0   \n", "3                             5.0                        5.0              5.0   \n", "4                             NaN                        NaN              NaN   \n", "\n", "   num_connections_with_valid_structure  \\\n", "0                                   7.0   \n", "1                                   NaN   \n", "2                                   6.0   \n", "3                                   5.0   \n", "4                                   NaN   \n", "\n", "   num_connections_with_valid_target_node  active_field_boolean  \\\n", "0                                     7.0                   1.0   \n", "1                                     NaN                   NaN   \n", "2                                     6.0                   1.0   \n", "3                                     5.0                   1.0   \n", "4                                     NaN                   NaN   \n", "\n", "          evaluation_results  \n", "0                        NaN  \n", "1  {'error': 'Invalid JSON'}  \n", "2                        NaN  \n", "3                        NaN  \n", "4  {'error': 'Invalid JSON'}  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": 6, "id": "f9419ccf", "metadata": {}, "outputs": [], "source": ["df[\"percent_node_with_valid_type\"] = df[\"num_nodes_with_valid_type\"] / df[\"num_nodes\"] * 100\n", "df[\"percent_node_with_valid_version\"] = df[\"num_nodes_with_valid_version\"] / df[\"num_nodes\"] * 100\n", "df[\"percent_node_with_valid_structure\"] = df[\"num_nodes_with_valid_structure\"] / df[\"num_nodes\"] * 100\n", "df[\"percent_connections_with_valid_structure\"] = df[\"num_connections_with_valid_structure\"] / df[\"num_connections\"] * 100\n", "df[\"percent_connections_with_valid_target_node\"] = df[\"num_connections_with_valid_target_node\"] / df[\"num_connections\"] * 100\n", "df[\"percent_node_with_parameters\"] = df[\"num_nodes_with_parameters\"] / df[\"num_nodes\"] * 100"]}, {"cell_type": "code", "execution_count": 7, "id": "c1ecdd0c", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='top_level_keys_present', ylabel='Count'>"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"top_level_keys_present\")"]}, {"cell_type": "code", "execution_count": 8, "id": "3b2bfd01", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='workflow_name_valid', ylabel='Count'>"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"workflow_name_valid\")"]}, {"cell_type": "code", "execution_count": 9, "id": "a18db11e", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_type', ylabel='Count'>"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_type\")"]}, {"cell_type": "code", "execution_count": 10, "id": "3c7c398e", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_version', ylabel='Count'>"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_version\")"]}, {"cell_type": "code", "execution_count": 11, "id": "4c46bdcf", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_structure', ylabel='Count'>"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_structure\")"]}, {"cell_type": "code", "execution_count": 12, "id": "44883d67", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_parameters', ylabel='Count'>"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_parameters\")"]}, {"cell_type": "code", "execution_count": 13, "id": "048db9e6", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_connections_with_valid_structure', ylabel='Count'>"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAjMAAAGxCAYAAACXwjeMAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjAsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvlHJYcgAAAAlwSFlzAAAPYQAAD2EBqD+naQAAMw9JREFUeJzt3XtcVVX+//H3EeGIChSpXJQAr3kpKzWTLNCUosk0m242pd8mpwIts6aJsRJtki7fHJss+9ajL11Nu1iZOipWoGWakpQaoiUoUxBpKmgGAev3Rz/O1yNXCThn2ev5eOzHw73W3vt81uIcz5t99mE7jDFGAAAAlmrj6QIAAAB+C8IMAACwGmEGAABYjTADAACsRpgBAABWI8wAAACrEWYAAIDVCDMAAMBqbT1dQEurqqrSd999p4CAADkcDk+XAwAAGsEYo9LSUoWHh6tNm/rPvZz0Yea7775TRESEp8sAAABNUFBQoG7dutW7zUkfZgICAiT9OhmBgYEergYAADRGSUmJIiIiXO/j9Tnpw0z1R0uBgYGEGQAALNOYS0S4ABgAAFiNMAMAAKxGmAEAAFYjzAAAAKsRZgAAgNUIMwAAwGqEGQAAYDXCDAAAsBphBgAAWI0wAwAArEaYAQAAViPMAAAAqxFmAACA1QgzAADAam09XYDt9u7dq3379nm6DAAAPKJTp046/fTTPVoDYeY32Lt3r844o6+OHv3J06UAAOAR/v7ttWNHjkcDDWHmN9i3b5+OHv1JQ2+eqcCwKE+XAwBAqyopzNfG/52lffv2EWZsFxgWpeDT+3i6DAAAfpe4ABgAAFiNMAMAAKxGmAEAAFYjzAAAAKsRZgAAgNUIMwAAwGqEGQAAYDXCDAAAsBphBgAAWI0wAwAArEaYAQAAViPMAAAAqxFmAACA1QgzAADAaoQZAABgNcIMAACwGmEGAABYjTADAACsRpgBAABWI8wAAACrEWYAAIDVCDMAAMBqhBkAAGA1wgwAALCaR8PMggULdNZZZykwMFCBgYEaNmyY/v3vf7v6jTFKSUlReHi4/P39FRcXp+3bt3uwYgAA4G08Gma6deumRx55RJs3b9bmzZs1cuRIjR071hVYHnvsMc2dO1fz58/Xpk2bFBoaqtGjR6u0tNSTZQMAAC/i0TAzZswYXXbZZerdu7d69+6thx9+WB07dtSGDRtkjNG8efM0Y8YMjR8/XgMGDNBLL72kn376SQsXLvRk2QAAwIt4zTUzlZWVWrRokY4cOaJhw4YpLy9PRUVFio+Pd23jdDoVGxur9evXe7BSAADgTdp6uoCtW7dq2LBh+vnnn9WxY0e988476tevnyuwhISEuG0fEhKiPXv21Hm8srIylZWVudZLSkpapnAAAOAVPH5mpk+fPsrOztaGDRt0++23a+LEifrqq69c/Q6Hw217Y0yNtmOlpqYqKCjItURERLRY7QAAwPM8Hmb8/PzUs2dPDR48WKmpqRo4cKCefPJJhYaGSpKKiorcti8uLq5xtuZYycnJOnTokGspKCho0foBAIBneTzMHM8Yo7KyMkVHRys0NFTp6emuvvLycmVmZiomJqbO/Z1Op+ur3tULAAA4eXn0mpm///3vSkhIUEREhEpLS7Vo0SJlZGRo5cqVcjgcmjZtmubMmaNevXqpV69emjNnjtq3b68JEyZ4smwAAOBFPBpmvv/+e914440qLCxUUFCQzjrrLK1cuVKjR4+WJN177706evSoEhMTdeDAAQ0dOlSrV69WQECAJ8sGAABexKNh5oUXXqi33+FwKCUlRSkpKa1TEAAAsI7XXTMDAABwIggzAADAaoQZAABgNcIMAACwGmEGAABYjTADAACsRpgBAABWI8wAAACrEWYAAIDVCDMAAMBqhBkAAGA1wgwAALAaYQYAAFiNMAMAAKxGmAEAAFYjzAAAAKsRZgAAgNUIMwAAwGqEGQAAYDXCDAAAsBphBgAAWI0wAwAArEaYAQAAViPMAAAAqxFmAACA1QgzAADAaoQZAABgNcIMAACwGmEGAABYjTADAACsRpgBAABWI8wAAACrEWYAAIDVCDMAAMBqhBkAAGA1wgwAALAaYQYAAFiNMAMAAKxGmAEAAFYjzAAAAKsRZgAAgNUIMwAAwGoeDTOpqakaMmSIAgIC1KVLF40bN065ublu20yaNEkOh8NtOf/88z1UMQAA8DYeDTOZmZlKSkrShg0blJ6eroqKCsXHx+vIkSNu21166aUqLCx0LStWrPBQxQAAwNu09eSDr1y50m09LS1NXbp0UVZWli666CJXu9PpVGhoaGuXBwAALOBV18wcOnRIkhQcHOzWnpGRoS5duqh3796aPHmyiouLPVEeAADwQh49M3MsY4ymT5+u4cOHa8CAAa72hIQEXX311YqMjFReXp4eeOABjRw5UllZWXI6nTWOU1ZWprKyMtd6SUlJq9QPAAA8w2vCzJQpU/Tll1/q448/dmu/9tprXf8eMGCABg8erMjISC1fvlzjx4+vcZzU1FTNmjWrxesFAADewSs+Zpo6daqWLl2qjz76SN26dat327CwMEVGRmrXrl219icnJ+vQoUOupaCgoCVKBgAAXsKjZ2aMMZo6dareeecdZWRkKDo6usF99u/fr4KCAoWFhdXa73Q6a/34CQAAnJw8emYmKSlJr776qhYuXKiAgAAVFRWpqKhIR48elSQdPnxY99xzjz799FPl5+crIyNDY8aMUadOnXTllVd6snQAAOAlPHpmZsGCBZKkuLg4t/a0tDRNmjRJPj4+2rp1q15++WUdPHhQYWFhGjFihBYvXqyAgAAPVAwAALyNxz9mqo+/v79WrVrVStUAAAAbecUFwAAAAE1FmAEAAFYjzAAAAKsRZgAAgNUIMwAAwGqEGQAAYDXCDAAAsBphBgAAWI0wAwAArEaYAQAAViPMAAAAqxFmAACA1QgzAADAaoQZAABgNcIMAACwGmEGAABYjTADAACsRpgBAABWI8wAAACrEWYAAIDVCDMAAMBqhBkAAGA1wgwAALAaYQYAAFiNMAMAAKxGmAEAAFYjzAAAAKsRZgAAgNUIMwAAwGqEGQAAYDXCDAAAsBphBgAAWI0wAwAArEaYAQAAViPMAAAAqxFmAACA1QgzAADAaoQZAABgNcIMAACwGmEGAABYjTADAACsRpgBAABWI8wAAACreTTMpKamasiQIQoICFCXLl00btw45ebmum1jjFFKSorCw8Pl7++vuLg4bd++3UMVAwAAb+PRMJOZmamkpCRt2LBB6enpqqioUHx8vI4cOeLa5rHHHtPcuXM1f/58bdq0SaGhoRo9erRKS0s9WDkAAPAWbT354CtXrnRbT0tLU5cuXZSVlaWLLrpIxhjNmzdPM2bM0Pjx4yVJL730kkJCQrRw4ULdeuutnigbAAB4Ea+6ZubQoUOSpODgYElSXl6eioqKFB8f79rG6XQqNjZW69evr/UYZWVlKikpcVsAAMDJy2vCjDFG06dP1/DhwzVgwABJUlFRkSQpJCTEbduQkBBX3/FSU1MVFBTkWiIiIlq2cAAA4FFeE2amTJmiL7/8Uq+//nqNPofD4bZujKnRVi05OVmHDh1yLQUFBS1SLwAA8A4evWam2tSpU7V06VKtXbtW3bp1c7WHhoZK+vUMTVhYmKu9uLi4xtmaak6nU06ns2ULBgAAXsOjZ2aMMZoyZYqWLFmiDz/8UNHR0W790dHRCg0NVXp6uqutvLxcmZmZiomJae1yAQCAF/LomZmkpCQtXLhQ7733ngICAlzXwQQFBcnf318Oh0PTpk3TnDlz1KtXL/Xq1Utz5sxR+/btNWHCBE+WDgAAvIRHw8yCBQskSXFxcW7taWlpmjRpkiTp3nvv1dGjR5WYmKgDBw5o6NChWr16tQICAlq5WgAA4I08GmaMMQ1u43A4lJKSopSUlJYvCAAAWMdrvs0EAADQFIQZAABgNcIMAACwWpPCTPfu3bV///4a7QcPHlT37t1/c1EAAACN1aQwk5+fr8rKyhrtZWVl+vbbb39zUQAAAI11Qt9mWrp0qevfq1atUlBQkGu9srJSH3zwgaKiopqtOAAAgIacUJgZN26cpF+/Lj1x4kS3Pl9fX0VFRemJJ55otuIAAAAackJhpqqqStKvtxnYtGmTOnXq1CJFAQAANFaT/mheXl5ec9cBAADQJE3+C8AffPCBPvjgAxUXF7vO2FT73//9399cGAAAQGM0KczMmjVLs2fP1uDBgxUWFiaHw9HcdQEAADRKk8LMs88+qxdffFE33nhjc9cDAABwQpr0d2bKy8sVExPT3LUAAACcsCaFmVtuuUULFy5s7loAAABOWJM+Zvr555/13HPPac2aNTrrrLPk6+vr1j937txmKQ4AAKAhTQozX375pc4++2xJ0rZt29z6uBgYAAC0piaFmY8++qi56wAAAGiSJl0zAwAA4C2adGZmxIgR9X6c9OGHHza5IAAAgBPRpDBTfb1MtV9++UXZ2dnatm1bjRtQAgAAtKQmhZl//vOftbanpKTo8OHDv6kgAACAE9Gs18z86U9/4r5MAACgVTVrmPn000/Vrl275jwkAABAvZr0MdP48ePd1o0xKiws1ObNm/XAAw80S2EAAACN0aQwExQU5Lbepk0b9enTR7Nnz1Z8fHyzFAYAANAYTQozaWlpzV0HAABAkzQpzFTLyspSTk6OHA6H+vXrp3POOae56gIAAGiUJoWZ4uJiXXfddcrIyNApp5wiY4wOHTqkESNGaNGiRercuXNz1wkAAFCrJn2baerUqSopKdH27dv1448/6sCBA9q2bZtKSkp0xx13NHeNAAAAdWrSmZmVK1dqzZo16tu3r6utX79+evrpp7kAGAAAtKomnZmpqqqSr69vjXZfX19VVVX95qIAAAAaq0lhZuTIkbrzzjv13Xffudq+/fZb3XXXXbr44oubrTgAAICGNCnMzJ8/X6WlpYqKilKPHj3Us2dPRUdHq7S0VE899VRz1wgAAFCnJl0zExERoc8//1zp6enasWOHjDHq16+fRo0a1dz1AQAA1OuEzsx8+OGH6tevn0pKSiRJo0eP1tSpU3XHHXdoyJAh6t+/v9atW9cihQIAANTmhMLMvHnzNHnyZAUGBtboCwoK0q233qq5c+c2W3EAAAANOaEw88UXX+jSSy+tsz8+Pl5ZWVm/uSgAAIDGOqEw8/3339f6lexqbdu21Q8//PCbiwIAAGisEwozXbt21datW+vs//LLLxUWFvabiwIAAGisEwozl112mR588EH9/PPPNfqOHj2qmTNn6vLLL2+24gAAABpyQl/Nvv/++7VkyRL17t1bU6ZMUZ8+feRwOJSTk6Onn35alZWVmjFjRkvVCgAAUMMJhZmQkBCtX79et99+u5KTk2WMkSQ5HA5dcskleuaZZxQSEtIihQIAANTmhP8CcGRkpFasWKF9+/Zp48aN2rBhg/bt26cVK1YoKirqhI61du1ajRkzRuHh4XI4HHr33Xfd+idNmiSHw+G2nH/++SdaMgAAOIk16S8AS9Kpp56qIUOG/KYHP3LkiAYOHKj/+q//0lVXXVXrNpdeeqnS0tJc635+fr/pMQEAwMmlyWGmOSQkJCghIaHebZxOp0JDQ1upIgAAYJsm3WiyNWVkZKhLly7q3bu3Jk+erOLiYk+XBAAAvIhHz8w0JCEhQVdffbUiIyOVl5enBx54QCNHjlRWVpacTmet+5SVlamsrMy1Xn0fKQAAcHLy6jBz7bXXuv49YMAADR48WJGRkVq+fLnGjx9f6z6pqamaNWtWa5UIAAA8zOs/ZjpWWFiYIiMjtWvXrjq3SU5O1qFDh1xLQUFBK1YIAABam1efmTne/v37VVBQUO8tE5xOZ50fQQEAgJOPR8PM4cOH9fXXX7vW8/LylJ2dreDgYAUHByslJUVXXXWVwsLClJ+fr7///e/q1KmTrrzySg9WDQAAvIlHw8zmzZs1YsQI1/r06dMlSRMnTtSCBQu0detWvfzyyzp48KDCwsI0YsQILV68WAEBAZ4qGQAAeBmPhpm4uDjXLRFqs2rVqlasBgAA2MiqC4ABAACOR5gBAABWI8wAAACrEWYAAIDVCDMAAMBqhBkAAGA1wgwAALAaYQYAAFiNMAMAAKxGmAEAAFYjzAAAAKsRZgAAgNUIMwAAwGqEGQAAYDXCDAAAsBphBgAAWI0wAwAArEaYAQAAViPMAAAAqxFmAACA1QgzAADAaoQZAABgNcIMAACwGmEGAABYjTADAACsRpgBAABWI8wAAACrEWYAAIDVCDMAAMBqhBkAAGA1wgwAALAaYQYAAFiNMAMAAKxGmAEAAFYjzAAAAKsRZgAAgNUIMwAAwGqEGQAAYDXCDAAAsBphBgAAWI0wAwAArEaYAQAAVvNomFm7dq3GjBmj8PBwORwOvfvuu279xhilpKQoPDxc/v7+iouL0/bt2z1TLAAA8EoeDTNHjhzRwIEDNX/+/Fr7H3vsMc2dO1fz58/Xpk2bFBoaqtGjR6u0tLSVKwUAAN6qrScfPCEhQQkJCbX2GWM0b948zZgxQ+PHj5ckvfTSSwoJCdHChQt16623tmapAADAS3ntNTN5eXkqKipSfHy8q83pdCo2Nlbr16/3YGUAAMCbePTMTH2KiookSSEhIW7tISEh2rNnT537lZWVqayszLVeUlLSMgUCAACv4LVnZqo5HA63dWNMjbZjpaamKigoyLVERES0dIkAAMCDvDbMhIaGSvq/MzTViouLa5ytOVZycrIOHTrkWgoKClq0TgAA4FleG2aio6MVGhqq9PR0V1t5ebkyMzMVExNT535Op1OBgYFuCwAAOHl59JqZw4cP6+uvv3at5+XlKTs7W8HBwTr99NM1bdo0zZkzR7169VKvXr00Z84ctW/fXhMmTPBg1QAAwJt4NMxs3rxZI0aMcK1Pnz5dkjRx4kS9+OKLuvfee3X06FElJibqwIEDGjp0qFavXq2AgABPlQwAALyMR8NMXFycjDF19jscDqWkpCglJaX1igIAAFbx2mtmAAAAGoMwAwAArEaYAQAAViPMAAAAqxFmAACA1QgzAADAaoQZAABgNcIMAACwGmEGAABYjTADAACsRpgBAABWI8wAAACrEWYAAIDVCDMAAMBqhBkAAGA1wgwAALAaYQYAAFiNMAMAAKxGmAEAAFYjzAAAAKsRZgAAgNUIMwAAwGqEGQAAYDXCDAAAsBphBgAAWI0wAwAArEaYAQAAViPMAAAAqxFmAACA1QgzAADAaoQZAABgNcIMAACwGmEGAABYjTADAACsRpgBAABWI8wAAACrEWYAAIDVCDMAAMBqhBkAAGA1wgwAALAaYQYAAFiNMAMAAKxGmAEAAFbz6jCTkpIih8PhtoSGhnq6LAAA4EXaerqAhvTv319r1qxxrfv4+HiwGgAA4G28Psy0bduWszEAAKBOXv0xkyTt2rVL4eHhio6O1nXXXafdu3fXu31ZWZlKSkrcFgAAcPLy6jAzdOhQvfzyy1q1apWef/55FRUVKSYmRvv3769zn9TUVAUFBbmWiIiIVqwYAAC0Nq8OMwkJCbrqqqt05plnatSoUVq+fLkk6aWXXqpzn+TkZB06dMi1FBQUtFa5AADAA7z+mpljdejQQWeeeaZ27dpV5zZOp1NOp7MVqwIAAJ7k1WdmjldWVqacnByFhYV5uhQAAOAlvDrM3HPPPcrMzFReXp42btyoP/7xjyopKdHEiRM9XRoAAPASXv0x03/+8x9df/312rdvnzp37qzzzz9fGzZsUGRkpKdLAwAAXsKrw8yiRYs8XQIAAPByXv0xEwAAQEMIMwAAwGqEGQAAYDXCDAAAsBphBgAAWI0wAwAArEaYAQAAViPMAAAAqxFmAACA1QgzAADAaoQZAABgNcIMAACwGmEGAABYjTADAACsRpgBAABWI8wAAACrEWYAAIDVCDMAAMBqhBkAAGA1wgwAALAaYQYAAFiNMAMAAKxGmAEAAFYjzAAAAKsRZgAAgNUIMwAAwGqEGQAAYDXCDAAAsBphBgAAWI0wAwAArEaYAQAAViPMAAAAqxFmAACA1QgzAADAaoQZAABgNcIMAACwGmEGAABYjTADAACsRpgBAABWI8wAAACrEWYAAIDVCDMAAMBqVoSZZ555RtHR0WrXrp0GDRqkdevWebokAADgJbw+zCxevFjTpk3TjBkztGXLFl144YVKSEjQ3r17PV0aAADwAl4fZubOnas///nPuuWWW9S3b1/NmzdPERERWrBggadLAwAAXsCrw0x5ebmysrIUHx/v1h4fH6/169d7qCoAAOBN2nq6gPrs27dPlZWVCgkJcWsPCQlRUVFRrfuUlZWprKzMtX7o0CFJUklJSbPXd/jwYUnSj3tyVVF2tNmPDwCANysp+vWSj8OHDzf7+2z18YwxDW7r1WGmmsPhcFs3xtRoq5aamqpZs2bVaI+IiGiR2iQp69VHWuzYAAB4u9jY2BY7dmlpqYKCgurdxqvDTKdOneTj41PjLExxcXGNszXVkpOTNX36dNd6VVWVfvzxR5122ml1BqDfk5KSEkVERKigoECBgYGeLuekxTy3Dua5dTDPrYN5dmeMUWlpqcLDwxvc1qvDjJ+fnwYNGqT09HRdeeWVrvb09HSNHTu21n2cTqecTqdb2ymnnNKSZVopMDCQF0srYJ5bB/PcOpjn1sE8/5+GzshU8+owI0nTp0/XjTfeqMGDB2vYsGF67rnntHfvXt12222eLg0AAHgBrw8z1157rfbv36/Zs2ersLBQAwYM0IoVKxQZGenp0gAAgBfw+jAjSYmJiUpMTPR0GScFp9OpmTNn1vgoDs2LeW4dzHPrYJ5bB/PcdA7TmO88AQAAeCmv/qN5AAAADSHMAAAAqxFmAACA1QgzFistLdW0adMUGRkpf39/xcTEaNOmTa7+77//XpMmTVJ4eLjat2+vSy+9VLt27WrwuAcPHlRSUpLCwsLUrl079e3bVytWrGjJoXi1lprnefPmqU+fPvL391dERITuuusu/fzzzy05FK+ydu1ajRkzRuHh4XI4HHr33Xfd+o0xSklJUXh4uPz9/RUXF6ft27e7bVNWVqapU6eqU6dO6tChg6644gr95z//afCxn3nmGUVHR6tdu3YaNGiQ1q1b15xD8yqemufU1FQNGTJEAQEB6tKli8aNG6fc3NzmHp7X8OTzuVpqaqocDoemTZvWDCOyC2HGYrfccovS09P1yiuvaOvWrYqPj9eoUaP07bffyhijcePGaffu3Xrvvfe0ZcsWRUZGatSoUTpy5EidxywvL9fo0aOVn5+vt956S7m5uXr++efVtWvXVhyZd2mJeX7ttdd03333aebMmcrJydELL7ygxYsXKzk5uRVH5llHjhzRwIEDNX/+/Fr7H3vsMc2dO1fz58/Xpk2bFBoaqtGjR6u0tNS1zbRp0/TOO+9o0aJF+vjjj3X48GFdfvnlqqysrPNxFy9erGnTpmnGjBnasmWLLrzwQiUkJGjv3r3NPkZv4Kl5zszMVFJSkjZs2KD09HRVVFQoPj6+3teFzTw1z9U2bdqk5557TmeddVazjckqBlb66aefjI+Pj1m2bJlb+8CBA82MGTNMbm6ukWS2bdvm6quoqDDBwcHm+eefr/O4CxYsMN27dzfl5eUtVrtNWmqek5KSzMiRI93apk+fboYPH968A7CEJPPOO++41quqqkxoaKh55JFHXG0///yzCQoKMs8++6wxxpiDBw8aX19fs2jRItc23377rWnTpo1ZuXJlnY913nnnmdtuu82t7YwzzjD33XdfM43Ge7XmPB+vuLjYSDKZmZm/fSBerrXnubS01PTq1cukp6eb2NhYc+eddzbreGzAmRlLVVRUqLKyUu3atXNr9/f318cff+y6c/ix/T4+PvLz89PHH39c53GXLl2qYcOGKSkpSSEhIRowYIDmzJnTqN8MTkYtNc/Dhw9XVlaWPvvsM0nS7t27tWLFCv3hD39ogVHYJy8vT0VFRYqPj3e1OZ1OxcbGav369ZKkrKws/fLLL27bhIeHa8CAAa5tjldeXq6srCy3fSQpPj6+zn1OZi01z7U5dOiQJCk4OLiZqrdHS89zUlKS/vCHP2jUqFEtMwALEGYsFRAQoGHDhumhhx7Sd999p8rKSr366qvauHGjCgsLdcYZZygyMlLJyck6cOCAysvL9cgjj6ioqEiFhYV1Hnf37t166623VFlZqRUrVuj+++/XE088oYcffrgVR+c9Wmqer7vuOj300EMaPny4fH191aNHD40YMUL33XdfK47Oe1XfXPb4G8qGhIS4+oqKiuTn56dTTz21zm2Ot2/fPlVWVtZ73N+Tlprn4xljNH36dA0fPlwDBgxohsrt0pLzvGjRIn3++edKTU1t5qrtQpix2CuvvCJjjLp27Sqn06l//etfmjBhgnx8fOTr66u3335bO3fuVHBwsNq3b6+MjAwlJCTIx8enzmNWVVWpS5cueu655zRo0CBdd911mjFjhhYsWNCKI/MuLTHPGRkZevjhh/XMM8/o888/15IlS7Rs2TI99NBDrTgy73f8ne6NMTXajteYbZpy3JNZS81ztSlTpujLL7/U66+/3uQaTwbNPc8FBQW688479eqrr9Y4e/x7Q5ixWI8ePZSZmanDhw+roKBAn332mX755RdFR0dLkgYNGqTs7GwdPHhQhYWFWrlypfbv3+/qr01YWJh69+7t9kbct29fFRUVqby8vMXH5I1aYp4feOAB3Xjjjbrlllt05pln6sorr9ScOXOUmpqqqqqq1hqa1woNDZWkGr+RFhcXu367DQ0NVXl5uQ4cOFDnNsfr1KmTfHx86j3u70lLzfOxpk6dqqVLl+qjjz5St27dmqlyu7TUPGdlZam4uFiDBg1S27Zt1bZtW2VmZupf//qX2rZt+7u6PIAwcxLo0KGDwsLCdODAAa1atUpjx4516w8KClLnzp21a9cubd68uUb/sS644AJ9/fXXbm+oO3fuVFhYmPz8/FpsDDZoznn+6aef1KaN+8vPx8dHxhgZ7jCi6OhohYaGKj093dVWXl6uzMxMxcTESPo1RPr6+rptU1hYqG3btrm2OZ6fn58GDRrkto8kpaen17nPyayl5ln69YzClClTtGTJEn344Yf1hvuTXUvN88UXX6ytW7cqOzvbtQwePFg33HCDsrOz6z07fNLxyGXHaBYrV640//73v83u3bvN6tWrzcCBA815553n+ibSG2+8YT766CPzzTffmHfffddERkaa8ePHux3jxhtvdPsWx969e03Hjh3NlClTTG5urlm2bJnp0qWL+cc//tGqY/MmLTHPM2fONAEBAeb11193HbdHjx7mmmuuadWxeVJpaanZsmWL2bJli5Fk5s6da7Zs2WL27NljjDHmkUceMUFBQWbJkiVm69at5vrrrzdhYWGmpKTEdYzbbrvNdOvWzaxZs8Z8/vnnZuTIkWbgwIGmoqLCtc3IkSPNU0895VpftGiR8fX1NS+88IL56quvzLRp00yHDh1Mfn5+6w2+FXlqnm+//XYTFBRkMjIyTGFhoWv56aefWm/wrchT83y83+u3mQgzFlu8eLHp3r278fPzM6GhoSYpKckcPHjQ1f/kk0+abt26GV9fX3P66aeb+++/35SVlbkdIzY21kycONGtbf369Wbo0KHG6XSa7t27m4cfftjtxfR70xLz/Msvv5iUlBTTo0cP065dOxMREWESExPNgQMHWmlUnvfRRx8ZSTWW6nmqqqoyM2fONKGhocbpdJqLLrrIbN261e0YR48eNVOmTDHBwcHG39/fXH755Wbv3r1u20RGRpqZM2e6tT399NMmMjLS+Pn5mXPPPfek/rqwp+a5tseUZNLS0lp4xJ7hyefzsX6vYYa7ZgMAAKtxzQwAALAaYQYAAFiNMAMAAKxGmAEAAFYjzAAAAKsRZgAAgNUIMwAAwGqEGQAAYDXCDIBmkZGRIYfDoYMHD3q6lBM2adIkjRs3rt5tvGF8x9cZFxenadOm1btPVFSU5s2b16J1AZ5GmAF+A4fDoXfffdfTZbS62t5EY2JiVFhYqKCgIM8U9Rs8+eSTevHFF13rjQkJ3mDJkiV66KGHWuWxWjMUeUNwhF3aeroAoLlVVlbK4XDUuCs1Wpafn59CQ0M9XUaT2BjAJCk4ONjTJbjxxtfeL7/8Il9fX0+XgRbmPc84/G7FxcVpypQpmjJlik455RSddtppuv/++1V927Dy8nLde++96tq1qzp06KChQ4cqIyPDtf+LL76oU045RcuWLVO/fv3kdDq1Z88elZWV6d5771VERIScTqd69eqlF154wbXfV199pcsuu0wdO3ZUSEiIbrzxRu3bt8+trjvuuEP33nuvgoODFRoaqpSUFFd/VFSUJOnKK6+Uw+FwrTdk6dKlGjx4sNq1a6dOnTpp/Pjxrr4DBw7opptu0qmnnqr27dsrISFBu3btqjHWVatWqW/fvurYsaMuvfRSFRYWurap/ijiv//7vxUWFqbTTjtNSUlJ+uWXX1zbNDSnkvTJJ58oNjZW7du316mnnqpLLrlEBw4c0KRJk5SZmaknn3xSDodDDodD+fn5tf42/fbbb6t///5yOp2KiorSE0884fYYUVFRmjNnjm6++WYFBATo9NNP13PPPedW55QpUxQWFqZ27dopKipKqampDc7x3XffrTFjxrjW582bJ4fDoeXLl7va+vTpo//5n/9xm7Pqf9c2vmpZWVkaPHiw2rdvr5iYGOXm5jZYT25urhwOh3bs2OHWPnfuXEVFRckYo8rKSv35z39WdHS0/P391adPHz355JP1Hvf4M0jFxcUaM2aM/P39FR0drddee63B2o6VkpKi008/XU6nU+Hh4brjjjtcj7Nnzx7dddddrjmR6n7t1XZma9y4cZo0aZJrva7XZ35+vkaMGCFJOvXUU+VwOFz71XZ26Oyzz3Z7XTocDj377LMaO3asOnTooH/84x+SpPfff1+DBg1Su3bt1L17d82aNUsVFRUnND/wYp69zyXw611eO3bsaO68806zY8cO8+qrr5r27dub5557zhhjzIQJE0xMTIxZu3at+frrr83jjz9unE6n2blzpzHGmLS0NOPr62tiYmLMJ598Ynbs2GEOHz5srrnmGhMREWGWLFlivvnmG7NmzRqzaNEiY4wx3333nenUqZNJTk42OTk55vPPPzejR482I0aMcKsrMDDQpKSkmJ07d5qXXnrJOBwOs3r1amOMMcXFxa67ABcWFpri4uIGx7ps2TLj4+NjHnzwQfPVV1+Z7Oxs8/DDD7v6r7jiCtO3b1+zdu1ak52dbS655BLTs2dPU15e7jbWUaNGmU2bNpmsrCzTt29fM2HCBNcxJk6caAIDA81tt91mcnJyzPvvv+82n42Z0y1bthin02luv/12k52dbbZt22aeeuop88MPP5iDBw+aYcOGmcmTJ5vCwkJTWFhoKioqXHcNrr7z9+bNm02bNm3M7NmzTW5urklLSzP+/v5ud02OjIw0wcHB5umnnza7du0yqamppk2bNiYnJ8cYY8zjjz9uIiIizNq1a01+fr5Zt26dWbhwYYPzvHTpUhMUFGQqKyuNMcaMGzfOdOrUyfz1r381xhhTWFhoJLkeZ+LEiWbs2LHGGNPg+IYOHWoyMjLM9u3bzYUXXmhiYmIarMcYYwYNGmTuv//+Gm3JycnGGGPKy8vNgw8+aD777DOze/du1+tg8eLFbj/b6jqNqXmH5ISEBDNgwACzfv16s3nzZhMTE2P8/f3NP//5zwbre/PNN01gYKBZsWKF2bNnj9m4caPrObN//37TrVs3M3v2bNecGFP3a6+2OzePHTvW7c7xdb0+KyoqzNtvv20kmdzcXFNYWOi6S31kZGSNsQwcOLDG3bq7dOliXnjhBfPNN9+Y/Px8s3LlShMYGGhefPFF880335jVq1ebqKgok5KS0uC8wA6EGXhcbGys6du3r6mqqnK1/e1vfzN9+/Y1X3/9tXE4HObbb7912+fiiy92vQmkpaUZSSY7O9vVn5ubaySZ9PT0Wh/zgQceMPHx8W5tBQUFrv9Aq+saPny42zZDhgwxf/vb31zrksw777zT6LEOGzbM3HDDDbX27dy500gyn3zyiatt3759xt/f37zxxhtuY/36669d2zz99NMmJCTEtT5x4kQTGRlpKioqXG1XX321ufbaa40xplFzev3115sLLrigznHU9mZ1fJiZMGGCGT16tNs2f/3rX02/fv1c65GRkeZPf/qTa72qqsp06dLFLFiwwBhjzNSpU83IkSPdnhuNcfDgQdOmTRuzefNmU1VVZU477TSTmppqhgwZYowxZuHChTXmrL6QcOz41qxZ42pbvny5kWSOHj3aYE1z58413bt3d61XP0e3b99e5z6JiYnmqquualSd1cfbsGGDqz8nJ8dIalSYeeKJJ0zv3r1dwfl4tQWJ2l57x9dV7dgw09Dr8/jnUn011BZmpk2b5rbNhRdeaObMmePW9sorr5iwsLBaHx/24WMmeIXzzz/fdepakoYNG6Zdu3Zp8+bNMsaod+/e6tixo2vJzMzUN99849rez89PZ511lms9OztbPj4+io2NrfXxsrKy9NFHH7kd84wzzpAkt+Mee0xJCgsLU3FxcZPHmZ2drYsvvrjWvpycHLVt21ZDhw51tZ122mnq06ePcnJyXG3t27dXjx496q2pf//+8vHxqXWbzz//vME5ra/OxsrJydEFF1zg1nbBBRdo165dqqysdLUdO8cOh0OhoaGuWidNmqTs7Gz16dNHd9xxh1avXt2oxw4KCtLZZ5+tjIwMbd26VW3atNGtt96qL774QqWlpcrIyKjzudGQY+sNCwuTpEY9J6677jrt2bNHGzZskCS99tprOvvss9WvXz/XNs8++6wGDx6szp07q2PHjnr++ee1d+/eRtVV/fwZPHiwq+2MM87QKaec0qj9r776ah09elTdu3fX5MmT9c477zTqY5jjX3uN0dDr87c6dg6kX1/vs2fPdnu+T548WYWFhfrpp59apAa0Li4Ahtfz8fFRVlaW25uzJHXs2NH1b39/f7cw5O/vX+8xq6qqNGbMGD366KM1+qrfoCTVuHDQ4XCoqqrqhOo/Vn11mf9/jVBt7ceOrbaajt+3vrqrqqoanNOG5q8xjq+7uu149dV67rnnKi8vT//+97+1Zs0aXXPNNRo1apTeeuutBh8/Li5OGRkZ8vPzU2xsrE499VT1799fn3zyiTIyMpr8baVj660eX2OeE2FhYRoxYoQWLlyo888/X6+//rpuvfVWV/8bb7yhu+66S0888YSGDRumgIAAPf7449q4cWOj6qqe2+PnvLEiIiKUm5ur9PR0rVmzRomJiXr88ceVmZlZ7wW0x7/2JKlNmzY1ftbHXrPV1OdXQ8et1qFDB7f1qqoqzZo1y+36tGrt2rVrUi3wLpyZgVeo/m312PVevXrpnHPOUWVlpYqLi9WzZ0+3pb5vzpx55pmqqqpSZmZmrf3nnnuutm/frqioqBrHPf4/wvr4+vq6nWVoyFlnnaUPPvig1r5+/fqpoqLC7c1r//792rlzp/r27dvox2hIY+a0vjqlX38bb2jc/fr108cff+zWtn79evXu3btGiKpPYGCgrr32Wj3//PNavHix3n77bf34448N7hcXF6d169bpww8/VFxcnCQpNjZWixYt0s6dO+s9K9CY8TXFDTfcoMWLF+vTTz/VN998o+uuu87Vt27dOsXExCgxMVHnnHOOevbs6XaWsCF9+/ZVRUWFNm/e7GrLzc09oa83+/v764orrtC//vUvZWRk6NNPP9XWrVslndicdO7c2e2i9MrKSm3bts213tDr08/Pz7VffcctKSlRXl5eg/Wce+65ys3NrfF879mzp1d98wpNx08RXqGgoEDTp09Xbm6uXn/9dT311FO688471bt3b91www266aabtGTJEuXl5WnTpk169NFHtWLFijqPFxUVpYkTJ+rmm2/Wu+++q7y8PGVkZOiNN96QJCUlJenHH3/U9ddfr88++0y7d+/W6tWrdfPNN5/Qm1hUVJQ++OADFRUV6cCBAw1uP3PmTL3++uuaOXOmcnJytHXrVj322GOSpF69emns2LGaPHmyPv74Y33xxRf605/+pK5du2rs2LGNrqkhjZnT5ORkbdq0SYmJifryyy+1Y8cOLViwwPVtr6ioKG3cuFH5+fnat29frWcm7r77bn3wwQd66KGHtHPnTr300kuaP3++7rnnnkbX+s9//lOLFi3Sjh07tHPnTr355psKDQ1t1EcnF110kUpLS/X++++7wkxcXJxeffVVde7c2e3jneM1ZnxNMX78eJWUlOj222/XiBEj1LVrV1dfz549tXnzZq1atUo7d+7UAw88oE2bNjX62H369NGll16qyZMna+PGjcrKytItt9zS6LMgL774ol544QVt27ZNu3fv1iuvvCJ/f39FRkZK+nVO1q5dq2+//dbtW3+1GTlypJYvX67ly5drx44dSkxMdAtVDb0+IyMj5XA4tGzZMv3www86fPiw67ivvPKK1q1bp23btmnixImNCsYPPvigXn75ZaWkpGj79u3KycnR4sWLdf/99zdqbuD9CDPwCjfddJOOHj2q8847T0lJSZo6dar+8pe/SJLS0tJ000036e6771afPn10xRVXaOPGjYqIiKj3mAsWLNAf//hHJSYm6owzztDkyZN15MgRSVJ4eLg++eQTVVZW6pJLLtGAAQN05513Kigo6IR+U3viiSeUnp6uiIgInXPOOQ1uHxcXpzfffFNLly7V2WefrZEjR7qdiUlLS9OgQYN0+eWXa9iwYTLGaMWKFc3+dzIamtPevXtr9erV+uKLL3Teeedp2LBheu+999S27a+fTN9zzz3y8fFRv3791Llz51qv6zj33HP1xhtvaNGiRRowYIAefPBBzZ492+3ruQ3p2LGjHn30UQ0ePFhDhgxRfn6+VqxY0aifUVBQkM455xwFBwe7gsuFF16oqqqqBq/VaMz4miIwMFBjxozRF198oRtuuMGt77bbbtP48eN17bXXaujQodq/f78SExNP6PhpaWmKiIhQbGysxo8fr7/85S/q0qVLo/Y95ZRT9Pzzz+uCCy5wnZl7//33ddppp0mSZs+erfz8fPXo0UOdO3eu91g333yzJk6cqJtuukmxsbGKjo52fd26Wn2vz65du2rWrFm67777FBISoilTpkj6NWRfdNFFuvzyy3XZZZdp3LhxbteP1eWSSy7RsmXLlJ6eriFDhuj888/X3LlzXUEN9nOYuj6oB1pJXFyczj77bP7kOgCgSTgzAwAArEaYAZpR//793b7+eexyon+NFXV77bXX6pzn/v37e6Qmb//Ze+OcAc2Fj5mAZrRnz55avyoqSSEhIQoICGjlik5OpaWl+v7772vt8/X19ci1EN7+s/fGOQOaC2EGAABYjY+ZAACA1QgzAADAaoQZAABgNcIMAACwGmEGAABYjTADAACsRpgBAABWI8wAAACr/T9IAbHVUAfJJwAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_connections_with_valid_structure\")"]}, {"cell_type": "code", "execution_count": 14, "id": "24225a83", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_connections_with_valid_target_node', ylabel='Count'>"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAjMAAAGxCAYAAACXwjeMAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjAsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvlHJYcgAAAAlwSFlzAAAPYQAAD2EBqD+naQAANUtJREFUeJzt3XtcVHXi//H3iDigXBRNLoqAq5Joal5SKQPd1Gwzzdosy8u3Mstb5aatayVaK20Xss3SbR990S6m7TdrrUxFV7ymCYaaIaChkmGupoKXBZXP749+zDqCMBIwc/T1fDzO4+G5zMx7Ph4Pb885w9iMMUYAAAAWVcfdAQAAAH4NygwAALA0ygwAALA0ygwAALA0ygwAALA0ygwAALA0ygwAALA0ygwAALC0uu4OUNNKSkr0448/yt/fXzabzd1xAACAC4wxKiwsVFhYmOrUqfjcyxVfZn788UeFh4e7OwYAAKiCvLw8NW/evMJtrvgy4+/vL+mXwQgICHBzGgAA4IqCggKFh4c7fo5X5IovM6WXlgICAigzAABYjCu3iHADMAAAsDTKDAAAsDTKDAAAsDTKDAAAsDTKDAAAsDTKDAAAsDTKDAAAsDTKDAAAsDTKDAAAsDTKDAAAsDTKDAAAsDTKDAAAsDTKDAAAsDTKDAAAsLS67g4AAABcc+DAAR05csTdMZw0adJELVq0cGsGygwAABZw4MABXXttW505c9rdUZz4+tbX7t2Zbi00lBkAACzgyJEjOnPmtLo/OF0BoZHujiNJKsjfpy3/O0NHjhyhzAAAANcEhEYqqEW0u2N4FG4ABgAAlkaZAQAAlkaZAQAAlkaZAQAAlkaZAQAAlkaZAQAAlkaZAQAAlkaZAQAAlkaZAQAAlkaZAQAAlkaZAQAAlkaZAQAAlkaZAQAAlkaZAQAAlubWMjN37lx16NBBAQEBCggIUM+ePfXll1861htjlJCQoLCwMPn6+io+Pl67du1yY2IAAOBp3FpmmjdvrhdffFFpaWlKS0tTnz59NGjQIEdheemll5SUlKQ5c+Zo69atCgkJUd++fVVYWOjO2AAAwIO4tcwMHDhQt912m9q0aaM2bdroz3/+s/z8/LR582YZYzR79mxNmzZNQ4YMUfv27bVgwQKdPn1aCxcudGdsAADgQTzmnpnz589r0aJFOnXqlHr27Knc3FwdOnRI/fr1c2xjt9sVFxenTZs2uTEpAADwJHXdHWDnzp3q2bOn/vOf/8jPz0+ffPKJYmJiHIUlODjYafvg4GDt37//ks9XVFSkoqIix3xBQUHNBAcAAB7B7WdmoqOjlZGRoc2bN+uxxx7TyJEj9d133znW22w2p+2NMWWWXSgxMVGBgYGOKTw8vMayAwAA93N7malXr55atWqlrl27KjExUR07dtTrr7+ukJAQSdKhQ4ectj98+HCZszUXmjp1qk6cOOGY8vLyajQ/AABwL7eXmYsZY1RUVKSoqCiFhIQoJSXFsa64uFhr165VbGzsJR9vt9sdH/UunQAAwJXLrffM/OlPf9KAAQMUHh6uwsJCLVq0SKmpqVq+fLlsNpueeOIJzZo1S61bt1br1q01a9Ys1a9fX8OGDXNnbAAA4EHcWmZ++uknDR8+XPn5+QoMDFSHDh20fPly9e3bV5I0ZcoUnTlzRmPHjtWxY8fUvXt3rVy5Uv7+/u6MDQAAPIhby8w777xT4XqbzaaEhAQlJCTUTiAAAGA5HnfPDAAAwOWgzAAAAEujzAAAAEujzAAAAEujzAAAAEujzAAAAEujzAAAAEujzAAAAEujzAAAAEujzAAAAEujzAAAAEujzAAAAEujzAAAAEujzAAAAEujzAAAAEujzAAAAEujzAAAAEujzAAAAEujzAAAAEujzAAAAEujzAAAAEujzAAAAEujzAAAAEujzAAAAEujzAAAAEujzAAAAEujzAAAAEujzAAAAEujzAAAAEujzAAAAEujzAAAAEujzAAAAEujzAAAAEujzAAAAEujzAAAAEujzAAAAEujzAAAAEujzAAAAEujzAAAAEujzAAAAEujzAAAAEujzAAAAEujzAAAAEujzAAAAEtza5lJTExUt27d5O/vr6ZNm2rw4MHKyspy2mbUqFGy2WxOU48ePdyUGAAAeBq3lpm1a9dq3Lhx2rx5s1JSUnTu3Dn169dPp06dctru1ltvVX5+vmNatmyZmxIDAABPU9edL758+XKn+eTkZDVt2lTp6em6+eabHcvtdrtCQkJqOx4AALAAj7pn5sSJE5KkoKAgp+Wpqalq2rSp2rRpo9GjR+vw4cPuiAcAADyQW8/MXMgYo0mTJummm25S+/btHcsHDBig3//+94qIiFBubq6effZZ9enTR+np6bLb7WWep6ioSEVFRY75goKCWskPAADcw2PKzPjx47Vjxw5t2LDBafnQoUMdf27fvr26du2qiIgIffHFFxoyZEiZ50lMTNSMGTNqPC8AAPAMHnGZacKECVq6dKnWrFmj5s2bV7htaGioIiIilJOTU+76qVOn6sSJE44pLy+vJiIDAAAP4dYzM8YYTZgwQZ988olSU1MVFRVV6WOOHj2qvLw8hYaGlrvebreXe/kJAABcmdx6ZmbcuHF6//33tXDhQvn7++vQoUM6dOiQzpw5I0k6efKknnrqKX311Vfat2+fUlNTNXDgQDVp0kR33nmnO6MDAAAP4dYzM3PnzpUkxcfHOy1PTk7WqFGj5OXlpZ07d+rdd9/V8ePHFRoaqt69e2vx4sXy9/d3Q2IAAOBp3H6ZqSK+vr5asWJFLaUBAABW5BE3AAMAAFQVZQYAAFgaZQYAAFgaZQYAAFgaZQYAAFgaZQYAAFgaZQYAAFgaZQYAAFgaZQYAAFgaZQYAAFgaZQYAAFgaZQYAAFgaZQYAAFgaZQYAAFgaZQYAAFgaZQYAAFgaZQYAAFgaZQYAAFgaZQYAAFgaZQYAAFgaZQYAAFgaZQYAAFgaZQYAAFgaZQYAAFgaZQYAAFgaZQYAAFgaZQYAAFgaZQYAAFgaZQYAAFgaZQYAAFgaZQYAAFgaZQYAAFgaZQYAAFgaZQYAAFgaZQYAAFgaZQYAAFgaZQYAAFgaZQYAAFgaZQYAAFgaZQYAAFgaZQYAAFgaZQYAAFgaZQYAAFgaZQYAAFiaW8tMYmKiunXrJn9/fzVt2lSDBw9WVlaW0zbGGCUkJCgsLEy+vr6Kj4/Xrl273JQYAAB4GreWmbVr12rcuHHavHmzUlJSdO7cOfXr10+nTp1ybPPSSy8pKSlJc+bM0datWxUSEqK+ffuqsLDQjckBAICnqOvOF1++fLnTfHJyspo2bar09HTdfPPNMsZo9uzZmjZtmoYMGSJJWrBggYKDg7Vw4UKNGTPGHbEBAIAH8ah7Zk6cOCFJCgoKkiTl5ubq0KFD6tevn2Mbu92uuLg4bdq0yS0ZAQCAZ3HrmZkLGWM0adIk3XTTTWrfvr0k6dChQ5Kk4OBgp22Dg4O1f//+cp+nqKhIRUVFjvmCgoIaSgwAADyBx5yZGT9+vHbs2KEPP/ywzDqbzeY0b4wps6xUYmKiAgMDHVN4eHiN5AUAAJ7BI8rMhAkTtHTpUq1Zs0bNmzd3LA8JCZH03zM0pQ4fPlzmbE2pqVOn6sSJE44pLy+v5oIDAAC3c2uZMcZo/PjxWrJkif71r38pKirKaX1UVJRCQkKUkpLiWFZcXKy1a9cqNja23Oe02+0KCAhwmgAAwJXLrffMjBs3TgsXLtQ///lP+fv7O87ABAYGytfXVzabTU888YRmzZql1q1bq3Xr1po1a5bq16+vYcOGuTM6AADwEG4tM3PnzpUkxcfHOy1PTk7WqFGjJElTpkzRmTNnNHbsWB07dkzdu3fXypUr5e/vX8tpAQCAJ3JrmTHGVLqNzWZTQkKCEhISaj4QAACwHI+4ARgAAKCqKDMAAMDSKDMAAMDSKDMAAMDSKDMAAMDSKDMAAMDSKDMAAMDSKDMAAMDSKDMAAMDSKDMAAMDSqlRmWrZsqaNHj5ZZfvz4cbVs2fJXhwIAAHBVlcrMvn37dP78+TLLi4qKdPDgwV8dCgAAwFWX9UWTS5cudfx5xYoVCgwMdMyfP39eq1evVmRkZLWFAwAAqMxllZnBgwdL+uWbrEeOHOm0ztvbW5GRkXr11VerLRwAAEBlLqvMlJSUSJKioqK0detWNWnSpEZCAQAAuOqyykyp3Nzc6s4BAABQJVUqM5K0evVqrV69WocPH3acsSn1v//7v786GAAAgCuqVGZmzJihmTNnqmvXrgoNDZXNZqvuXAAAAC6pUpmZN2+e5s+fr+HDh1d3HgAAgMtSpd8zU1xcrNjY2OrOAgAAcNmqVGYefvhhLVy4sLqzAAAAXLYqXWb6z3/+o7ffflurVq1Shw4d5O3t7bQ+KSmpWsIBAABUpkplZseOHerUqZMk6dtvv3Vax83AAACgNlWpzKxZs6a6cwAAAFRJle6ZAQAA8BRVOjPTu3fvCi8n/etf/6pyIAAAgMtRpTJTer9MqbNnzyojI0PffvttmS+gBAAAqElVKjOvvfZaucsTEhJ08uTJXxUIAADgclTrPTMPPPAA38sEAABqVbWWma+++ko+Pj7V+ZQAAAAVqtJlpiFDhjjNG2OUn5+vtLQ0Pfvss9USDAAAwBVVKjOBgYFO83Xq1FF0dLRmzpypfv36VUswAAAAV1SpzCQnJ1d3DgAAgCqpUpkplZ6erszMTNlsNsXExOj666+vrlwAAAAuqVKZOXz4sO69916lpqaqYcOGMsboxIkT6t27txYtWqRrrrmmunMCAACUq0qfZpowYYIKCgq0a9cu/fzzzzp27Ji+/fZbFRQUaOLEidWdEQAA4JKqdGZm+fLlWrVqldq2betYFhMTozfffJMbgAEAQK2q0pmZkpISeXt7l1nu7e2tkpKSXx0KAADAVVUqM3369NHjjz+uH3/80bHs4MGDevLJJ/Xb3/622sIBAABUpkplZs6cOSosLFRkZKR+85vfqFWrVoqKilJhYaHeeOON6s4IAABwSVW6ZyY8PFzbtm1TSkqKdu/eLWOMYmJidMstt1R3PgAAgApd1pmZf/3rX4qJiVFBQYEkqW/fvpowYYImTpyobt26qV27dlq/fn2NBAUAACjPZZWZ2bNna/To0QoICCizLjAwUGPGjFFSUlK1hQMAAKjMZZWZ7du369Zbb73k+n79+ik9Pf1XhwIAAHDVZZWZn376qdyPZJeqW7eu/v3vf7v8fOvWrdPAgQMVFhYmm82mTz/91Gn9qFGjZLPZnKYePXpcTmQAAHCFu6wy06xZM+3cufOS63fs2KHQ0FCXn+/UqVPq2LGj5syZc8ltbr31VuXn5zumZcuWXU5kAABwhbusTzPddttteu655zRgwAD5+Pg4rTtz5oymT5+u22+/3eXnGzBggAYMGFDhNna7XSEhIZcTEwAAXEUuq8w888wzWrJkidq0aaPx48crOjpaNptNmZmZevPNN3X+/HlNmzatWgOmpqaqadOmatiwoeLi4vTnP/9ZTZs2rdbXAAAA1nVZZSY4OFibNm3SY489pqlTp8oYI0my2Wzq37+/3nrrLQUHB1dbuAEDBuj3v/+9IiIilJubq2effVZ9+vRRenq67HZ7uY8pKipSUVGRY770Y+QAAODKdNm/NC8iIkLLli3TsWPHtGfPHhlj1Lp1azVq1Kjaww0dOtTx5/bt26tr166KiIjQF198oSFDhpT7mMTERM2YMaPaswAAAM9Upa8zkKRGjRqpW7duuuGGG2qkyJQnNDRUERERysnJueQ2U6dO1YkTJxxTXl5erWQDAADuUaWvM3CXo0ePKi8vr8JPTNnt9kteggIAAFcet5aZkydPas+ePY753NxcZWRkKCgoSEFBQUpISNBdd92l0NBQ7du3T3/605/UpEkT3XnnnW5MDQAAPIlby0xaWpp69+7tmJ80aZIkaeTIkZo7d6527typd999V8ePH1doaKh69+6txYsXy9/f312RAQCAh3FrmYmPj3d8Iqo8K1asqMU0AADAiqp8AzAAAIAnoMwAAABLo8wAAABLo8wAAABLo8wAAABLo8wAAABLo8wAAABLo8wAAABLo8wAAABLo8wAAABLo8wAAABLo8wAAABLo8wAAABLo8wAAABLo8wAAABLo8wAAABLo8wAAABLo8wAAABLo8wAAABLo8wAAABLo8wAAABLo8wAAABLo8wAAABLo8wAAABLo8wAAABLo8wAAABLo8wAAABLo8wAAABLo8wAAABLo8wAAABLo8wAAABLo8wAAABLo8wAAABLo8wAAABLo8wAAABLo8wAAABLo8wAAABLo8wAAABLo8wAAABLo8wAAABLo8wAAABLo8wAAABLo8wAAABLo8wAAABLc2uZWbdunQYOHKiwsDDZbDZ9+umnTuuNMUpISFBYWJh8fX0VHx+vXbt2uScsAADwSG4tM6dOnVLHjh01Z86ccte/9NJLSkpK0pw5c7R161aFhISob9++KiwsrOWkAADAU9V154sPGDBAAwYMKHedMUazZ8/WtGnTNGTIEEnSggULFBwcrIULF2rMmDG1GRUAAHgoj71nJjc3V4cOHVK/fv0cy+x2u+Li4rRp0yY3JgMAAJ7ErWdmKnLo0CFJUnBwsNPy4OBg7d+//5KPKyoqUlFRkWO+oKCgZgICAACP4LFnZkrZbDaneWNMmWUXSkxMVGBgoGMKDw+v6YgAAMCNPLbMhISESPrvGZpShw8fLnO25kJTp07ViRMnHFNeXl6N5gQAAO7lsWUmKipKISEhSklJcSwrLi7W2rVrFRsbe8nH2e12BQQEOE0AAODK5dZ7Zk6ePKk9e/Y45nNzc5WRkaGgoCC1aNFCTzzxhGbNmqXWrVurdevWmjVrlurXr69hw4a5MTUAAPAkbi0zaWlp6t27t2N+0qRJkqSRI0dq/vz5mjJlis6cOaOxY8fq2LFj6t69u1auXCl/f393RQYAAB7GrWUmPj5exphLrrfZbEpISFBCQkLthQIAAJbisffMAAAAuIIyAwAALI0yAwAALI0yAwAALI0yAwAALI0yAwAALI0yAwAALI0yAwAALI0yAwAALI0yAwAALI0yAwAALI0yAwAALI0yAwAALI0yAwAALI0yAwAALI0yAwAALI0yAwAALI0yAwAALI0yAwAALI0yAwAALI0yAwAALI0yAwAALI0yAwAALI0yAwAALI0yAwAALI0yAwAALI0yAwAALI0yAwAALI0yAwAALI0yAwAALI0yAwAALI0yAwAALI0yAwAALI0yAwAALI0yAwAALI0yAwAALI0yAwAALI0yAwAALI0yAwAALI0yAwAALI0yAwAALI0yAwAALI0yAwAALI0yAwAALM2jy0xCQoJsNpvTFBIS4u5YAADAg9R1d4DKtGvXTqtWrXLMe3l5uTENAADwNB5fZurWrcvZGAAAcEkefZlJknJychQWFqaoqCjde++9+v77790dCQAAeBCPPjPTvXt3vfvuu2rTpo1++uknvfDCC4qNjdWuXbvUuHHjch9TVFSkoqIix3xBQUFtxQUAAG7g0WdmBgwYoLvuukvXXXedbrnlFn3xxReSpAULFlzyMYmJiQoMDHRM4eHhtRUXAAC4gUeXmYs1aNBA1113nXJyci65zdSpU3XixAnHlJeXV4sJAQBAbfPoy0wXKyoqUmZmpnr16nXJbex2u+x2ey2mAgAA7uTRZ2aeeuoprV27Vrm5udqyZYvuvvtuFRQUaOTIke6OBgAAPIRHn5n54YcfdN999+nIkSO65ppr1KNHD23evFkRERHujgYAADyER5eZRYsWuTsCAADwcB59mQkAAKAylBkAAGBplBkAAGBplBkAAGBplBkAAGBplBkAAGBplBkAAGBplBkAAGBplBkAAGBplBkAAGBplBkAAGBplBkAAGBplBkAAGBplBkAAGBplBkAAGBplBkAAGBplBkAAGBplBkAAGBplBkAAGBplBkAAGBplBkAAGBplBkAAGBplBkAAGBplBkAAGBplBkAAGBplBkAAGBplBkAAGBplBkAAGBplBkAAGBplBkAAGBplBkAAGBplBkAAGBplBkAAGBplBkAAGBplBkAAGBplBkAAGBplBkAAGBpdd0dwOoOHDigI0eOuDuGkyZNmqhFixbujgEAQK2gzPwKBw4c0LXXttWZM6fdHcWJr2997d6dSaEBAFwVKDO/wpEjR3TmzGl1f3C6AkIj3R1HklSQv09b/neGjhw5QpkBAFwVKDPVICA0UkEtot0dAwCAqxI3AAMAAEujzAAAAEujzAAAAEuzRJl56623FBUVJR8fH3Xp0kXr1693dyQAAOAhPL7MLF68WE888YSmTZumb775Rr169dKAAQN04MABd0cDAAAewOPLTFJSkh566CE9/PDDatu2rWbPnq3w8HDNnTvX3dEAAIAH8OgyU1xcrPT0dPXr189peb9+/bRp0yY3pQIAAJ7Eo3/PzJEjR3T+/HkFBwc7LQ8ODtahQ4fKfUxRUZGKiooc8ydOnJAkFRQUVHu+kydPSpJ+3p+lc0Vnqv35q6Lg0C+X39LT0x35PEWdOnVUUlLi7hhOPDGT5Jm5PDGTRK7L4YmZJM/M5YmZsrKyJHnmz5yTJ09W+8/Z0uczxlS+sfFgBw8eNJLMpk2bnJa/8MILJjo6utzHTJ8+3UhiYmJiYmJiugKmvLy8SvuCR5+ZadKkiby8vMqchTl8+HCZszWlpk6dqkmTJjnmjx8/roiICB04cECBgYE1mtcKCgoKFB4erry8PAUEBLg7jlsxFs4YD2eMx38xFs4Yj/+qybEwxqiwsFBhYWGVbuvRZaZevXrq0qWLUlJSdOeddzqWp6SkaNCgQeU+xm63y263l1keGBh41e90FwoICGA8/j/Gwhnj4Yzx+C/Gwhnj8V81NRaunoTw6DIjSZMmTdLw4cPVtWtX9ezZU2+//bYOHDigRx991N3RAACAB/D4MjN06FAdPXpUM2fOVH5+vtq3b69ly5YpIiLC3dEAAIAH8PgyI0ljx47V2LFjq/RYu92u6dOnl3vp6WrEePwXY+GM8XDGePwXY+GM8fgvTxkLmzGufOYJAADAM3n0L80DAACoDGUGAABYGmUGAABY2hVRZs6dO6dnnnlGUVFR8vX1VcuWLTVz5swyv4o6MzNTd9xxhwIDA+Xv768ePXpckd++7cp4nDx5UuPHj1fz5s3l6+urtm3bXrFf3llYWKgnnnhCERER8vX1VWxsrLZu3epYb4xRQkKCwsLC5Ovrq/j4eO3atcuNiWtWReNx9uxZPf3007ruuuvUoEEDhYWFacSIEfrxxx/dnLrmVLZ/XGjMmDGy2WyaPXt27YasJa6MxdVyHJUqH48r+Ti6bt06DRw4UGFhYbLZbPr000+d1rty3CwqKtKECRPUpEkTNWjQQHfccYd++OGHmgn8a79ywBO88MILpnHjxubzzz83ubm55h//+Ifx8/Mzs2fPdmyzZ88eExQUZCZPnmy2bdtm9u7daz7//HPz008/uTF5zXBlPB5++GHzm9/8xqxZs8bk5uaav/3tb8bLy8t8+umnbkxeM+655x4TExNj1q5da3Jycsz06dNNQECA+eGHH4wxxrz44ovG39/ffPzxx2bnzp1m6NChJjQ01BQUFLg5ec2oaDyOHz9ubrnlFrN48WKze/du89VXX5nu3bubLl26uDt2jals/yj1ySefmI4dO5qwsDDz2muvuSdsDatsLK6m46gxlY/HlXwcXbZsmZk2bZr5+OOPjSTzySefOK135bj56KOPmmbNmpmUlBSzbds207t3b9OxY0dz7ty5as97RZSZ3/3ud+bBBx90WjZkyBDzwAMPOOaHDh3qNH8lc2U82rVrZ2bOnOm0TefOnc0zzzxTKxlry+nTp42Xl5f5/PPPnZZ37NjRTJs2zZSUlJiQkBDz4osvOtb95z//MYGBgWbevHm1HbfGVTYe5fn666+NJLN///7aiFirXB2PH374wTRr1sx8++23JiIi4oosM66MxdV0HHVlPK6W4+jFZcaV4+bx48eNt7e3WbRokWObgwcPmjp16pjly5dXe8Yr4jLTTTfdpNWrVys7O1uStH37dm3YsEG33XabJKmkpERffPGF2rRpo/79+6tp06bq3r17mdNmV4rKxqN0m6VLl+rgwYMyxmjNmjXKzs5W//793RW7Rpw7d07nz5+Xj4+P03JfX19t2LBBubm5OnTokPr16+dYZ7fbFRcXp02bNtV23BpX2XiU58SJE7LZbGrYsGEtJKxdroxHSUmJhg8frsmTJ6tdu3buiFkrKhuLq+046sq+cbUcRy/mynEzPT1dZ8+eddomLCxM7du3r5lja7XXIzcoKSkxf/zjH43NZjN169Y1NpvNzJo1y7E+Pz/fSDL169c3SUlJ5ptvvjGJiYnGZrOZ1NRUNyavGZWNhzHGFBUVmREjRhhJpm7duqZevXrm3XffdVPimtWzZ08TFxdnDh48aM6dO2fee+89Y7PZTJs2bczGjRuNJHPw4EGnx4wePdr069fPTYlrVkXjcbEzZ86YLl26mPvvv98NSWtHZeMxa9Ys07dvX1NSUmKMMVfsmRljKh6Lq+04akzl+8bVchzVRWdmXDlufvDBB6ZevXplnqtv377mkUceqfaMV8SZmcWLF+v999/XwoULtW3bNi1YsECvvPKKFixYIEmOG18HDRqkJ598Up06ddIf//hH3X777Zo3b547o9eIysZDkv76179q8+bNWrp0qdLT0/Xqq69q7NixWrVqlRuT14z33ntPxhg1a9ZMdrtdf/3rXzVs2DB5eXk5trHZbE6PMcaUWXalcGU8pF9uBr733ntVUlKit956y01pa15F45Genq7XX39d8+fPv2L3hwtVNBZX23FUqvzfytV0HC1PVY6bNXZsrfZ65AbNmzc3c+bMcVr2/PPPm+joaGPML+25bt265vnnn3faZsqUKSY2NrbWctaWysbj9OnTxtvbu8y14Iceesj079+/1nLWtpMnT5off/zRGPPLjX233Xab2bt3r5Fktm3b5rTtHXfcYUaMGOGOmLWmvPEoVVxcbAYPHmw6dOhgjhw54q6Itaq88XjttdeMzWYzXl5ejkmSqVOnjomIiHBv4BpU3lhcbcfRC5U3HlfTcVQXnZlx5bi5evVqI8n8/PPPTtt06NDBPPfcc9We8Yo4M3P69GnVqeP8Vi78n0S9evXUrVs3ZWVlOW2TnZ19RX5hZWXjcfbsWZ09e7bCba5EDRo0UGhoqI4dO6YVK1Zo0KBBioqKUkhIiFJSUhzbFRcXa+3atYqNjXVj2ppX3nhIv+wf99xzj3JycrRq1So1btzYzUlrR3njMXz4cO3YsUMZGRmOKSwsTJMnT9aKFSvcHbnGlDcWV9tx9ELljcfVehyV5NJxs0uXLvL29nbaJj8/X99++23NHFurvR65wciRI02zZs0cH0VesmSJadKkiZkyZYpjmyVLlhhvb2/z9ttvm5ycHPPGG28YLy8vs379ejcmrxmujEdcXJxp166dWbNmjfn+++9NcnKy8fHxMW+99ZYbk9eM5cuXmy+//NJ8//33ZuXKlaZjx47mhhtuMMXFxcaYXz5iGBgYaJYsWWJ27txp7rvvviv6o9kVjcfZs2fNHXfcYZo3b24yMjJMfn6+YyoqKnJ39BpR2f5xsSv5npnKxuJqOo4aU/l4XMnH0cLCQvPNN9+Yb775xkhy3CdV+qlGV46bjz76qGnevLlZtWqV2bZtm+nTpw8fza5IQUGBefzxx02LFi2Mj4+PadmypZk2bVqZg+8777xjWrVqZXx8fEzHjh2viN8FUB5XxiM/P9+MGjXKhIWFGR8fHxMdHW1effVVx02OV5LFixebli1bmnr16pmQkBAzbtw4c/z4ccf6kpISM336dBMSEmLsdru5+eabzc6dO92YuGZVNB65ublGUrnTmjVr3Bu8hlS2f1zsSi4zrozF1XIcNaby8biSj6Nr1qwp9zgwcuRIY4xrx80zZ86Y8ePHm6CgIOPr62tuv/12c+DAgRrJy7dmAwAAS7si7pkBAABXL8oMAACwNMoMAACwNMoMAACwNMoMAACwNMoMAACwNMoMAACwNMoMAACwNMoMgEtKTU2VzWbT8ePH3R3lso0aNUqDBw+ucBtPeH8X54yPj9cTTzxR4WMiIyM1e/bsGs11NfCEv39UD8oMcBGbzaZPP/3U3TFqXXk/RGNjY5Wfn6/AwED3hPoVXn/9dc2fP98x70pJ8ARLlizR888/X23P54n7sydmgrXVdXcAwBXnz5+XzWYr8w21qFn16tVTSEiIu2NUiRULmCQFBQW5O0K5iouLVa9ePXfHAMrFTwbUiPj4eI0fP17jx49Xw4YN1bhxYz3zzDMq/Sqw4uJiTZkyRc2aNVODBg3UvXt3paamOh4/f/58NWzYUJ9//rliYmJkt9u1f/9+FRUVacqUKQoPD5fdblfr1q31zjvvOB733Xff6bbbbpOfn5+Cg4M1fPhwHTlyxCnXxIkTNWXKFAUFBSkkJEQJCQmO9ZGRkZKkO++8UzabzTFfmaVLl6pr167y8fFRkyZNNGTIEMe6Y8eOacSIEWrUqJHq16+vAQMGKCcnp8x7XbFihdq2bSs/Pz/deuutys/Pd2xTeinilVdeUWhoqBo3bqxx48bp7Nmzjm0qG1NJ2rhxo+Li4lS/fn01atRI/fv317FjxzRq1CitXbtWr7/+umw2m2w2m/bt21fuafiPP/5Y7dq1k91uV2RkpF599VWn14iMjNSsWbP04IMPyt/fXy1atNDbb7/tlHP8+PEKDQ2Vj4+PIiMjlZiYWOkY/+EPf9DAgQMd87Nnz5bNZtMXX3zhWBYdHa2//e1vTmNW+ufy3l+p9PR0de3aVfXr11dsbKyysrIqzZOVlSWbzabdu3c7LU9KSlJkZKSMMTp//rweeughRUVFydfXV9HR0Xr99dcrfN6LzyAdPnxYAwcOlK+vr6KiovTBBx9Umq3UpfbnvXv3atCgQQoODpafn5+6deumVatWlXnsCy+8oFGjRikwMFCjR4+WJP39739XeHi46tevrzvvvFNJSUlq2LCh02M/++wzdenSRT4+PmrZsqVmzJihc+fOVZipIgkJCerUqZPee+89RUZGKjAwUPfee68KCwsd2xQVFWnixIlq2rSpfHx8dNNNN2nr1q1Oz7Ns2TK1adNGvr6+6t27t9M+UGrTpk26+eab5evrq/DwcE2cOFGnTp2qNCPcrEa+vhJXvbi4OOPn52cef/xxs3v3bvP++++b+vXrm7ffftsYY8ywYcNMbGysWbdundmzZ495+eWXjd1uN9nZ2cYYY5KTk423t7eJjY01GzduNLt37zYnT54099xzjwkPDzdLliwxe/fuNatWrTKLFi0yxhjz448/miZNmpipU6eazMxMs23bNtO3b1/Tu3dvp1wBAQEmISHBZGdnmwULFhibzWZWrlxpjDHm8OHDRpJJTk42+fn55vDhw5W+188//9x4eXmZ5557znz33XcmIyPD/PnPf3asv+OOO0zbtm3NunXrTEZGhunfv79p1aqVKS4udnqvt9xyi9m6datJT083bdu2NcOGDXM8x8iRI01AQIB59NFHTWZmpvnss8+cxtOVMf3mm2+M3W43jz32mMnIyDDffvuteeONN8y///1vc/z4cdOzZ08zevRok5+fb/Lz8825c+cc35x77NgxY4wxaWlppk6dOmbmzJkmKyvLJCcnG19fX5OcnOzIERERYYKCgsybb75pcnJyTGJioqlTp47JzMw0xhjz8ssvm/DwcLNu3Tqzb98+s379erNw4cJKx3np0qUmMDDQnD9/3hhjzODBg02TJk3M5MmTjTG/fIOxJMfrjBw50gwaNMgYYyp9f927dzepqalm165dplevXiY2NrbSPMYY06VLF/PMM8+UWTZ16lRjjDHFxcXmueeeM19//bX5/vvvHf8OFi9e7PR3W5rTmF/20ccff9wxP2DAANO+fXuzadMmk5aWZmJjY42vr69L39x9qf05IyPDzJs3z+zYscNkZ2ebadOmGR8fH7N//37HYyMiIkxAQIB5+eWXTU5OjsnJyTEbNmwwderUMS+//LLJysoyb775pgkKCjKBgYGOxy1fvtwEBASY+fPnm71795qVK1eayMhIk5CQUGGmikyfPt34+fmZIUOGmJ07d5p169aZkJAQ86c//cmxzcSJE01YWJhZtmyZ2bVrlxk5cqRp1KiROXr0qDHGmAMHDhi73e50TAoODnbav3fs2GH8/PzMa6+9ZrKzs83GjRvN9ddfb0aNGlVpRrgXZQY1Ii4uzrRt29aUlJQ4lj399NOmbdu2Zs+ePcZms5mDBw86Pea3v/2t44dAcnKykWQyMjIc67Oysowkk5KSUu5rPvvss6Zfv35Oy/Ly8owkk5WV5ch10003OW3TrVs38/TTTzvmJZlPPvnE5ffas2dPc//995e7Ljs720gyGzdudCw7cuSI8fX1NR999JHTe92zZ49jmzfffNMEBwc75keOHGkiIiLMuXPnHMt+//vfm6FDhxpjjEtjet9995kbb7zxku/j4h+ixpgyZWbYsGGmb9++TttMnjzZxMTEOOYjIiLMAw884JgvKSkxTZs2NXPnzjXGGDNhwgTTp08fp33DFcePHzd16tQxaWlppqSkxDRu3NgkJiaabt26GWOMWbhwYZkxq6gkXPj+Vq1a5Vj2xRdfGEnmzJkzlWZKSkoyLVu2dMyX7qO7du265GPGjh1r7rrrLpdylj7f5s2bHeszMzONJJfKjDGu788xMTHmjTfecMxHRESYwYMHO20zdOhQ87vf/c5p2f333+9UZnr16mVmzZrltM17771nQkNDLztTqenTp5v69eubgoICx7LJkyeb7t27G2OMOXnypPH29jYffPCBY31xcbEJCwszL730kjHGmKlTp5Z7TLpw/x4+fLh55JFHnF57/fr1pk6dOi7tD3AfLjOhxvTo0UM2m80x37NnT+Xk5CgtLU3GGLVp00Z+fn6Oae3atdq7d69j+3r16qlDhw6O+YyMDHl5eSkuLq7c10tPT9eaNWucnvPaa6+VJKfnvfA5JSk0NFSHDx+u8vvMyMjQb3/723LXZWZmqm7duurevbtjWePGjRUdHa3MzEzHsvr16+s3v/lNhZnatWsnLy+vcrfZtm1bpWNaUU5XZWZm6sYbb3RaduONNyonJ0fnz593LLtwjG02m0JCQhxZR40apYyMDEVHR2vixIlauXKlS68dGBioTp06KTU1VTt37lSdOnU0ZswYbd++XYWFhUpNTb3kvlGZC/OGhoZKkkv7xL333qv9+/dr8+bNkqQPPvhAnTp1UkxMjGObefPmqWvXrrrmmmvk5+env//97zpw4IBLuUr3n65duzqWXXvttWUu61yuU6dOacqUKYqJiVHDhg3l5+en3bt3l8l14etKv1xau+GGG5yWXTyfnp6umTNnOu2Ho0ePVn5+vk6fPl3lzJGRkfL393fMX7j/7927V2fPnnXaN729vXXDDTc4/p1lZmaWe0y6OPv8+fOdsvfv318lJSXKzc2tcnbUPG4Ahlt4eXkpPT3d6YezJPn5+Tn+7Ovr63Tg8fX1rfA5S0pKNHDgQP3lL38ps670B5T0y0HuQjabTSUlJZeV/0IV5TL//x6h8pZf+N7Ky3TxYyvKXVJSUumYVjZ+rrg4d+myi1WUtXPnzsrNzdWXX36pVatW6Z577tEtt9yi//u//6v09ePj45Wamqp69eopLi5OjRo1Urt27bRx40alpqZW+dNKF+YtfX+u7BOhoaHq3bu3Fi5cqB49eujDDz/UmDFjHOs/+ugjPfnkk3r11VfVs2dP+fv76+WXX9aWLVtcylU6theP+a81efJkrVixQq+88opatWolX19f3X333SouLnbarkGDBmXyVPb3X1JSohkzZjjdN1bKx8enypkr2qcuNU4X5r3Uv8ULlZSUaMyYMZo4cWKZdS1atKhSbtQOygxqTOn/Vi+cb926ta6//nqdP39ehw8fVq9evVx+vuuuu04lJSVau3atbrnlljLrO3furI8//liRkZGqW7fqu7a3t7fTWYbKdOjQQatXr9b//M//lFkXExOjc+fOacuWLYqNjZUkHT16VNnZ2Wrbtm2VM17MlTEtzTljxoxy19erV6/S9x0TE6MNGzY4Ldu0aZPatGlTpkRVJCAgQEOHDtXQoUN1991369Zbb9XPP/9c6Sd54uPj9c4776hu3bqOfSAuLk6LFi1SdnZ2hWdmXHl/VXH//ffr6aef1n333ae9e/fq3nvvdaxbv369YmNjNXbsWMeyC88SVqZt27Y6d+6c0tLSHGdAsrKyLuv3opS3P69fv16jRo3SnXfeKUk6efJkuTfDXuzaa6/V119/7bQsLS3Nab5z587KyspSq1atLivTr9GqVSvVq1dPGzZs0LBhwyRJZ8+eVVpamqPgxsTElPk4+MXHqM6dO2vXrl0VZodn4jITakxeXp4mTZqkrKwsffjhh3rjjTf0+OOPq02bNrr//vs1YsQILVmyRLm5udq6dav+8pe/aNmyZZd8vsjISI0cOVIPPvigPv30U+Xm5io1NVUfffSRJGncuHH6+eefdd999+nrr7/W999/r5UrV+rBBx+8rANnZGSkVq9erUOHDunYsWOVbj99+nR9+OGHmj59ujIzM7Vz50699NJLkqTWrVtr0KBBGj16tDZs2KDt27frgQceULNmzTRo0CCXM1XGlTGdOnWqtm7dqrFjx2rHjh3avXu35s6d6/i0V2RkpLZs2aJ9+/bpyJEj5Z6Z+MMf/qDVq1fr+eefV3Z2thYsWKA5c+boqaeecjnra6+9pkWLFmn37t3Kzs7WP/7xD4WEhLh06eTmm29WYWGhPvvsM8XHx0v6peC8//77uuaaa5wu71zMlfdXFUOGDFFBQYEee+wx9e7dW82aNXOsa9WqldLS0rRixQplZ2fr2WefLfMJm4pER0fr1ltv1ejRo7Vlyxalp6fr4YcfvqyzbOXtz61atdKSJUuUkZGh7du3a9iwYS6Nx4QJE7Rs2TIlJSUpJydHf/vb3/Tll186nRF57rnn9O677yohIUG7du1SZmamFi9erGeeeabCTL9GgwYN9Nhjj2ny5Mlavny5vvvuO40ePVqnT5/WQw89JEl69NFHtXfvXscxaeHChU6/h0iSnn76aX311VcaN26cMjIylJOTo6VLl2rChAm/OiNqFmUGNWbEiBE6c+aMbrjhBo0bN04TJkzQI488IklKTk7WiBEj9Ic//EHR0dG64447tGXLFoWHh1f4nHPnztXdd9+tsWPH6tprr9Xo0aMdH5sMCwvTxo0bdf78efXv31/t27fX448/rsDAwMv6/TSvvvqqUlJSFB4eruuvv77S7ePj4/WPf/xDS5cuVadOndSnTx+nywjJycnq0qWLbr/9dvXs2VPGGC1btqzMafNfq7IxbdOmjVauXKnt27frhhtuUM+ePfXPf/7TcRbrqaeekpeXl2JiYnTNNdeUe19H586d9dFHH2nRokVq3769nnvuOc2cOVOjRo1yOaefn5/+8pe/qGvXrurWrZv27dunZcuWufR3FBgYqOuvv15BQUGO4tKrVy+VlJRUer+MK++vKgICAjRw4EBt375d999/v9O6Rx99VEOGDNHQoUPVvXt3HT161OksjSuSk5MVHh6uuLg4DRkyRI888oiaNm3q8uPL259fe+01NWrUSLGxsRo4cKD69++vzp07V/pcN954o+bNm6ekpCR17NhRy5cv15NPPul0+ah///76/PPPlZKSom7duqlHjx5KSkpSREREhZl+rRdffFF33XWXhg8frs6dO2vPnj1asWKFGjVqJOmXy0Qff/yxPvvsM3Xs2FHz5s3TrFmznJ6jQ4cOWrt2rXJyctSrVy9df/31evbZZ50uU8Mz2YwrFxKByxQfH69OnTrxK9eBK9zo0aO1e/durV+/3t1RcBXjnhkAgMteeeUV9e3bVw0aNNCXX36pBQsW6K233nJ3LFzluMwEVKJdu3ZOH9W8cLqc38aKin3wwQeXHOd27dq5JZOn/927Y8y+/vpr9e3bV9ddd53mzZunv/71r3r44Yd/1XN6+jjD83GZCajE/v37nb424ELBwcFOv/sCVVdYWKiffvqp3HXe3t5O91zUFk//u/fEMasKTx9neD7KDAAAsDQuMwEAAEujzAAAAEujzAAAAEujzAAAAEujzAAAAEujzAAAAEujzAAAAEujzAAAAEv7f0j4O5XSIxEzAAAAAElFTkSuQmCC", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_connections_with_valid_target_node\")"]}, {"cell_type": "code", "execution_count": 15, "id": "4ed22d8e", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='active_field_boolean', ylabel='Count'>"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"active_field_boolean\")"]}, {"cell_type": "code", "execution_count": null, "id": "9bb20feb", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}