import json

from openai import OpenAI

requesty_api_key = "sk-PcLyYtnhSm2UntRhL6aECcw1ZGxqbDoeU8midASAgsSwAuh/Sy5cX0YF6/O/tkyvDFvdKJLrmJMFY4z7ha7obgkcsoPHiwQnO4icZabGGg4="


reward_weight = {
    "llm_score": 1.0,
    "n8n_workflow": 1.0,
    "valid_node_type_and_version": 1.0,
}


def load_node_types_and_versions():
    """Parse the n8n_nodes_list.txt file to extract node types and their valid versions."""
    node_types = set()
    node_versions = {}

    try:
        with open("n8n_nodes_list.txt", "r") as f:
            for line in f:
                line = line.strip()
                if line.startswith("- ") and " : " in line:
                    # Parse format: "- node_type : version1,version2,version3"
                    parts = line[2:].split(
                        " : ", 1
                    )  # Remove "- " prefix and split on " : "
                    if len(parts) == 2:
                        node_type = parts[0].strip()
                        versions_str = parts[1].strip()

                        # Parse versions (comma-separated)
                        versions = [v.strip() for v in versions_str.split(",")]

                        node_types.add(node_type)
                        node_versions[node_type] = versions

    except FileNotFoundError:
        print("Warning: n8n_nodes_list.txt not found, falling back to empty lists")

    return node_types, node_versions


VALID_N8N_NODE_TYPES, VALID_N8N_NODE_VERSIONS = load_node_types_and_versions()


### as per we determined by visualiting the result from evaluation
def reward_valid_node_type_and_version(n8n_json):
    """
    Validates that all nodes in the n8n workflow JSON have valid node types and versions.

    Args:
        n8n_json: JSON string representing an n8n workflow

    Returns:
        float: Score between 0.0 and 1.0 representing the percentage of nodes with valid types and versions
    """
    try:
        # Parse the JSON
        workflow = json.loads(n8n_json) if isinstance(n8n_json, str) else n8n_json

        if not isinstance(workflow, dict):
            return 0.0

        # Get the nodes array
        nodes = workflow.get("nodes", [])
        if not isinstance(nodes, list) or len(nodes) == 0:
            return 0.0

        valid_nodes = 0
        total_nodes = len(nodes)

        for node in nodes:
            if not isinstance(node, dict):
                continue

            node_type = node.get("type")
            node_version = node.get("typeVersion")

            # Check if node type is valid
            if not isinstance(node_type, str) or node_type not in VALID_N8N_NODE_TYPES:
                continue

            # Check if version is valid for this node type
            valid_versions = VALID_N8N_NODE_VERSIONS.get(node_type, [])
            version_str = str(node_version) if node_version is not None else ""

            if version_str in valid_versions:
                valid_nodes += 1

        # Return the percentage of valid nodes
        return valid_nodes / total_nodes if total_nodes > 0 else 0.0

    except (json.JSONDecodeError, TypeError, AttributeError):
        return 0.0


## to improve the logic behind workflow
## None could be replace by a llm but will take the vram during training and would take some time to generate the result.
def llm_score(prompt, n8n_json):
    client = OpenAI(
        api_key=requesty_api_key,
        base_url="https://router.requesty.ai/v1",
    )
    verification_prompt = f"""Evaluate if the JSON fulfills the given PROMPT.

PROMPT: {prompt}
JSON: {n8n_json}

Score 0-10 for:
1. Prompt-JSON Match: Does JSON fulfill the prompt?

Respond only with JSON:
{{"prompt_json_score": <score>, "summary": "<brief_assessment>"}}"""

    response = client.chat.completions.create(
        model="google/gemini-2.5-flash",
        messages=[
            {
                "role": "system",
                "content": "You are an expert evaluator. Respond only with valid JSON.",
            },
            {"role": "user", "content": verification_prompt},
        ],
    )

    result_text = response.choices[0].message.content.strip()
    try:
        result_data = json.loads(result_text)
    except json.JSONDecodeError:
        if "```json" in result_text:
            json_start = result_text.find("```json") + 7
            json_end = result_text.find("```", json_start)
            result_text = result_text[json_start:json_end].strip()
            result_data = json.loads(result_text)
        else:
            raise

    return result_data.get("prompt_json_score", 0.0) / 10.0


## forward reward function is to keep the n8n workflow format intacts
# Reward weights for different components
REWARDS = {
    "json_parsing": 0.05,
    "top_level_keys": 0.15,
    "workflow_name": 0.05,
    "nodes_list": 0.05,
    "nodes_non_empty": 0.05,
    "node_structure": 0.15,
    "node_types_valid": 0.15,
    "node_versions_valid": 0.10,
    "node_parameters": 0.10,
    "connections_dict": 0.05,
    "connection_structure": 0.05,
    "connection_validity": 0.05,
    "active_boolean": 0.05,
}

MAX_POSSIBLE_REWARD = sum(REWARDS.values())


def validate_node_structure(node):
    """
    Validate the structure of a single node.
    Returns a score between 0.0 and 1.0.
    """
    if not isinstance(node, dict):
        return 0.0

    score = 0.0
    required_fields = ["name", "type", "typeVersion", "position"]

    for field in required_fields:
        if field in node:
            if (
                field == "position"
                and isinstance(node[field], list)
                and len(node[field]) >= 2
            ):
                score += 0.25
            elif field == "typeVersion" and isinstance(node[field], (int, float)):
                score += 0.25
            elif (
                field in ["name", "type"]
                and isinstance(node[field], str)
                and node[field].strip()
            ):
                score += 0.25

    return score


def validate_node_version(node_type, node_type_version):
    """
    Validate if the node type version is valid for the given node type.
    Returns 1.0 if valid, 0.5 if type exists but version unknown, 0.0 if invalid.
    """
    if not isinstance(node_type, str):
        return 0.0

    # Check if node type exists in our valid types
    if node_type not in VALID_N8N_NODE_TYPES:
        return 0.0

    valid_versions = VALID_N8N_NODE_VERSIONS[node_type]

    # Convert version to string for comparison
    version_str = str(node_type_version) if node_type_version is not None else ""

    # Check if the version is in the list of valid versions
    if version_str in valid_versions:
        return 1.0

    # If version is provided but not in valid list, give partial credit
    if isinstance(node_type_version, (int, float, str)):
        return 0.3

    return 0.0


def validate_connections_structure(connections, node_names):
    """
    Validate the connections structure.
    Returns (structure_score, validity_score, total_connections).
    """
    if not isinstance(connections, dict):
        return 0.0, 0.0, 0

    # If no connections, return perfect score (empty connections are valid)
    if len(connections) == 0:
        return 1.0, 1.0, 0

    structure_score = 0.0
    validity_score = 0.0
    total_connections = 0
    valid_source_nodes = 0

    for source_node, connection_data in connections.items():
        if not isinstance(connection_data, dict):
            continue

        # Check if source node exists
        source_exists = source_node in node_names
        if source_exists:
            valid_source_nodes += 1

        for connection_type, connection_list in connection_data.items():
            if not isinstance(connection_list, list):
                continue

            for connection_group in connection_list:
                if not isinstance(connection_group, list):
                    continue

                for connection in connection_group:
                    total_connections += 1

                    if isinstance(connection, dict):
                        structure_score += 0.3  # Credit for being a dict

                        # Check required fields
                        if (
                            "node" in connection
                            and "type" in connection
                            and "index" in connection
                        ):
                            structure_score += 0.4  # Credit for having required fields

                            # Check if target node exists
                            target_node = connection.get("node")
                            if (
                                isinstance(target_node, str)
                                and target_node in node_names
                            ):
                                validity_score += (
                                    1.0  # Full credit for valid connection
                                )

    # Normalize scores
    if total_connections > 0:
        structure_score = min(structure_score / total_connections, 1.0)
        validity_score = min(validity_score / total_connections, 1.0)
    else:
        # No connections found but connections dict exists - partial credit
        structure_score = 0.5
        validity_score = 0.5

    # Factor in source node validity
    if len(connections) > 0:
        source_validity = valid_source_nodes / len(connections)
        validity_score = (validity_score + source_validity) / 2

    return structure_score, validity_score, total_connections


def reward_n8n_workflow(workflow_json_string: str) -> float:
    """
    Improved reward function for n8n workflow validation.
    Returns a score between 0.0 and 1.0 based on workflow quality.

    Args:
        workflow_json_string: JSON string or dict of the workflow to evaluate

    Returns:
        Float score between 0.0 and 1.0
    """
    reward = 0.0

    # 1. JSON Parsing
    try:
        workflow = (
            json.loads(workflow_json_string)
            if isinstance(workflow_json_string, str)
            else workflow_json_string
        )
        reward += REWARDS["json_parsing"]
    except Exception:
        return 0.0

    if not isinstance(workflow, dict):
        return 0.0

    # 2. Top-level structure validation
    required_keys = ["name", "nodes", "connections", "active"]
    present_keys = [key for key in required_keys if key in workflow]
    reward += REWARDS["top_level_keys"] * (len(present_keys) / len(required_keys))

    # 3. Workflow name validation
    workflow_name = workflow.get("name", "")
    if isinstance(workflow_name, str) and workflow_name.strip():
        reward += REWARDS["workflow_name"]

    # 4. Nodes validation
    nodes = workflow.get("nodes", [])
    node_names = set()

    if isinstance(nodes, list):
        reward += REWARDS["nodes_list"]

        if nodes:
            reward += REWARDS["nodes_non_empty"]
            # Collect node names, filtering out None values
            node_names = {
                node.get("name")
                for node in nodes
                if isinstance(node.get("name"), str) and node.get("name").strip()
            }
            # Also collect node IDs as fallback for connections
            node_ids = {
                node.get("id")
                for node in nodes
                if isinstance(node.get("id"), str) and node.get("id").strip()
            }
            # Use both names and IDs for connection validation
            node_identifiers = node_names.union(node_ids)

            # Detailed node validation
            total_structure_score = 0.0
            total_type_score = 0.0
            total_version_score = 0.0
            total_parameter_score = 0.0

            for node in nodes:
                # Structure validation
                total_structure_score += validate_node_structure(node)

                # Type validation
                node_type = node.get("type")
                if isinstance(node_type, str) and node_type in VALID_N8N_NODE_TYPES:
                    total_type_score += 1.0
                elif isinstance(node_type, str):
                    total_type_score += 0.1  # Small credit for having a type

                # Version validation
                node_type_version = node.get("typeVersion")
                if isinstance(node_type, str):
                    total_version_score += validate_node_version(
                        node_type, node_type_version
                    )

                # Parameter validation
                parameters = node.get("parameters")
                if isinstance(parameters, dict):
                    total_parameter_score += 1.0
                elif parameters is not None:
                    total_parameter_score += 0.3  # Partial credit

            # Normalize by number of nodes (with safety check)
            if len(nodes) > 0:
                reward += REWARDS["node_structure"] * (
                    total_structure_score / len(nodes)
                )
                reward += REWARDS["node_types_valid"] * (total_type_score / len(nodes))
                reward += REWARDS["node_versions_valid"] * (
                    total_version_score / len(nodes)
                )
                reward += REWARDS["node_parameters"] * (
                    total_parameter_score / len(nodes)
                )

    # 5. Connections validation
    connections = workflow.get("connections", {})

    if isinstance(connections, dict):
        reward += REWARDS["connections_dict"]

        # Use node_identifiers if available, otherwise fall back to node_names
        connection_node_refs = (
            node_identifiers if "node_identifiers" in locals() else node_names
        )
        structure_score, validity_score, _ = validate_connections_structure(
            connections, connection_node_refs
        )

        reward += REWARDS["connection_structure"] * structure_score
        reward += REWARDS["connection_validity"] * validity_score

    # 6. Active field validation
    if "active" in workflow and isinstance(workflow["active"], bool):
        reward += REWARDS["active_boolean"]

    # Final score calculation
    final_score = reward / MAX_POSSIBLE_REWARD
    return max(0.0, min(final_score, 1.0))


## final reward


def final_reward(prompt, n8n_json_str):
    reward = reward_weight["llm_score"] * llm_score(prompt, n8n_json_str)
    reward += reward_weight["n8n_workflow"] * reward_n8n_workflow(n8n_json_str)
    reward += reward_weight[
        "valid_node_type_and_version"
    ] * reward_valid_node_type_and_version(n8n_json_str)
    return reward
