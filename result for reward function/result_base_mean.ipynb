{"cells": [{"cell_type": "code", "execution_count": 1, "id": "cf9d20bd", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import seaborn as sns"]}, {"cell_type": "code", "execution_count": 2, "id": "2a29a094", "metadata": {}, "outputs": [], "source": ["df = pd.read_json(\"qwen3-14B-base-mean.json\")"]}, {"cell_type": "code", "execution_count": 3, "id": "55ed4ffb", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "json_valid", "rawType": "float64", "type": "float"}, {"name": "llm_score", "rawType": "float64", "type": "float"}, {"name": "top_level_keys_present", "rawType": "int64", "type": "integer"}, {"name": "num_nodes", "rawType": "float64", "type": "float"}, {"name": "num_nodes_with_valid_type", "rawType": "float64", "type": "float"}, {"name": "num_nodes_with_valid_version", "rawType": "float64", "type": "float"}, {"name": "num_nodes_with_valid_structure", "rawType": "float64", "type": "float"}, {"name": "num_nodes_with_parameters", "rawType": "float64", "type": "float"}, {"name": "num_connections", "rawType": "float64", "type": "float"}, {"name": "num_connections_with_valid_structure", "rawType": "float64", "type": "float"}, {"name": "num_connections_with_valid_target_node", "rawType": "float64", "type": "float"}, {"name": "percent_node_with_valid_type", "rawType": "float64", "type": "float"}, {"name": "percent_node_with_valid_version", "rawType": "float64", "type": "float"}, {"name": "percent_node_with_valid_structure", "rawType": "int64", "type": "integer"}, {"name": "percent_connections_with_valid_structure", "rawType": "float64", "type": "float"}, {"name": "percent_connections_with_valid_target_node", "rawType": "float64", "type": "float"}, {"name": "percent_node_with_parameters", "rawType": "int64", "type": "integer"}, {"name": "tries", "rawType": "float64", "type": "float"}], "ref": "8f56c8f0-945b-455c-9fd9-eee52611c951", "rows": [["0", "1.0", "5.5", "4", "7.75", "0.0", "0.0", "7.75", "7.75", "7.0", "7.0", "7.0", "0.0", "0.0", "100", "100.0", "100.0", "100", "2.3333333333"], ["1", "0.5", "2.375", "4", "12.5", "0.0", "0.0", "12.5", "12.5", "9.5", "5.5", "5.5", "0.0", "0.0", "100", "50.0", "50.0", "100", "2.6666666667000003"], ["2", "0.75", "4.5", "4", "3.0", "0.0", "0.0", "3.0", "3.0", "2.0", "2.0", "1.6666666667", "0.0", "0.0", "100", "100.0", "83.3333333333", "100", "2.0"], ["3", "0.75", "4.75", "4", "4.0", "0.0", "0.0", "4.0", "4.0", "3.0", "3.0", "3.0", "0.0", "0.0", "100", "100.0", "100.0", "100", "2.0"], ["4", "0.75", "4.0", "4", "4.0", "0.0", "0.0", "4.0", "4.0", "3.0", "2.0", "2.0", "0.0", "0.0", "100", "66.6666666667", "66.6666666667", "100", "2.0"]], "shape": {"columns": 18, "rows": 5}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>json_valid</th>\n", "      <th>llm_score</th>\n", "      <th>top_level_keys_present</th>\n", "      <th>num_nodes</th>\n", "      <th>num_nodes_with_valid_type</th>\n", "      <th>num_nodes_with_valid_version</th>\n", "      <th>num_nodes_with_valid_structure</th>\n", "      <th>num_nodes_with_parameters</th>\n", "      <th>num_connections</th>\n", "      <th>num_connections_with_valid_structure</th>\n", "      <th>num_connections_with_valid_target_node</th>\n", "      <th>percent_node_with_valid_type</th>\n", "      <th>percent_node_with_valid_version</th>\n", "      <th>percent_node_with_valid_structure</th>\n", "      <th>percent_connections_with_valid_structure</th>\n", "      <th>percent_connections_with_valid_target_node</th>\n", "      <th>percent_node_with_parameters</th>\n", "      <th>tries</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1.00</td>\n", "      <td>5.500</td>\n", "      <td>4</td>\n", "      <td>7.75</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>7.75</td>\n", "      <td>7.75</td>\n", "      <td>7.0</td>\n", "      <td>7.0</td>\n", "      <td>7.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>100</td>\n", "      <td>100.000000</td>\n", "      <td>100.000000</td>\n", "      <td>100</td>\n", "      <td>2.333333</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0.50</td>\n", "      <td>2.375</td>\n", "      <td>4</td>\n", "      <td>12.50</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>12.50</td>\n", "      <td>12.50</td>\n", "      <td>9.5</td>\n", "      <td>5.5</td>\n", "      <td>5.500000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>100</td>\n", "      <td>50.000000</td>\n", "      <td>50.000000</td>\n", "      <td>100</td>\n", "      <td>2.666667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0.75</td>\n", "      <td>4.500</td>\n", "      <td>4</td>\n", "      <td>3.00</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>3.00</td>\n", "      <td>3.00</td>\n", "      <td>2.0</td>\n", "      <td>2.0</td>\n", "      <td>1.666667</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>100</td>\n", "      <td>100.000000</td>\n", "      <td>83.333333</td>\n", "      <td>100</td>\n", "      <td>2.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0.75</td>\n", "      <td>4.750</td>\n", "      <td>4</td>\n", "      <td>4.00</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>4.00</td>\n", "      <td>4.00</td>\n", "      <td>3.0</td>\n", "      <td>3.0</td>\n", "      <td>3.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>100</td>\n", "      <td>100.000000</td>\n", "      <td>100.000000</td>\n", "      <td>100</td>\n", "      <td>2.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0.75</td>\n", "      <td>4.000</td>\n", "      <td>4</td>\n", "      <td>4.00</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>4.00</td>\n", "      <td>4.00</td>\n", "      <td>3.0</td>\n", "      <td>2.0</td>\n", "      <td>2.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>100</td>\n", "      <td>66.666667</td>\n", "      <td>66.666667</td>\n", "      <td>100</td>\n", "      <td>2.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   json_valid  llm_score  top_level_keys_present  num_nodes  \\\n", "0        1.00      5.500                       4       7.75   \n", "1        0.50      2.375                       4      12.50   \n", "2        0.75      4.500                       4       3.00   \n", "3        0.75      4.750                       4       4.00   \n", "4        0.75      4.000                       4       4.00   \n", "\n", "   num_nodes_with_valid_type  num_nodes_with_valid_version  \\\n", "0                        0.0                           0.0   \n", "1                        0.0                           0.0   \n", "2                        0.0                           0.0   \n", "3                        0.0                           0.0   \n", "4                        0.0                           0.0   \n", "\n", "   num_nodes_with_valid_structure  num_nodes_with_parameters  num_connections  \\\n", "0                            7.75                       7.75              7.0   \n", "1                           12.50                      12.50              9.5   \n", "2                            3.00                       3.00              2.0   \n", "3                            4.00                       4.00              3.0   \n", "4                            4.00                       4.00              3.0   \n", "\n", "   num_connections_with_valid_structure  \\\n", "0                                   7.0   \n", "1                                   5.5   \n", "2                                   2.0   \n", "3                                   3.0   \n", "4                                   2.0   \n", "\n", "   num_connections_with_valid_target_node  percent_node_with_valid_type  \\\n", "0                                7.000000                           0.0   \n", "1                                5.500000                           0.0   \n", "2                                1.666667                           0.0   \n", "3                                3.000000                           0.0   \n", "4                                2.000000                           0.0   \n", "\n", "   percent_node_with_valid_version  percent_node_with_valid_structure  \\\n", "0                              0.0                                100   \n", "1                              0.0                                100   \n", "2                              0.0                                100   \n", "3                              0.0                                100   \n", "4                              0.0                                100   \n", "\n", "   percent_connections_with_valid_structure  \\\n", "0                                100.000000   \n", "1                                 50.000000   \n", "2                                100.000000   \n", "3                                100.000000   \n", "4                                 66.666667   \n", "\n", "   percent_connections_with_valid_target_node  percent_node_with_parameters  \\\n", "0                                  100.000000                           100   \n", "1                                   50.000000                           100   \n", "2                                   83.333333                           100   \n", "3                                  100.000000                           100   \n", "4                                   66.666667                           100   \n", "\n", "      tries  \n", "0  2.333333  \n", "1  2.666667  \n", "2  2.000000  \n", "3  2.000000  \n", "4  2.000000  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": 4, "id": "298bdacb", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='json_valid', ylabel='Count'>"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"json_valid\")"]}, {"cell_type": "code", "execution_count": 5, "id": "aeaeeef2", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='llm_score', ylabel='Count'>"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"llm_score\", bins=20)"]}, {"cell_type": "code", "execution_count": 6, "id": "8825d978", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.int64(6)"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["(df[\"llm_score\"]>=7).sum()"]}, {"cell_type": "code", "execution_count": 7, "id": "c1ecdd0c", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='top_level_keys_present', ylabel='Count'>"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"top_level_keys_present\")"]}, {"cell_type": "code", "execution_count": 9, "id": "a18db11e", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_type', ylabel='Count'>"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_type\")"]}, {"cell_type": "code", "execution_count": 10, "id": "3c7c398e", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_version', ylabel='Count'>"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_version\")"]}, {"cell_type": "code", "execution_count": 11, "id": "4c46bdcf", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_structure', ylabel='Count'>"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_structure\")"]}, {"cell_type": "code", "execution_count": 12, "id": "44883d67", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_parameters', ylabel='Count'>"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_parameters\")"]}, {"cell_type": "code", "execution_count": 13, "id": "048db9e6", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_connections_with_valid_structure', ylabel='Count'>"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_connections_with_valid_structure\")"]}, {"cell_type": "code", "execution_count": 14, "id": "24225a83", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_connections_with_valid_target_node', ylabel='Count'>"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_connections_with_valid_target_node\")"]}, {"cell_type": "code", "execution_count": null, "id": "9bb20feb", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}