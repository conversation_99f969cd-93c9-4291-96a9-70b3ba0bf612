#!/usr/bin/env python3
"""
Simple analysis script for the merged LLM score dataset.
This script provides basic analysis without requiring external dependencies.
"""

import json
import re
from collections import Counter, defaultdict
import statistics

def load_training_data(filepath: str = "merged_training_dataset.json"):
    """Load the merged training dataset."""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            data = json.load(f)
        print(f"Loaded {len(data)} training samples")
        return data
    except Exception as e:
        print(f"Error loading training data: {e}")
        return []

def analyze_json_features(json_string: str):
    """Analyze features from the generated JSON string."""
    try:
        # Parse JSON to get structural features
        json_obj = json.loads(json_string)
        
        features = {
            'json_length': len(json_string),
            'json_valid': True,
            'num_nodes': len(json_obj.get('nodes', [])),
            'num_connections': len(json_obj.get('connections', {})),
            'has_name': bool(json_obj.get('name')),
            'has_active_field': 'active' in json_obj,
        }
        
        # Count nodes with parameters
        nodes_with_params = sum(1 for node in json_obj.get('nodes', []) if node.get('parameters'))
        features['nodes_with_parameters'] = nodes_with_params
        
        # Count different node types
        node_types = set()
        for node in json_obj.get('nodes', []):
            if node.get('type'):
                node_types.add(node['type'])
        features['unique_node_types'] = len(node_types)
        features['node_types'] = list(node_types)
        
        return features
        
    except json.JSONDecodeError:
        # JSON is invalid
        return {
            'json_length': len(json_string),
            'json_valid': False,
            'num_nodes': 0,
            'num_connections': 0,
            'has_name': False,
            'has_active_field': False,
            'nodes_with_parameters': 0,
            'unique_node_types': 0,
            'node_types': [],
        }

def analyze_prompt_features(prompt: str):
    """Analyze features from the prompt text."""
    features = {
        'prompt_length': len(prompt),
        'prompt_word_count': len(prompt.split()),
        'has_webhook': 'webhook' in prompt.lower(),
        'has_api': 'api' in prompt.lower(),
        'has_database': any(db in prompt.lower() for db in ['database', 'airtable', 'notion', 'google sheet']),
        'has_trigger': 'trigger' in prompt.lower(),
        'has_schedule': 'schedule' in prompt.lower(),
        'complexity_indicators': len(re.findall(r'\b(and|then|also|additionally|finally)\b', prompt.lower())),
    }
    return features

def analyze_score_correlations(data):
    """Analyze correlations between features and LLM scores."""
    print("\n=== Score Correlation Analysis ===")
    
    # Group data by score ranges
    score_groups = {
        'Excellent (8-10)': [],
        'Good (6-7.9)': [],
        'Fair (4-5.9)': [],
        'Poor (1-3.9)': []
    }
    
    for item in data:
        score = item['llm_score']
        if score >= 8.0:
            score_groups['Excellent (8-10)'].append(item)
        elif score >= 6.0:
            score_groups['Good (6-7.9)'].append(item)
        elif score >= 4.0:
            score_groups['Fair (4-5.9)'].append(item)
        else:
            score_groups['Poor (1-3.9)'].append(item)
    
    print("Score distribution:")
    for group, items in score_groups.items():
        print(f"  {group}: {len(items)} samples ({len(items)/len(data)*100:.1f}%)")
    
    # Analyze features by score group
    print("\nFeature analysis by score group:")
    
    for group_name, group_data in score_groups.items():
        if not group_data:
            continue
            
        print(f"\n{group_name} ({len(group_data)} samples):")
        
        # JSON validity
        valid_json = sum(1 for item in group_data if analyze_json_features(item['generated_json'])['json_valid'])
        print(f"  Valid JSON: {valid_json}/{len(group_data)} ({valid_json/len(group_data)*100:.1f}%)")
        
        # Average features
        json_lengths = []
        num_nodes = []
        num_connections = []
        prompt_lengths = []
        
        for item in group_data:
            json_features = analyze_json_features(item['generated_json'])
            prompt_features = analyze_prompt_features(item['prompt'])
            
            json_lengths.append(json_features['json_length'])
            num_nodes.append(json_features['num_nodes'])
            num_connections.append(json_features['num_connections'])
            prompt_lengths.append(prompt_features['prompt_length'])
        
        if json_lengths:
            print(f"  Avg JSON length: {statistics.mean(json_lengths):.0f} chars")
            print(f"  Avg nodes: {statistics.mean(num_nodes):.1f}")
            print(f"  Avg connections: {statistics.mean(num_connections):.1f}")
            print(f"  Avg prompt length: {statistics.mean(prompt_lengths):.0f} chars")

def analyze_common_patterns(data):
    """Analyze common patterns in high vs low scoring samples."""
    print("\n=== Pattern Analysis ===")
    
    high_score_samples = [item for item in data if item['llm_score'] >= 7.0]
    low_score_samples = [item for item in data if item['llm_score'] < 5.0]
    
    print(f"High score samples (≥7.0): {len(high_score_samples)}")
    print(f"Low score samples (<5.0): {len(low_score_samples)}")
    
    # Analyze node types
    high_score_node_types = Counter()
    low_score_node_types = Counter()
    
    for item in high_score_samples:
        json_features = analyze_json_features(item['generated_json'])
        for node_type in json_features['node_types']:
            high_score_node_types[node_type] += 1
    
    for item in low_score_samples:
        json_features = analyze_json_features(item['generated_json'])
        for node_type in json_features['node_types']:
            low_score_node_types[node_type] += 1
    
    print("\nMost common node types in high-scoring workflows:")
    for node_type, count in high_score_node_types.most_common(10):
        print(f"  {node_type}: {count}")
    
    print("\nMost common node types in low-scoring workflows:")
    for node_type, count in low_score_node_types.most_common(10):
        print(f"  {node_type}: {count}")
    
    # Analyze prompt keywords
    high_score_keywords = Counter()
    low_score_keywords = Counter()
    
    for item in high_score_samples:
        words = re.findall(r'\b\w+\b', item['prompt'].lower())
        for word in words:
            if len(word) > 3:  # Skip short words
                high_score_keywords[word] += 1
    
    for item in low_score_samples:
        words = re.findall(r'\b\w+\b', item['prompt'].lower())
        for word in words:
            if len(word) > 3:  # Skip short words
                low_score_keywords[word] += 1
    
    print("\nMost common keywords in high-scoring prompts:")
    for word, count in high_score_keywords.most_common(15):
        print(f"  {word}: {count}")

def analyze_source_files(data):
    """Analyze performance by source file."""
    print("\n=== Source File Analysis ===")
    
    source_stats = defaultdict(list)
    
    for item in data:
        source_stats[item['source_file']].append(item['llm_score'])
    
    print("Performance by source file:")
    for source, scores in sorted(source_stats.items()):
        avg_score = statistics.mean(scores)
        min_score = min(scores)
        max_score = max(scores)
        print(f"  {source}:")
        print(f"    Samples: {len(scores)}")
        print(f"    Avg score: {avg_score:.2f}")
        print(f"    Range: {min_score:.1f} - {max_score:.1f}")

def generate_insights(data):
    """Generate actionable insights from the analysis."""
    print("\n=== Key Insights ===")
    
    # Overall statistics
    scores = [item['llm_score'] for item in data]
    avg_score = statistics.mean(scores)
    
    print(f"1. Dataset Overview:")
    print(f"   - {len(data)} total samples")
    print(f"   - Average score: {avg_score:.2f}/10.0")
    print(f"   - Score range: {min(scores):.1f} - {max(scores):.1f}")
    
    # JSON validity impact
    valid_json_scores = []
    invalid_json_scores = []
    
    for item in data:
        json_features = analyze_json_features(item['generated_json'])
        if json_features['json_valid']:
            valid_json_scores.append(item['llm_score'])
        else:
            invalid_json_scores.append(item['llm_score'])
    
    if valid_json_scores and invalid_json_scores:
        print(f"\n2. JSON Validity Impact:")
        print(f"   - Valid JSON avg score: {statistics.mean(valid_json_scores):.2f}")
        print(f"   - Invalid JSON avg score: {statistics.mean(invalid_json_scores):.2f}")
        print(f"   - Difference: {statistics.mean(valid_json_scores) - statistics.mean(invalid_json_scores):.2f} points")
    
    # Complexity analysis
    high_complexity = []
    low_complexity = []
    
    for item in data:
        json_features = analyze_json_features(item['generated_json'])
        if json_features['num_nodes'] >= 5:
            high_complexity.append(item['llm_score'])
        else:
            low_complexity.append(item['llm_score'])
    
    if high_complexity and low_complexity:
        print(f"\n3. Workflow Complexity Impact:")
        print(f"   - High complexity (≥5 nodes) avg score: {statistics.mean(high_complexity):.2f}")
        print(f"   - Low complexity (<5 nodes) avg score: {statistics.mean(low_complexity):.2f}")
    
    print(f"\n4. Recommendations for Model Training:")
    print(f"   - Focus on JSON validity as a key feature")
    print(f"   - Include workflow complexity metrics")
    print(f"   - Consider prompt length and keyword analysis")
    print(f"   - Balance dataset across score ranges if possible")

def main():
    """Main analysis function."""
    print("=== LLM Score Dataset Analysis ===")
    
    # Load data
    data = load_training_data()
    if not data:
        print("No training data available. Please run merge_datasets.py first.")
        return
    
    # Run analyses
    analyze_score_correlations(data)
    analyze_common_patterns(data)
    analyze_source_files(data)
    generate_insights(data)
    
    print("\n=== Analysis Complete ===")
    print("This analysis provides insights for building an effective LLM score prediction model.")
    print("Key factors to consider: JSON validity, workflow complexity, and prompt characteristics.")

if __name__ == "__main__":
    main()
