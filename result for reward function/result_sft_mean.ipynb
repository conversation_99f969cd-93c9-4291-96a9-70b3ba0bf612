{"cells": [{"cell_type": "code", "execution_count": 18, "id": "cf9d20bd", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import seaborn as sns"]}, {"cell_type": "code", "execution_count": 19, "id": "2a29a094", "metadata": {}, "outputs": [], "source": ["df = pd.read_json(\"qwen3-14B-sft-mean.json\")"]}, {"cell_type": "code", "execution_count": 20, "id": "55ed4ffb", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "json_valid", "rawType": "float64", "type": "float"}, {"name": "tries", "rawType": "float64", "type": "float"}, {"name": "llm_score", "rawType": "float64", "type": "float"}, {"name": "json_parsed", "rawType": "float64", "type": "float"}, {"name": "top_level_keys_present", "rawType": "float64", "type": "float"}, {"name": "workflow_name_valid", "rawType": "float64", "type": "float"}, {"name": "num_nodes", "rawType": "float64", "type": "float"}, {"name": "num_nodes_with_valid_type", "rawType": "float64", "type": "float"}, {"name": "num_nodes_with_valid_version", "rawType": "float64", "type": "float"}, {"name": "num_nodes_with_valid_structure", "rawType": "float64", "type": "float"}, {"name": "num_nodes_with_parameters", "rawType": "float64", "type": "float"}, {"name": "num_connections", "rawType": "float64", "type": "float"}, {"name": "num_connections_with_valid_structure", "rawType": "float64", "type": "float"}, {"name": "num_connections_with_valid_target_node", "rawType": "float64", "type": "float"}, {"name": "active_field_boolean", "rawType": "float64", "type": "float"}, {"name": "percent_node_with_valid_type", "rawType": "float64", "type": "float"}, {"name": "percent_node_with_valid_version", "rawType": "float64", "type": "float"}, {"name": "percent_node_with_valid_structure", "rawType": "float64", "type": "float"}, {"name": "percent_connections_with_valid_structure", "rawType": "float64", "type": "float"}, {"name": "percent_connections_with_valid_target_node", "rawType": "float64", "type": "float"}, {"name": "percent_node_with_parameters", "rawType": "float64", "type": "float"}], "ref": "b9e86010-e415-441d-99af-8692e035ddb4", "rows": [["0", "1.0", "1.0", "8.5", "1.0", "4.0", "1.0", "7.25", "6.25", "6.25", "7.25", "7.25", "6.5", "6.5", "6.5", "1.0", "86.1607142857", "86.1607142857", "100.0", "100.0", "100.0", "100.0"], ["1", "0.75", "1.75", "3.75", "1.0", "4.0", "1.0", "18.0", "17.0", "17.0", "18.0", "18.0", "12.0", "12.0", "12.0", "1.0", "94.2773600668", "94.2773600668", "100.0", "100.0", "100.0", "100.0"], ["2", "1.0", "1.25", "9.0", "1.0", "4.0", "1.0", "4.75", "4.25", "4.25", "4.75", "4.75", "3.75", "3.75", "3.75", "1.0", "88.75", "88.75", "100.0", "100.0", "100.0", "100.0"], ["3", "1.0", "1.5", "3.75", "1.0", "4.0", "1.0", "4.0", "4.0", "4.0", "4.0", "4.0", "3.0", "3.0", "3.0", "1.0", "100.0", "100.0", "100.0", "100.0", "100.0", "100.0"], ["4", "1.0", "2.5", "10.0", "1.0", "4.0", "1.0", "4.0", "4.0", "4.0", "4.0", "4.0", "3.0", "3.0", "3.0", "1.0", "100.0", "100.0", "100.0", "100.0", "100.0", "100.0"]], "shape": {"columns": 21, "rows": 5}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>json_valid</th>\n", "      <th>tries</th>\n", "      <th>llm_score</th>\n", "      <th>json_parsed</th>\n", "      <th>top_level_keys_present</th>\n", "      <th>workflow_name_valid</th>\n", "      <th>num_nodes</th>\n", "      <th>num_nodes_with_valid_type</th>\n", "      <th>num_nodes_with_valid_version</th>\n", "      <th>num_nodes_with_valid_structure</th>\n", "      <th>...</th>\n", "      <th>num_connections</th>\n", "      <th>num_connections_with_valid_structure</th>\n", "      <th>num_connections_with_valid_target_node</th>\n", "      <th>active_field_boolean</th>\n", "      <th>percent_node_with_valid_type</th>\n", "      <th>percent_node_with_valid_version</th>\n", "      <th>percent_node_with_valid_structure</th>\n", "      <th>percent_connections_with_valid_structure</th>\n", "      <th>percent_connections_with_valid_target_node</th>\n", "      <th>percent_node_with_parameters</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1.00</td>\n", "      <td>1.00</td>\n", "      <td>8.50</td>\n", "      <td>1.0</td>\n", "      <td>4.0</td>\n", "      <td>1.0</td>\n", "      <td>7.25</td>\n", "      <td>6.25</td>\n", "      <td>6.25</td>\n", "      <td>7.25</td>\n", "      <td>...</td>\n", "      <td>6.50</td>\n", "      <td>6.50</td>\n", "      <td>6.50</td>\n", "      <td>1.0</td>\n", "      <td>86.160714</td>\n", "      <td>86.160714</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0.75</td>\n", "      <td>1.75</td>\n", "      <td>3.75</td>\n", "      <td>1.0</td>\n", "      <td>4.0</td>\n", "      <td>1.0</td>\n", "      <td>18.00</td>\n", "      <td>17.00</td>\n", "      <td>17.00</td>\n", "      <td>18.00</td>\n", "      <td>...</td>\n", "      <td>12.00</td>\n", "      <td>12.00</td>\n", "      <td>12.00</td>\n", "      <td>1.0</td>\n", "      <td>94.277360</td>\n", "      <td>94.277360</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1.00</td>\n", "      <td>1.25</td>\n", "      <td>9.00</td>\n", "      <td>1.0</td>\n", "      <td>4.0</td>\n", "      <td>1.0</td>\n", "      <td>4.75</td>\n", "      <td>4.25</td>\n", "      <td>4.25</td>\n", "      <td>4.75</td>\n", "      <td>...</td>\n", "      <td>3.75</td>\n", "      <td>3.75</td>\n", "      <td>3.75</td>\n", "      <td>1.0</td>\n", "      <td>88.750000</td>\n", "      <td>88.750000</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1.00</td>\n", "      <td>1.50</td>\n", "      <td>3.75</td>\n", "      <td>1.0</td>\n", "      <td>4.0</td>\n", "      <td>1.0</td>\n", "      <td>4.00</td>\n", "      <td>4.00</td>\n", "      <td>4.00</td>\n", "      <td>4.00</td>\n", "      <td>...</td>\n", "      <td>3.00</td>\n", "      <td>3.00</td>\n", "      <td>3.00</td>\n", "      <td>1.0</td>\n", "      <td>100.000000</td>\n", "      <td>100.000000</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1.00</td>\n", "      <td>2.50</td>\n", "      <td>10.00</td>\n", "      <td>1.0</td>\n", "      <td>4.0</td>\n", "      <td>1.0</td>\n", "      <td>4.00</td>\n", "      <td>4.00</td>\n", "      <td>4.00</td>\n", "      <td>4.00</td>\n", "      <td>...</td>\n", "      <td>3.00</td>\n", "      <td>3.00</td>\n", "      <td>3.00</td>\n", "      <td>1.0</td>\n", "      <td>100.000000</td>\n", "      <td>100.000000</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "      <td>100.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 21 columns</p>\n", "</div>"], "text/plain": ["   json_valid  tries  llm_score  json_parsed  top_level_keys_present  \\\n", "0        1.00   1.00       8.50          1.0                     4.0   \n", "1        0.75   1.75       3.75          1.0                     4.0   \n", "2        1.00   1.25       9.00          1.0                     4.0   \n", "3        1.00   1.50       3.75          1.0                     4.0   \n", "4        1.00   2.50      10.00          1.0                     4.0   \n", "\n", "   workflow_name_valid  num_nodes  num_nodes_with_valid_type  \\\n", "0                  1.0       7.25                       6.25   \n", "1                  1.0      18.00                      17.00   \n", "2                  1.0       4.75                       4.25   \n", "3                  1.0       4.00                       4.00   \n", "4                  1.0       4.00                       4.00   \n", "\n", "   num_nodes_with_valid_version  num_nodes_with_valid_structure  ...  \\\n", "0                          6.25                            7.25  ...   \n", "1                         17.00                           18.00  ...   \n", "2                          4.25                            4.75  ...   \n", "3                          4.00                            4.00  ...   \n", "4                          4.00                            4.00  ...   \n", "\n", "   num_connections  num_connections_with_valid_structure  \\\n", "0             6.50                                  6.50   \n", "1            12.00                                 12.00   \n", "2             3.75                                  3.75   \n", "3             3.00                                  3.00   \n", "4             3.00                                  3.00   \n", "\n", "   num_connections_with_valid_target_node  active_field_boolean  \\\n", "0                                    6.50                   1.0   \n", "1                                   12.00                   1.0   \n", "2                                    3.75                   1.0   \n", "3                                    3.00                   1.0   \n", "4                                    3.00                   1.0   \n", "\n", "   percent_node_with_valid_type  percent_node_with_valid_version  \\\n", "0                     86.160714                        86.160714   \n", "1                     94.277360                        94.277360   \n", "2                     88.750000                        88.750000   \n", "3                    100.000000                       100.000000   \n", "4                    100.000000                       100.000000   \n", "\n", "   percent_node_with_valid_structure  \\\n", "0                              100.0   \n", "1                              100.0   \n", "2                              100.0   \n", "3                              100.0   \n", "4                              100.0   \n", "\n", "   percent_connections_with_valid_structure  \\\n", "0                                     100.0   \n", "1                                     100.0   \n", "2                                     100.0   \n", "3                                     100.0   \n", "4                                     100.0   \n", "\n", "   percent_connections_with_valid_target_node  percent_node_with_parameters  \n", "0                                       100.0                         100.0  \n", "1                                       100.0                         100.0  \n", "2                                       100.0                         100.0  \n", "3                                       100.0                         100.0  \n", "4                                       100.0                         100.0  \n", "\n", "[5 rows x 21 columns]"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": 21, "id": "298bdacb", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='json_valid', ylabel='Count'>"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"json_valid\")"]}, {"cell_type": "code", "execution_count": 34, "id": "3a6dd530", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.int64(24)"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["(df[\"json_valid\"]==1.0).sum()"]}, {"cell_type": "code", "execution_count": 23, "id": "aeaeeef2", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='llm_score', ylabel='Count'>"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"llm_score\", bins=20)"]}, {"cell_type": "code", "execution_count": 24, "id": "8825d978", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.int64(16)"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["(df[\"llm_score\"]>=7).sum()"]}, {"cell_type": "code", "execution_count": 25, "id": "c1ecdd0c", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='top_level_keys_present', ylabel='Count'>"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"top_level_keys_present\")"]}, {"cell_type": "code", "execution_count": 26, "id": "3b2bfd01", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='workflow_name_valid', ylabel='Count'>"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"workflow_name_valid\")"]}, {"cell_type": "code", "execution_count": 27, "id": "a18db11e", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_type', ylabel='Count'>"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_type\")"]}, {"cell_type": "code", "execution_count": 28, "id": "3c7c398e", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_version', ylabel='Count'>"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_version\")"]}, {"cell_type": "code", "execution_count": 29, "id": "4c46bdcf", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_valid_structure', ylabel='Count'>"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_valid_structure\")"]}, {"cell_type": "code", "execution_count": 30, "id": "44883d67", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_node_with_parameters', ylabel='Count'>"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_node_with_parameters\")"]}, {"cell_type": "code", "execution_count": 31, "id": "048db9e6", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_connections_with_valid_structure', ylabel='Count'>"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_connections_with_valid_structure\")"]}, {"cell_type": "code", "execution_count": 32, "id": "24225a83", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='percent_connections_with_valid_target_node', ylabel='Count'>"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"percent_connections_with_valid_target_node\")"]}, {"cell_type": "code", "execution_count": 33, "id": "4ed22d8e", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Axes: xlabel='active_field_boolean', ylabel='Count'>"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sns.histplot(df, x=\"active_field_boolean\")"]}, {"cell_type": "code", "execution_count": null, "id": "9bb20feb", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}