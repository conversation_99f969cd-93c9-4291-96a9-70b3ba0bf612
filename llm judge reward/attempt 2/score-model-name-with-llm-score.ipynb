{"cells": [{"cell_type": "code", "execution_count": 1, "id": "3fbd3b8a", "metadata": {"_cell_guid": "b1076dfc-b9ad-4769-8c92-a6c4dae69d19", "_uuid": "8f2839f25d086af736a60e9eeb907d3b93b6e0e5", "execution": {"iopub.execute_input": "2025-08-05T07:18:03.141248Z", "iopub.status.busy": "2025-08-05T07:18:03.140649Z", "iopub.status.idle": "2025-08-05T07:24:02.869034Z", "shell.execute_reply": "2025-08-05T07:24:02.868271Z"}, "papermill": {"duration": 359.73311, "end_time": "2025-08-05T07:24:02.871049", "exception": false, "start_time": "2025-08-05T07:18:03.137939", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting pip3-autoremove\r\n", "  Downloading pip3_autoremove-1.2.2-py2.py3-none-any.whl.metadata (2.2 kB)\r\n", "Requirement already satisfied: pip in /usr/local/lib/python3.11/dist-packages (from pip3-autoremove) (24.1.2)\r\n", "Requirement already satisfied: setuptools in /usr/local/lib/python3.11/dist-packages (from pip3-autoremove) (75.2.0)\r\n", "Downloading pip3_autoremove-1.2.2-py2.py3-none-any.whl (6.7 kB)\r\n", "Installing collected packages: pip3-autoremove\r\n", "Successfully installed pip3-autoremove-1.2.2\r\n", "Looking in indexes: https://download.pytorch.org/whl/cu124\r\n", "Requirement already satisfied: torch in /usr/local/lib/python3.11/dist-packages (2.6.0+cu124)\r\n", "Requirement already satisfied: torchvision in /usr/local/lib/python3.11/dist-packages (0.21.0+cu124)\r\n", "Requirement already satisfied: torchaudio in /usr/local/lib/python3.11/dist-packages (2.6.0+cu124)\r\n", "Collecting xformers\r\n", "  Downloading https://download.pytorch.org/whl/cu124/xformers-0.0.29.post3-cp311-cp311-manylinux_2_28_x86_64.whl.metadata (1.0 kB)\r\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.11/dist-packages (from torch) (3.18.0)\r\n", "Requirement already satisfied: typing-extensions>=4.10.0 in /usr/local/lib/python3.11/dist-packages (from torch) (4.14.0)\r\n", "Requirement already satisfied: networkx in /usr/local/lib/python3.11/dist-packages (from torch) (3.5)\r\n", "Requirement already satisfied: jinja2 in /usr/local/lib/python3.11/dist-packages (from torch) (3.1.6)\r\n", "Requirement already satisfied: fsspec in /usr/local/lib/python3.11/dist-packages (from torch) (2025.5.1)\r\n", "Collecting nvidia-cuda-nvrtc-cu12==12.4.127 (from torch)\r\n", "  Downloading https://download.pytorch.org/whl/cu124/nvidia_cuda_nvrtc_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl (24.6 MB)\r\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m24.6/24.6 MB\u001b[0m \u001b[31m68.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hCollecting nvidia-cuda-runtime-cu12==12.4.127 (from torch)\r\n", "  Downloading https://download.pytorch.org/whl/cu124/nvidia_cuda_runtime_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl (883 kB)\r\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m883.7/883.7 kB\u001b[0m \u001b[31m40.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hCollecting nvidia-cuda-cupti-cu12==12.4.127 (from torch)\r\n", "  Downloading https://download.pytorch.org/whl/cu124/nvidia_cuda_cupti_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl (13.8 MB)\r\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m13.8/13.8 MB\u001b[0m \u001b[31m86.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hCollecting nvidia-cudnn-cu12==9.1.0.70 (from torch)\r\n", "  Downloading https://download.pytorch.org/whl/cu124/nvidia_cudnn_cu12-9.1.0.70-py3-none-manylinux2014_x86_64.whl (664.8 MB)\r\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m664.8/664.8 MB\u001b[0m \u001b[31m2.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hCollecting nvidia-cublas-cu12==12.4.5.8 (from torch)\r\n", "  Downloading https://download.pytorch.org/whl/cu124/nvidia_cublas_cu12-12.4.5.8-py3-none-manylinux2014_x86_64.whl (363.4 MB)\r\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m363.4/363.4 MB\u001b[0m \u001b[31m5.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hCollecting nvidia-cufft-cu12==11.2.1.3 (from torch)\r\n", "  Downloading https://download.pytorch.org/whl/cu124/nvidia_cufft_cu12-11.2.1.3-py3-none-manylinux2014_x86_64.whl (211.5 MB)\r\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m211.5/211.5 MB\u001b[0m \u001b[31m7.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hCollecting nvidia-curand-cu12==10.3.5.147 (from torch)\r\n", "  Downloading https://download.pytorch.org/whl/cu124/nvidia_curand_cu12-10.3.5.147-py3-none-manylinux2014_x86_64.whl (56.3 MB)\r\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m56.3/56.3 MB\u001b[0m \u001b[31m27.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hCollecting nvidia-cusolver-cu12==11.6.1.9 (from torch)\r\n", "  Downloading https://download.pytorch.org/whl/cu124/nvidia_cusolver_cu12-11.6.1.9-py3-none-manylinux2014_x86_64.whl (127.9 MB)\r\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m127.9/127.9 MB\u001b[0m \u001b[31m13.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hCollecting nvidia-cusparse-cu12==12.3.1.170 (from torch)\r\n", "  Downloading https://download.pytorch.org/whl/cu124/nvidia_cusparse_cu12-12.3.1.170-py3-none-manylinux2014_x86_64.whl (207.5 MB)\r\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m207.5/207.5 MB\u001b[0m \u001b[31m8.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hRequirement already satisfied: nvidia-cusparselt-cu12==0.6.2 in /usr/local/lib/python3.11/dist-packages (from torch) (0.6.2)\r\n", "Requirement already satisfied: nvidia-nccl-cu12==2.21.5 in /usr/local/lib/python3.11/dist-packages (from torch) (2.21.5)\r\n", "Requirement already satisfied: nvidia-nvtx-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch) (12.4.127)\r\n", "Collecting nvidia-nvjitlink-cu12==12.4.127 (from torch)\r\n", "  Downloading https://download.pytorch.org/whl/cu124/nvidia_nvjitlink_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl (21.1 MB)\r\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m21.1/21.1 MB\u001b[0m \u001b[31m79.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hRequirement already satisfied: triton==3.2.0 in /usr/local/lib/python3.11/dist-packages (from torch) (3.2.0)\r\n", "Requirement already satisfied: sympy==1.13.1 in /usr/local/lib/python3.11/dist-packages (from torch) (1.13.1)\r\n", "Requirement already satisfied: mpmath<1.4,>=1.1.0 in /usr/local/lib/python3.11/dist-packages (from sympy==1.13.1->torch) (1.3.0)\r\n", "Requirement already satisfied: numpy in /usr/local/lib/python3.11/dist-packages (from torchvision) (1.26.4)\r\n", "Requirement already satisfied: pillow!=8.3.*,>=5.3.0 in /usr/local/lib/python3.11/dist-packages (from torchvision) (11.2.1)\r\n", "Requirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.11/dist-packages (from jinja2->torch) (3.0.2)\r\n", "Requirement already satisfied: mkl_fft in /usr/local/lib/python3.11/dist-packages (from numpy->torchvision) (1.3.8)\r\n", "Requirement already satisfied: mkl_random in /usr/local/lib/python3.11/dist-packages (from numpy->torchvision) (1.2.4)\r\n", "Requirement already satisfied: mkl_umath in /usr/local/lib/python3.11/dist-packages (from numpy->torchvision) (0.1.1)\r\n", "Requirement already satisfied: mkl in /usr/local/lib/python3.11/dist-packages (from numpy->torchvision) (2025.2.0)\r\n", "Requirement already satisfied: tbb4py in /usr/local/lib/python3.11/dist-packages (from numpy->torchvision) (2022.2.0)\r\n", "Requirement already satisfied: mkl-service in /usr/local/lib/python3.11/dist-packages (from numpy->torchvision) (2.4.1)\r\n", "Requirement already satisfied: intel-openmp<2026,>=2024 in /usr/local/lib/python3.11/dist-packages (from mkl->numpy->torchvision) (2024.2.0)\r\n", "Requirement already satisfied: tbb==2022.* in /usr/local/lib/python3.11/dist-packages (from mkl->numpy->torchvision) (2022.2.0)\r\n", "Requirement already satisfied: tcmlib==1.* in /usr/local/lib/python3.11/dist-packages (from tbb==2022.*->mkl->numpy->torchvision) (1.4.0)\r\n", "Requirement already satisfied: intel-cmplr-lib-rt in /usr/local/lib/python3.11/dist-packages (from mkl_umath->numpy->torchvision) (2024.2.0)\r\n", "Requirement already satisfied: intel-cmplr-lib-ur==2024.2.0 in /usr/local/lib/python3.11/dist-packages (from intel-openmp<2026,>=2024->mkl->numpy->torchvision) (2024.2.0)\r\n", "Downloading https://download.pytorch.org/whl/cu124/xformers-0.0.29.post3-cp311-cp311-manylinux_2_28_x86_64.whl (43.4 MB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m43.4/43.4 MB\u001b[0m \u001b[31m15.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hInstalling collected packages: nvidia-nvjitlink-cu12, nvidia-curand-cu12, nvidia-cufft-cu12, nvidia-cuda-runtime-cu12, nvidia-cuda-nvrtc-cu12, nvidia-cuda-cupti-cu12, nvidia-cublas-cu12, nvidia-cusparse-cu12, nvidia-cudnn-cu12, nvidia-cusolver-cu12, xformers\r\n", "  Attempting uninstall: nvidia-nvjitlink-cu12\r\n", "    Found existing installation: nvidia-nvjitlink-cu12 12.5.82\r\n", "    Uninstalling nvidia-nvjitlink-cu12-12.5.82:\r\n", "      Successfully uninstalled nvidia-nvjitlink-cu12-12.5.82\r\n", "  Attempting uninstall: nvidia-curand-cu12\r\n", "    Found existing installation: nvidia-curand-cu12 10.3.6.82\r\n", "    Uninstalling nvidia-curand-cu12-10.3.6.82:\r\n", "      Successfully uninstalled nvidia-curand-cu12-10.3.6.82\r\n", "  Attempting uninstall: nvidia-cufft-cu12\r\n", "    Found existing installation: nvidia-cufft-cu12 11.2.3.61\r\n", "    Uninstalling nvidia-cufft-cu12-11.2.3.61:\r\n", "      Successfully uninstalled nvidia-cufft-cu12-11.2.3.61\r\n", "  Attempting uninstall: nvidia-cuda-runtime-cu12\r\n", "    Found existing installation: nvidia-cuda-runtime-cu12 12.5.82\r\n", "    Uninstalling nvidia-cuda-runtime-cu12-12.5.82:\r\n", "      Successfully uninstalled nvidia-cuda-runtime-cu12-12.5.82\r\n", "  Attempting uninstall: nvidia-cuda-nvrtc-cu12\r\n", "    Found existing installation: nvidia-cuda-nvrtc-cu12 12.5.82\r\n", "    Uninstalling nvidia-cuda-nvrtc-cu12-12.5.82:\r\n", "      Successfully uninstalled nvidia-cuda-nvrtc-cu12-12.5.82\r\n", "  Attempting uninstall: nvidia-cuda-cupti-cu12\r\n", "    Found existing installation: nvidia-cuda-cupti-cu12 12.5.82\r\n", "    Uninstalling nvidia-cuda-cupti-cu12-12.5.82:\r\n", "      Successfully uninstalled nvidia-cuda-cupti-cu12-12.5.82\r\n", "  Attempting uninstall: nvidia-cublas-cu12\r\n", "    Found existing installation: nvidia-cublas-cu12 12.5.3.2\r\n", "    Uninstalling nvidia-cublas-cu12-12.5.3.2:\r\n", "      Successfully uninstalled nvidia-cublas-cu12-12.5.3.2\r\n", "  Attempting uninstall: nvidia-cusparse-cu12\r\n", "    Found existing installation: nvidia-cusparse-cu12 12.5.1.3\r\n", "    Uninstalling nvidia-cusparse-cu12-12.5.1.3:\r\n", "      Successfully uninstalled nvidia-cusparse-cu12-12.5.1.3\r\n", "  Attempting uninstall: nvidia-cudnn-cu12\r\n", "    Found existing installation: nvidia-cudnn-cu12 9.3.0.75\r\n", "    Uninstalling nvidia-cudnn-cu12-9.3.0.75:\r\n", "      Successfully uninstalled nvidia-cudnn-cu12-9.3.0.75\r\n", "  Attempting uninstall: nvidia-cusolver-cu12\r\n", "    Found existing installation: nvidia-cusolver-cu12 11.6.3.83\r\n", "    Uninstalling nvidia-cusolver-cu12-11.6.3.83:\r\n", "      Successfully uninstalled nvidia-cusolver-cu12-11.6.3.83\r\n", "Successfully installed nvidia-cublas-cu12-12.4.5.8 nvidia-cuda-cupti-cu12-12.4.127 nvidia-cuda-nvrtc-cu12-12.4.127 nvidia-cuda-runtime-cu12-12.4.127 nvidia-cudnn-cu12-9.1.0.70 nvidia-cufft-cu12-11.2.1.3 nvidia-curand-cu12-10.3.5.147 nvidia-cusolver-cu12-11.6.1.9 nvidia-cusparse-cu12-12.3.1.170 nvidia-nvjitlink-cu12-12.4.127 xformers-0.0.29.post3\r\n", "Collecting unsloth\r\n", "  Downloading unsloth-2025.8.1-py3-none-any.whl.metadata (47 kB)\r\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m47.3/47.3 kB\u001b[0m \u001b[31m1.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hCollecting vllm\r\n", "  Downloading vllm-0.10.0-cp38-abi3-manylinux1_x86_64.whl.metadata (14 kB)\r\n", "Collecting unsloth_zoo>=2025.8.1 (from unsloth)\r\n", "  Downloading unsloth_zoo-2025.8.1-py3-none-any.whl.metadata (8.1 kB)\r\n", "Requirement already satisfied: torch>=2.4.0 in /usr/local/lib/python3.11/dist-packages (from unsloth) (2.6.0+cu124)\r\n", "Requirement already satisfied: xformers>=0.0.27.post2 in /usr/local/lib/python3.11/dist-packages (from unsloth) (0.0.29.post3)\r\n", "Collecting bitsandbytes (from unsloth)\r\n", "  Downloading bitsandbytes-0.46.1-py3-none-manylinux_2_24_x86_64.whl.metadata (10 kB)\r\n", "Requirement already satisfied: triton>=3.0.0 in /usr/local/lib/python3.11/dist-packages (from unsloth) (3.2.0)\r\n", "Requirement already satisfied: packaging in /usr/local/lib/python3.11/dist-packages (from unsloth) (25.0)\r\n", "Collecting tyro (from unsloth)\r\n", "  Downloading tyro-0.9.27-py3-none-any.whl.metadata (11 kB)\r\n", "Requirement already satisfied: transformers!=4.47.0,!=4.52.0,!=4.52.1,!=4.52.2,!=4.52.3,!=4.53.0,>=4.51.3 in /usr/local/lib/python3.11/dist-packages (from unsloth) (4.52.4)\r\n", "Requirement already satisfied: datasets<4.0.0,>=3.4.1 in /usr/local/lib/python3.11/dist-packages (from unsloth) (3.6.0)\r\n", "Requirement already satisfied: sentencepiece>=0.2.0 in /usr/local/lib/python3.11/dist-packages (from unsloth) (0.2.0)\r\n", "Requirement already satisfied: tqdm in /usr/local/lib/python3.11/dist-packages (from unsloth) (4.67.1)\r\n", "Requirement already satisfied: psutil in /usr/local/lib/python3.11/dist-packages (from unsloth) (7.0.0)\r\n", "Requirement already satisfied: wheel>=0.42.0 in /usr/local/lib/python3.11/dist-packages (from unsloth) (0.45.1)\r\n", "Requirement already satisfied: numpy in /usr/local/lib/python3.11/dist-packages (from unsloth) (1.26.4)\r\n", "Requirement already satisfied: accelerate>=0.34.1 in /usr/local/lib/python3.11/dist-packages (from unsloth) (1.8.1)\r\n", "Collecting trl!=0.15.0,!=0.19.0,!=0.9.0,!=0.9.1,!=0.9.2,!=0.9.3,>=0.7.9 (from unsloth)\r\n", "  Downloading trl-0.20.0-py3-none-any.whl.metadata (11 kB)\r\n", "Requirement already satisfied: peft!=0.11.0,>=0.7.1 in /usr/local/lib/python3.11/dist-packages (from unsloth) (0.15.2)\r\n", "Requirement already satisfied: protobuf in /usr/local/lib/python3.11/dist-packages (from unsloth) (3.20.3)\r\n", "Collecting huggingface_hub>=0.34.0 (from unsloth)\r\n", "  Downloading huggingface_hub-0.34.3-py3-none-any.whl.metadata (14 kB)\r\n", "Requirement already satisfied: hf_transfer in /usr/local/lib/python3.11/dist-packages (from unsloth) (0.1.9)\r\n", "Requirement already satisfied: diffusers in /usr/local/lib/python3.11/dist-packages (from unsloth) (0.34.0)\r\n", "Requirement already satisfied: torchvision in /usr/local/lib/python3.11/dist-packages (from unsloth) (0.21.0+cu124)\r\n", "Requirement already satisfied: regex in /usr/local/lib/python3.11/dist-packages (from vllm) (2024.11.6)\r\n", "Requirement already satisfied: cachetools in /usr/local/lib/python3.11/dist-packages (from vllm) (5.5.2)\r\n", "Requirement already satisfied: requests>=2.26.0 in /usr/local/lib/python3.11/dist-packages (from vllm) (2.32.4)\r\n", "Collecting blake3 (from vllm)\r\n", "  Downloading blake3-1.0.5-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (4.2 kB)\r\n", "Requirement already satisfied: py-cpuinfo in /usr/local/lib/python3.11/dist-packages (from vllm) (9.0.0)\r\n", "Collecting transformers!=4.47.0,!=4.52.0,!=4.52.1,!=4.52.2,!=4.52.3,!=4.53.0,>=4.51.3 (from unsloth)\r\n", "  Downloading transformers-4.54.1-py3-none-any.whl.metadata (41 kB)\r\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m41.7/41.7 kB\u001b[0m \u001b[31m31.7 kB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hRequirement already satisfied: tokenizers>=0.21.1 in /usr/local/lib/python3.11/dist-packages (from vllm) (0.21.2)\r\n", "Requirement already satisfied: fastapi>=0.115.0 in /usr/local/lib/python3.11/dist-packages (from fastapi[standard]>=0.115.0->vllm) (0.115.13)\r\n", "Requirement already satisfied: aiohttp in /usr/local/lib/python3.11/dist-packages (from vllm) (3.12.13)\r\n", "Collecting openai<=1.90.0,>=1.87.0 (from vllm)\r\n", "  Downloading openai-1.90.0-py3-none-any.whl.metadata (26 kB)\r\n", "Requirement already satisfied: pydantic>=2.10 in /usr/local/lib/python3.11/dist-packages (from vllm) (2.11.7)\r\n", "Requirement already satisfied: prometheus_client>=0.18.0 in /usr/local/lib/python3.11/dist-packages (from vllm) (0.22.1)\r\n", "Requirement already satisfied: pillow in /usr/local/lib/python3.11/dist-packages (from vllm) (11.2.1)\r\n", "Collecting prometheus-fastapi-instrumentator>=7.0.0 (from vllm)\r\n", "  Downloading prometheus_fastapi_instrumentator-7.1.0-py3-none-any.whl.metadata (13 kB)\r\n", "Requirement already satisfied: tiktoken>=0.6.0 in /usr/local/lib/python3.11/dist-packages (from vllm) (0.9.0)\r\n", "Collecting lm-format-enforcer<0.11,>=0.10.11 (from vllm)\r\n", "  Downloading lm_format_enforcer-0.10.12-py3-none-any.whl.metadata (17 kB)\r\n", "Collecting llguidance<0.8.0,>=0.7.11 (from vllm)\r\n", "  Downloading llguidance-0.7.30-cp39-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (10 kB)\r\n", "Collecting outlines_core==0.2.10 (from vllm)\r\n", "  Downloading outlines_core-0.2.10-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (5.8 kB)\r\n", "Collecting diskcache==5.6.3 (from vllm)\r\n", "  Downloading diskcache-5.6.3-py3-none-any.whl.metadata (20 kB)\r\n", "Collecting lark==1.2.2 (from vllm)\r\n", "  Downloading lark-1.2.2-py3-none-any.whl.metadata (1.8 kB)\r\n", "Collecting xgrammar==0.1.21 (from vllm)\r\n", "  Downloading xgrammar-0.1.21-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (6.3 kB)\r\n", "Requirement already satisfied: typing_extensions>=4.10 in /usr/local/lib/python3.11/dist-packages (from vllm) (4.14.0)\r\n", "Requirement already satisfied: filelock>=3.16.1 in /usr/local/lib/python3.11/dist-packages (from vllm) (3.18.0)\r\n", "Collecting partial-json-parser (from vllm)\r\n", "  Downloading partial_json_parser-0.2.1.1.post6-py3-none-any.whl.metadata (6.1 kB)\r\n", "Collecting pyzmq>=25.0.0 (from vllm)\r\n", "  Downloading pyzmq-27.0.1-cp311-cp311-manylinux_2_26_x86_64.manylinux_2_28_x86_64.whl.metadata (6.0 kB)\r\n", "Collecting msgspec (from vllm)\r\n", "  Downloading msgspec-0.19.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (6.9 kB)\r\n", "Collecting gguf>=0.13.0 (from vllm)\r\n", "  Downloading gguf-0.17.1-py3-none-any.whl.metadata (4.3 kB)\r\n", "Collecting mistral_common>=1.8.2 (from mistral_common[audio,image]>=1.8.2->vllm)\r\n", "  Downloading mistral_common-1.8.3-py3-none-any.whl.metadata (3.8 kB)\r\n", "Requirement already satisfied: opencv-python-headless>=4.11.0 in /usr/local/lib/python3.11/dist-packages (from vllm) (4.11.0.86)\r\n", "Requirement already satisfied: pyyaml in /usr/local/lib/python3.11/dist-packages (from vllm) (6.0.2)\r\n", "Requirement already satisfied: einops in /usr/local/lib/python3.11/dist-packages (from vllm) (0.8.1)\r\n", "Collecting compressed-tensors==0.10.2 (from vllm)\r\n", "  Downloading compressed_tensors-0.10.2-py3-none-any.whl.metadata (7.0 kB)\r\n", "Collecting depyf==0.19.0 (from vllm)\r\n", "  Downloading depyf-0.19.0-py3-none-any.whl.metadata (7.3 kB)\r\n", "Requirement already satisfied: cloudpickle in /usr/local/lib/python3.11/dist-packages (from vllm) (3.1.1)\r\n", "Collecting watchfiles (from vllm)\r\n", "  Downloading watchfiles-1.1.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (4.9 kB)\r\n", "Requirement already satisfied: python-json-logger in /usr/local/lib/python3.11/dist-packages (from vllm) (3.3.0)\r\n", "Requirement already satisfied: scipy in /usr/local/lib/python3.11/dist-packages (from vllm) (1.15.3)\r\n", "Requirement already satisfied: ninja in /usr/local/lib/python3.11/dist-packages (from vllm) (********)\r\n", "Collecting pybase64 (from vllm)\r\n", "  Downloading pybase64-1.4.2-cp311-cp311-manylinux1_x86_64.manylinux2014_x86_64.manylinux_2_17_x86_64.manylinux_2_5_x86_64.whl.metadata (8.7 kB)\r\n", "Collecting cbor2 (from vllm)\r\n", "  Downloading cbor2-5.6.5-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (6.0 kB)\r\n", "Collecting numba==0.61.2 (from vllm)\r\n", "  Downloading numba-0.61.2-cp311-cp311-manylinux2014_x86_64.manylinux_2_17_x86_64.whl.metadata (2.8 kB)\r\n", "Requirement already satisfied: ray!=2.44.*,>=2.43.0 in /usr/local/lib/python3.11/dist-packages (from ray[cgraph]!=2.44.*,>=2.43.0->vllm) (2.47.1)\r\n", "Collecting torch>=2.4.0 (from unsloth)\r\n", "  Downloading torch-2.7.1-cp311-cp311-manylinux_2_28_x86_64.whl.metadata (29 kB)\r\n", "Collecting torchaudio==2.7.1 (from vllm)\r\n", "  Downloading torchaudio-2.7.1-cp311-cp311-manylinux_2_28_x86_64.whl.metadata (6.6 kB)\r\n", "Collecting torchvision (from unsloth)\r\n", "  Downloading torchvision-0.22.1-cp311-cp311-manylinux_2_28_x86_64.whl.metadata (6.1 kB)\r\n", "Collecting xformers>=0.0.27.post2 (from unsloth)\r\n", "  Downloading xformers-0.0.31-cp39-abi3-manylinux_2_28_x86_64.whl.metadata (1.0 kB)\r\n", "Collecting astor (from depyf==0.19.0->vllm)\r\n", "  Downloading astor-0.8.1-py2.py3-none-any.whl.metadata (4.2 kB)\r\n", "Requirement already satisfied: dill in /usr/local/lib/python3.11/dist-packages (from depyf==0.19.0->vllm) (0.3.8)\r\n", "Collecting llvmlite<0.45,>=0.44.0dev0 (from numba==0.61.2->vllm)\r\n", "  Downloading llvmlite-0.44.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (4.8 kB)\r\n", "Collecting sympy>=1.13.3 (from torch>=2.4.0->unsloth)\r\n", "  Downloading sympy-1.14.0-py3-none-any.whl.metadata (12 kB)\r\n", "Requirement already satisfied: networkx in /usr/local/lib/python3.11/dist-packages (from torch>=2.4.0->unsloth) (3.5)\r\n", "Requirement already satisfied: jinja2 in /usr/local/lib/python3.11/dist-packages (from torch>=2.4.0->unsloth) (3.1.6)\r\n", "Requirement already satisfied: fsspec in /usr/local/lib/python3.11/dist-packages (from torch>=2.4.0->unsloth) (2025.5.1)\r\n", "Collecting nvidia-cuda-nvrtc-cu12==12.6.77 (from torch>=2.4.0->unsloth)\r\n", "  Downloading nvidia_cuda_nvrtc_cu12-12.6.77-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\r\n", "Collecting nvidia-cuda-runtime-cu12==12.6.77 (from torch>=2.4.0->unsloth)\r\n", "  Downloading nvidia_cuda_runtime_cu12-12.6.77-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl.metadata (1.5 kB)\r\n", "Collecting nvidia-cuda-cupti-cu12==12.6.80 (from torch>=2.4.0->unsloth)\r\n", "  Downloading nvidia_cuda_cupti_cu12-12.6.80-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl.metadata (1.6 kB)\r\n", "Collecting nvidia-cudnn-cu12==9.5.1.17 (from torch>=2.4.0->unsloth)\r\n", "  Downloading nvidia_cudnn_cu12-9.5.1.17-py3-none-manylinux_2_28_x86_64.whl.metadata (1.6 kB)\r\n", "Collecting nvidia-cublas-cu12==12.6.4.1 (from torch>=2.4.0->unsloth)\r\n", "  Downloading nvidia_cublas_cu12-12.6.4.1-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl.metadata (1.5 kB)\r\n", "Collecting nvidia-cufft-cu12==11.3.0.4 (from torch>=2.4.0->unsloth)\r\n", "  Downloading nvidia_cufft_cu12-11.3.0.4-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl.metadata (1.5 kB)\r\n", "Collecting nvidia-curand-cu12==10.3.7.77 (from torch>=2.4.0->unsloth)\r\n", "  Downloading nvidia_curand_cu12-10.3.7.77-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl.metadata (1.5 kB)\r\n", "Collecting nvidia-cusolver-cu12==11.7.1.2 (from torch>=2.4.0->unsloth)\r\n", "  Downloading nvidia_cusolver_cu12-11.7.1.2-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl.metadata (1.6 kB)\r\n", "Collecting nvidia-cusparse-cu12==12.5.4.2 (from torch>=2.4.0->unsloth)\r\n", "  Downloading nvidia_cusparse_cu12-12.5.4.2-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl.metadata (1.6 kB)\r\n", "Collecting nvidia-cusparselt-cu12==0.6.3 (from torch>=2.4.0->unsloth)\r\n", "  Downloading nvidia_cusparselt_cu12-0.6.3-py3-none-manylinux2014_x86_64.whl.metadata (6.8 kB)\r\n", "Collecting nvidia-nccl-cu12==2.26.2 (from torch>=2.4.0->unsloth)\r\n", "  Downloading nvidia_nccl_cu12-2.26.2-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl.metadata (2.0 kB)\r\n", "Collecting nvidia-nvtx-cu12==12.6.77 (from torch>=2.4.0->unsloth)\r\n", "  Downloading nvidia_nvtx_cu12-12.6.77-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl.metadata (1.6 kB)\r\n", "Collecting nvidia-nvjitlink-cu12==12.6.85 (from torch>=2.4.0->unsloth)\r\n", "  Downloading nvidia_nvjitlink_cu12-12.6.85-py3-none-manylinux2010_x86_64.manylinux_2_12_x86_64.whl.metadata (1.5 kB)\r\n", "Collecting nvidia-cufile-cu12==1.11.1.6 (from torch>=2.4.0->unsloth)\r\n", "  Downloading nvidia_cufile_cu12-1.11.1.6-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl.metadata (1.5 kB)\r\n", "Collecting triton>=3.0.0 (from unsloth)\r\n", "  Downloading triton-3.3.1-cp311-cp311-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl.metadata (1.5 kB)\r\n", "Requirement already satisfied: setuptools>=40.8.0 in /usr/local/lib/python3.11/dist-packages (from triton>=3.0.0->unsloth) (75.2.0)\r\n", "Requirement already satisfied: safetensors>=0.4.3 in /usr/local/lib/python3.11/dist-packages (from accelerate>=0.34.1->unsloth) (0.5.3)\r\n", "Requirement already satisfied: pyarrow>=15.0.0 in /usr/local/lib/python3.11/dist-packages (from datasets<4.0.0,>=3.4.1->unsloth) (19.0.1)\r\n", "Requirement already satisfied: pandas in /usr/local/lib/python3.11/dist-packages (from datasets<4.0.0,>=3.4.1->unsloth) (2.2.3)\r\n", "Requirement already satisfied: xxhash in /usr/local/lib/python3.11/dist-packages (from datasets<4.0.0,>=3.4.1->unsloth) (3.5.0)\r\n", "Requirement already satisfied: multiprocess<0.70.17 in /usr/local/lib/python3.11/dist-packages (from datasets<4.0.0,>=3.4.1->unsloth) (0.70.16)\r\n", "Collecting fsspec (from torch>=2.4.0->unsloth)\r\n", "  Downloading fsspec-2025.3.0-py3-none-any.whl.metadata (11 kB)\r\n", "Requirement already satisfied: starlette<0.47.0,>=0.40.0 in /usr/local/lib/python3.11/dist-packages (from fastapi>=0.115.0->fastapi[standard]>=0.115.0->vllm) (0.46.2)\r\n", "Collecting fastapi-cli>=0.0.5 (from fastapi-cli[standard]>=0.0.5; extra == \"standard\"->fastapi[standard]>=0.115.0->vllm)\r\n", "  Downloading fastapi_cli-0.0.8-py3-none-any.whl.metadata (6.3 kB)\r\n", "Requirement already satisfied: httpx>=0.23.0 in /usr/local/lib/python3.11/dist-packages (from fastapi[standard]>=0.115.0->vllm) (0.28.1)\r\n", "Requirement already satisfied: python-multipart>=0.0.18 in /usr/local/lib/python3.11/dist-packages (from fastapi[standard]>=0.115.0->vllm) (0.0.20)\r\n", "Requirement already satisfied: email-validator>=2.0.0 in /usr/local/lib/python3.11/dist-packages (from fastapi[standard]>=0.115.0->vllm) (2.2.0)\r\n", "Requirement already satisfied: uvicorn>=0.12.0 in /usr/local/lib/python3.11/dist-packages (from uvicorn[standard]>=0.12.0; extra == \"standard\"->fastapi[standard]>=0.115.0->vllm) (0.34.3)\r\n", "Requirement already satisfied: hf-xet<2.0.0,>=1.1.3 in /usr/local/lib/python3.11/dist-packages (from huggingface_hub>=0.34.0->unsloth) (1.1.5)\r\n", "Collecting interegular>=0.3.2 (from lm-format-enforcer<0.11,>=0.10.11->vllm)\r\n", "  Downloading interegular-0.3.3-py37-none-any.whl.metadata (3.0 kB)\r\n", "Requirement already satisfied: jsonschema>=4.21.1 in /usr/local/lib/python3.11/dist-packages (from mistral_common>=1.8.2->mistral_common[audio,image]>=1.8.2->vllm) (4.24.0)\r\n", "Collecting pydantic-extra-types>=2.10.5 (from pydantic-extra-types[pycountry]>=2.10.5->mistral_common>=1.8.2->mistral_common[audio,image]>=1.8.2->vllm)\r\n", "  Downloading pydantic_extra_types-2.10.5-py3-none-any.whl.metadata (3.9 kB)\r\n", "Requirement already satisfied: mkl_fft in /usr/local/lib/python3.11/dist-packages (from numpy->unsloth) (1.3.8)\r\n", "Requirement already satisfied: mkl_random in /usr/local/lib/python3.11/dist-packages (from numpy->unsloth) (1.2.4)\r\n", "Requirement already satisfied: mkl_umath in /usr/local/lib/python3.11/dist-packages (from numpy->unsloth) (0.1.1)\r\n", "Requirement already satisfied: mkl in /usr/local/lib/python3.11/dist-packages (from numpy->unsloth) (2025.2.0)\r\n", "Requirement already satisfied: tbb4py in /usr/local/lib/python3.11/dist-packages (from numpy->unsloth) (2022.2.0)\r\n", "Requirement already satisfied: mkl-service in /usr/local/lib/python3.11/dist-packages (from numpy->unsloth) (2.4.1)\r\n", "Requirement already satisfied: anyio<5,>=3.5.0 in /usr/local/lib/python3.11/dist-packages (from openai<=1.90.0,>=1.87.0->vllm) (4.9.0)\r\n", "Requirement already satisfied: distro<2,>=1.7.0 in /usr/local/lib/python3.11/dist-packages (from openai<=1.90.0,>=1.87.0->vllm) (1.9.0)\r\n", "Requirement already satisfied: jiter<1,>=0.4.0 in /usr/local/lib/python3.11/dist-packages (from openai<=1.90.0,>=1.87.0->vllm) (0.10.0)\r\n", "Requirement already satisfied: sniffio in /usr/local/lib/python3.11/dist-packages (from openai<=1.90.0,>=1.87.0->vllm) (1.3.1)\r\n", "Requirement already satisfied: annotated-types>=0.6.0 in /usr/local/lib/python3.11/dist-packages (from pydantic>=2.10->vllm) (0.7.0)\r\n", "Requirement already satisfied: pydantic-core==2.33.2 in /usr/local/lib/python3.11/dist-packages (from pydantic>=2.10->vllm) (2.33.2)\r\n", "Requirement already satisfied: typing-inspection>=0.4.0 in /usr/local/lib/python3.11/dist-packages (from pydantic>=2.10->vllm) (0.4.1)\r\n", "Requirement already satisfied: click>=7.0 in /usr/local/lib/python3.11/dist-packages (from ray!=2.44.*,>=2.43.0->ray[cgraph]!=2.44.*,>=2.43.0->vllm) (8.2.1)\r\n", "Requirement already satisfied: msgpack<2.0.0,>=1.0.0 in /usr/local/lib/python3.11/dist-packages (from ray!=2.44.*,>=2.43.0->ray[cgraph]!=2.44.*,>=2.43.0->vllm) (1.1.1)\r\n", "Requirement already satisfied: cupy-cuda12x in /usr/local/lib/python3.11/dist-packages (from ray[cgraph]!=2.44.*,>=2.43.0->vllm) (13.4.1)\r\n", "Requirement already satisfied: charset_normalizer<4,>=2 in /usr/local/lib/python3.11/dist-packages (from requests>=2.26.0->vllm) (3.4.2)\r\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.11/dist-packages (from requests>=2.26.0->vllm) (3.10)\r\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.11/dist-packages (from requests>=2.26.0->vllm) (2.5.0)\r\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.11/dist-packages (from requests>=2.26.0->vllm) (2025.6.15)\r\n", "Collecting cut_cross_entropy (from unsloth_zoo>=2025.8.1->unsloth)\r\n", "  Downloading cut_cross_entropy-25.1.1-py3-none-any.whl.metadata (9.3 kB)\r\n", "Requirement already satisfied: aiohappyeyeballs>=2.5.0 in /usr/local/lib/python3.11/dist-packages (from aiohttp->vllm) (2.6.1)\r\n", "Requirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.11/dist-packages (from aiohttp->vllm) (1.3.2)\r\n", "Requirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.11/dist-packages (from aiohttp->vllm) (25.3.0)\r\n", "Requirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.11/dist-packages (from aiohttp->vllm) (1.7.0)\r\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.11/dist-packages (from aiohttp->vllm) (6.6.3)\r\n", "Requirement already satisfied: propcache>=0.2.0 in /usr/local/lib/python3.11/dist-packages (from aiohttp->vllm) (0.3.2)\r\n", "Requirement already satisfied: yarl<2.0,>=1.17.0 in /usr/local/lib/python3.11/dist-packages (from aiohttp->vllm) (1.20.1)\r\n", "Requirement already satisfied: importlib_metadata in /usr/local/lib/python3.11/dist-packages (from diffusers->unsloth) (8.7.0)\r\n", "Requirement already satisfied: docstring-parser>=0.15 in /usr/local/lib/python3.11/dist-packages (from tyro->unsloth) (0.16)\r\n", "Requirement already satisfied: rich>=11.1.0 in /usr/local/lib/python3.11/dist-packages (from tyro->unsloth) (14.0.0)\r\n", "Collecting shtab>=1.5.6 (from tyro->unsloth)\r\n", "  Downloading shtab-1.7.2-py3-none-any.whl.metadata (7.4 kB)\r\n", "Requirement already satisfied: typeguard>=4.0.0 in /usr/local/lib/python3.11/dist-packages (from tyro->unsloth) (4.4.4)\r\n", "Requirement already satisfied: dnspython>=2.0.0 in /usr/local/lib/python3.11/dist-packages (from email-validator>=2.0.0->fastapi[standard]>=0.115.0->vllm) (2.7.0)\r\n", "Requirement already satisfied: typer>=0.15.1 in /usr/local/lib/python3.11/dist-packages (from fastapi-cli>=0.0.5->fastapi-cli[standard]>=0.0.5; extra == \"standard\"->fastapi[standard]>=0.115.0->vllm) (0.16.0)\r\n", "Collecting rich-toolkit>=0.14.8 (from fastapi-cli>=0.0.5->fastapi-cli[standard]>=0.0.5; extra == \"standard\"->fastapi[standard]>=0.115.0->vllm)\r\n", "  Downloading rich_toolkit-0.14.9-py3-none-any.whl.metadata (999 bytes)\r\n", "Collecting fastapi-cloud-cli>=0.1.1 (from fastapi-cli[standard]>=0.0.5; extra == \"standard\"->fastapi[standard]>=0.115.0->vllm)\r\n", "  Downloading fastapi_cloud_cli-0.1.5-py3-none-any.whl.metadata (3.2 kB)\r\n", "Requirement already satisfied: httpcore==1.* in /usr/local/lib/python3.11/dist-packages (from httpx>=0.23.0->fastapi[standard]>=0.115.0->vllm) (1.0.9)\r\n", "Requirement already satisfied: h11>=0.16 in /usr/local/lib/python3.11/dist-packages (from httpcore==1.*->httpx>=0.23.0->fastapi[standard]>=0.115.0->vllm) (0.16.0)\r\n", "Requirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.11/dist-packages (from jinja2->torch>=2.4.0->unsloth) (3.0.2)\r\n", "Requirement already satisfied: jsonschema-specifications>=2023.03.6 in /usr/local/lib/python3.11/dist-packages (from jsonschema>=4.21.1->mistral_common>=1.8.2->mistral_common[audio,image]>=1.8.2->vllm) (2025.4.1)\r\n", "Requirement already satisfied: referencing>=0.28.4 in /usr/local/lib/python3.11/dist-packages (from jsonschema>=4.21.1->mistral_common>=1.8.2->mistral_common[audio,image]>=1.8.2->vllm) (0.36.2)\r\n", "Requirement already satisfied: rpds-py>=0.7.1 in /usr/local/lib/python3.11/dist-packages (from jsonschema>=4.21.1->mistral_common>=1.8.2->mistral_common[audio,image]>=1.8.2->vllm) (0.25.1)\r\n", "Collecting pycountry>=23 (from pydantic-extra-types[pycountry]>=2.10.5->mistral_common>=1.8.2->mistral_common[audio,image]>=1.8.2->vllm)\r\n", "  Downloading pycountry-24.6.1-py3-none-any.whl.metadata (12 kB)\r\n", "Requirement already satisfied: markdown-it-py>=2.2.0 in /usr/local/lib/python3.11/dist-packages (from rich>=11.1.0->tyro->unsloth) (3.0.0)\r\n", "Requirement already satisfied: pygments<3.0.0,>=2.13.0 in /usr/local/lib/python3.11/dist-packages (from rich>=11.1.0->tyro->unsloth) (2.19.2)\r\n", "Requirement already satisfied: mpmath<1.4,>=1.1.0 in /usr/local/lib/python3.11/dist-packages (from sympy>=1.13.3->torch>=2.4.0->unsloth) (1.3.0)\r\n", "Collecting httptools>=0.6.3 (from uvicorn[standard]>=0.12.0; extra == \"standard\"->fastapi[standard]>=0.115.0->vllm)\r\n", "  Downloading httptools-0.6.4-cp311-cp311-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (3.6 kB)\r\n", "Collecting python-dotenv>=0.13 (from uvicorn[standard]>=0.12.0; extra == \"standard\"->fastapi[standard]>=0.115.0->vllm)\r\n", "  Downloading python_dotenv-1.1.1-py3-none-any.whl.metadata (24 kB)\r\n", "Collecting uvloop>=0.15.1 (from uvicorn[standard]>=0.12.0; extra == \"standard\"->fastapi[standard]>=0.115.0->vllm)\r\n", "  Downloading uvloop-0.21.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (4.9 kB)\r\n", "Requirement already satisfied: websockets>=10.4 in /usr/local/lib/python3.11/dist-packages (from uvicorn[standard]>=0.12.0; extra == \"standard\"->fastapi[standard]>=0.115.0->vllm) (15.0.1)\r\n", "Requirement already satisfied: fastrlock>=0.5 in /usr/local/lib/python3.11/dist-packages (from cupy-cuda12x->ray[cgraph]!=2.44.*,>=2.43.0->vllm) (0.8.3)\r\n", "Requirement already satisfied: zipp>=3.20 in /usr/local/lib/python3.11/dist-packages (from importlib_metadata->diffusers->unsloth) (3.23.0)\r\n", "Requirement already satisfied: soundfile>=0.12.1 in /usr/local/lib/python3.11/dist-packages (from mistral_common>=1.8.2->mistral_common[audio,image]>=1.8.2->vllm) (0.13.1)\r\n", "Requirement already satisfied: soxr>=0.5.0 in /usr/local/lib/python3.11/dist-packages (from mistral_common>=1.8.2->mistral_common[audio,image]>=1.8.2->vllm) (0.5.0.post1)\r\n", "Requirement already satisfied: intel-openmp<2026,>=2024 in /usr/local/lib/python3.11/dist-packages (from mkl->numpy->unsloth) (2024.2.0)\r\n", "Requirement already satisfied: tbb==2022.* in /usr/local/lib/python3.11/dist-packages (from mkl->numpy->unsloth) (2022.2.0)\r\n", "Requirement already satisfied: tcmlib==1.* in /usr/local/lib/python3.11/dist-packages (from tbb==2022.*->mkl->numpy->unsloth) (1.4.0)\r\n", "Requirement already satisfied: intel-cmplr-lib-rt in /usr/local/lib/python3.11/dist-packages (from mkl_umath->numpy->unsloth) (2024.2.0)\r\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /usr/local/lib/python3.11/dist-packages (from pandas->datasets<4.0.0,>=3.4.1->unsloth) (2.9.0.post0)\r\n", "Requirement already satisfied: pytz>=2020.1 in /usr/local/lib/python3.11/dist-packages (from pandas->datasets<4.0.0,>=3.4.1->unsloth) (2025.2)\r\n", "Requirement already satisfied: tzdata>=2022.7 in /usr/local/lib/python3.11/dist-packages (from pandas->datasets<4.0.0,>=3.4.1->unsloth) (2025.2)\r\n", "Collecting rignore>=0.5.1 (from fastapi-cloud-cli>=0.1.1->fastapi-cli[standard]>=0.0.5; extra == \"standard\"->fastapi[standard]>=0.115.0->vllm)\r\n", "  Downloading rignore-0.6.4-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (3.8 kB)\r\n", "Requirement already satisfied: sentry-sdk>=2.20.0 in /usr/local/lib/python3.11/dist-packages (from fastapi-cloud-cli>=0.1.1->fastapi-cli[standard]>=0.0.5; extra == \"standard\"->fastapi[standard]>=0.115.0->vllm) (2.31.0)\r\n", "Requirement already satisfied: intel-cmplr-lib-ur==2024.2.0 in /usr/local/lib/python3.11/dist-packages (from intel-openmp<2026,>=2024->mkl->numpy->unsloth) (2024.2.0)\r\n", "Requirement already satisfied: mdurl~=0.1 in /usr/local/lib/python3.11/dist-packages (from markdown-it-py>=2.2.0->rich>=11.1.0->tyro->unsloth) (0.1.2)\r\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.11/dist-packages (from python-dateutil>=2.8.2->pandas->datasets<4.0.0,>=3.4.1->unsloth) (1.17.0)\r\n", "Requirement already satisfied: cffi>=1.0 in /usr/local/lib/python3.11/dist-packages (from soundfile>=0.12.1->mistral_common>=1.8.2->mistral_common[audio,image]>=1.8.2->vllm) (1.17.1)\r\n", "Requirement already satisfied: shellingham>=1.3.0 in /usr/local/lib/python3.11/dist-packages (from typer>=0.15.1->fastapi-cli>=0.0.5->fastapi-cli[standard]>=0.0.5; extra == \"standard\"->fastapi[standard]>=0.115.0->vllm) (1.5.4)\r\n", "Requirement already satisfied: pycparser in /usr/local/lib/python3.11/dist-packages (from cffi>=1.0->soundfile>=0.12.1->mistral_common>=1.8.2->mistral_common[audio,image]>=1.8.2->vllm) (2.22)\r\n", "Downloading unsloth-2025.8.1-py3-none-any.whl (299 kB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m299.8/299.8 kB\u001b[0m \u001b[31m8.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading vllm-0.10.0-cp38-abi3-manylinux1_x86_64.whl (386.6 MB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m386.6/386.6 MB\u001b[0m \u001b[31m4.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading compressed_tensors-0.10.2-py3-none-any.whl (169 kB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m169.0/169.0 kB\u001b[0m \u001b[31m8.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading depyf-0.19.0-py3-none-any.whl (39 kB)\r\n", "Downloading diskcache-5.6.3-py3-none-any.whl (45 kB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m45.5/45.5 kB\u001b[0m \u001b[31m1.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading lark-1.2.2-py3-none-any.whl (111 kB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m111.0/111.0 kB\u001b[0m \u001b[31m5.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading numba-0.61.2-cp311-cp311-manylinux2014_x86_64.manylinux_2_17_x86_64.whl (3.8 MB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m3.8/3.8 MB\u001b[0m \u001b[31m74.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading outlines_core-0.2.10-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (2.3 MB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.3/2.3 MB\u001b[0m \u001b[31m63.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading torch-2.7.1-cp311-cp311-manylinux_2_28_x86_64.whl (821.2 MB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m821.2/821.2 MB\u001b[0m \u001b[31m1.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading torchaudio-2.7.1-cp311-cp311-manylinux_2_28_x86_64.whl (3.5 MB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m3.5/3.5 MB\u001b[0m \u001b[31m1.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading torchvision-0.22.1-cp311-cp311-manylinux_2_28_x86_64.whl (7.5 MB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m7.5/7.5 MB\u001b[0m \u001b[31m7.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading triton-3.3.1-cp311-cp311-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl (155.7 MB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m155.7/155.7 MB\u001b[0m \u001b[31m3.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading xformers-0.0.31-cp39-abi3-manylinux_2_28_x86_64.whl (117.1 MB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m117.1/117.1 MB\u001b[0m \u001b[31m14.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading xgrammar-0.1.21-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (11.8 MB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m11.8/11.8 MB\u001b[0m \u001b[31m97.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading nvidia_cublas_cu12-12.6.4.1-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl (393.1 MB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m393.1/393.1 MB\u001b[0m \u001b[31m4.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading nvidia_cuda_cupti_cu12-12.6.80-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl (8.9 MB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m8.9/8.9 MB\u001b[0m \u001b[31m100.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading nvidia_cuda_nvrtc_cu12-12.6.77-py3-none-manylinux2014_x86_64.whl (23.7 MB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m23.7/23.7 MB\u001b[0m \u001b[31m77.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading nvidia_cuda_runtime_cu12-12.6.77-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl (897 kB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m897.7/897.7 kB\u001b[0m \u001b[31m36.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading nvidia_cudnn_cu12-9.5.1.17-py3-none-manylinux_2_28_x86_64.whl (571.0 MB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m571.0/571.0 MB\u001b[0m \u001b[31m3.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading nvidia_cufft_cu12-11.3.0.4-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl (200.2 MB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m200.2/200.2 MB\u001b[0m \u001b[31m8.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading nvidia_cufile_cu12-1.11.1.6-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl (1.1 MB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.1/1.1 MB\u001b[0m \u001b[31m45.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading nvidia_curand_cu12-10.3.7.77-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl (56.3 MB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m56.3/56.3 MB\u001b[0m \u001b[31m30.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading nvidia_cusolver_cu12-11.7.1.2-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl (158.2 MB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m158.2/158.2 MB\u001b[0m \u001b[31m10.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading nvidia_cusparse_cu12-12.5.4.2-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl (216.6 MB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m216.6/216.6 MB\u001b[0m \u001b[31m2.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading nvidia_cusparselt_cu12-0.6.3-py3-none-manylinux2014_x86_64.whl (156.8 MB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m156.8/156.8 MB\u001b[0m \u001b[31m2.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading nvidia_nccl_cu12-2.26.2-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl (201.3 MB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m201.3/201.3 MB\u001b[0m \u001b[31m2.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading nvidia_nvjitlink_cu12-12.6.85-py3-none-manylinux2010_x86_64.manylinux_2_12_x86_64.whl (19.7 MB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m19.7/19.7 MB\u001b[0m \u001b[31m89.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading nvidia_nvtx_cu12-12.6.77-py3-none-manylinux2014_x86_64.manylinux_2_17_x86_64.whl (89 kB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m89.3/89.3 kB\u001b[0m \u001b[31m4.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading gguf-0.17.1-py3-none-any.whl (96 kB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m96.2/96.2 kB\u001b[0m \u001b[31m4.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading huggingface_hub-0.34.3-py3-none-any.whl (558 kB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m558.8/558.8 kB\u001b[0m \u001b[31m17.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading llguidance-0.7.30-cp39-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (15.0 MB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m15.0/15.0 MB\u001b[0m \u001b[31m92.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading lm_format_enforcer-0.10.12-py3-none-any.whl (44 kB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m44.3/44.3 kB\u001b[0m \u001b[31m2.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading mistral_common-1.8.3-py3-none-any.whl (6.5 MB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m6.5/6.5 MB\u001b[0m \u001b[31m97.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading openai-1.90.0-py3-none-any.whl (734 kB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m734.6/734.6 kB\u001b[0m \u001b[31m33.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading prometheus_fastapi_instrumentator-7.1.0-py3-none-any.whl (19 kB)\r\n", "Downloading pyzmq-27.0.1-cp311-cp311-manylinux_2_26_x86_64.manylinux_2_28_x86_64.whl (856 kB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m856.6/856.6 kB\u001b[0m \u001b[31m40.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading transformers-4.54.1-py3-none-any.whl (11.2 MB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m11.2/11.2 MB\u001b[0m \u001b[31m101.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading trl-0.20.0-py3-none-any.whl (504 kB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m504.6/504.6 kB\u001b[0m \u001b[31m30.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading unsloth_zoo-2025.8.1-py3-none-any.whl (166 kB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m166.7/166.7 kB\u001b[0m \u001b[31m9.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading bitsandbytes-0.46.1-py3-none-manylinux_2_24_x86_64.whl (72.9 MB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m72.9/72.9 MB\u001b[0m \u001b[31m23.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading blake3-1.0.5-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (385 kB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m385.5/385.5 kB\u001b[0m \u001b[31m17.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading cbor2-5.6.5-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (249 kB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m249.2/249.2 kB\u001b[0m \u001b[31m12.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading msgspec-0.19.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (210 kB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m210.7/210.7 kB\u001b[0m \u001b[31m10.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading partial_json_parser-0.2.1.1.post6-py3-none-any.whl (10 kB)\r\n", "Downloading pybase64-1.4.2-cp311-cp311-manylinux1_x86_64.manylinux2014_x86_64.manylinux_2_17_x86_64.manylinux_2_5_x86_64.whl (71 kB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m71.4/71.4 kB\u001b[0m \u001b[31m4.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading tyro-0.9.27-py3-none-any.whl (129 kB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m129.0/129.0 kB\u001b[0m \u001b[31m8.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading watchfiles-1.1.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (453 kB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m453.1/453.1 kB\u001b[0m \u001b[31m24.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading fastapi_cli-0.0.8-py3-none-any.whl (10 kB)\r\n", "Downloading fsspec-2025.3.0-py3-none-any.whl (193 kB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m193.6/193.6 kB\u001b[0m \u001b[31m9.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading interegular-0.3.3-py37-none-any.whl (23 kB)\r\n", "Downloading llvmlite-0.44.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (42.4 MB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m42.4/42.4 MB\u001b[0m \u001b[31m41.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading pydantic_extra_types-2.10.5-py3-none-any.whl (38 kB)\r\n", "Downloading shtab-1.7.2-py3-none-any.whl (14 kB)\r\n", "Downloading sympy-1.14.0-py3-none-any.whl (6.3 MB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m6.3/6.3 MB\u001b[0m \u001b[31m99.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading astor-0.8.1-py2.py3-none-any.whl (27 kB)\r\n", "Downloading cut_cross_entropy-25.1.1-py3-none-any.whl (22 kB)\r\n", "Downloading fastapi_cloud_cli-0.1.5-py3-none-any.whl (18 kB)\r\n", "Downloading httptools-0.6.4-cp311-cp311-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl (459 kB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m459.8/459.8 kB\u001b[0m \u001b[31m23.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading pycountry-24.6.1-py3-none-any.whl (6.3 MB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m6.3/6.3 MB\u001b[0m \u001b[31m90.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading python_dotenv-1.1.1-py3-none-any.whl (20 kB)\r\n", "Downloading rich_toolkit-0.14.9-py3-none-any.whl (25 kB)\r\n", "Downloading uvloop-0.21.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (4.0 MB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m4.0/4.0 MB\u001b[0m \u001b[31m81.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hDownloading rignore-0.6.4-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (950 kB)\r\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m950.6/950.6 kB\u001b[0m \u001b[31m39.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\r\n", "\u001b[?25hInstalling collected packages: nvidia-cusparselt-cu12, blake3, uvloop, triton, sympy, shtab, rignore, pyzmq, python-dotenv, pycountry, pybase64, partial-json-parser, outlines_core, nvidia-nvtx-cu12, nvidia-nvjitlink-cu12, nvidia-nccl-cu12, nvidia-curand-cu12, nvidia-cufile-cu12, nvidia-cuda-runtime-cu12, nvidia-cuda-nvrtc-cu12, nvidia-cuda-cupti-cu12, nvidia-cublas-cu12, msgspec, llvmlite, llguidance, lark, interegular, httptools, fsspec, diskcache, cbor2, astor, watchfiles, nvidia-cusparse-cu12, nvidia-cufft-cu12, nvidia-cudnn-cu12, huggingface_hub, depyf, tyro, rich-toolkit, pydantic-extra-types, prometheus-fastapi-instrumentator, openai, nvidia-cusolver-cu12, lm-format-enforcer, torch, fastapi-cloud-cli, fastapi-cli, torchaudio, cut_cross_entropy, transformers, mistral_common, trl, xgrammar, xformers, unsloth_zoo, torchvision, numba, gguf, compressed-tensors, bitsandbytes, vllm, unsloth\r\n", "  Attempting uninstall: nvidia-cusparselt-cu12\r\n", "    Found existing installation: nvidia-cusparselt-cu12 0.6.2\r\n", "    Uninstalling nvidia-cusparselt-cu12-0.6.2:\r\n", "      Successfully uninstalled nvidia-cusparselt-cu12-0.6.2\r\n", "  Attempting uninstall: triton\r\n", "    Found existing installation: triton 3.2.0\r\n", "    Uninstalling triton-3.2.0:\r\n", "      Successfully uninstalled triton-3.2.0\r\n", "  Attempting uninstall: sympy\r\n", "    Found existing installation: sympy 1.13.1\r\n", "    Uninstalling sympy-1.13.1:\r\n", "      Successfully uninstalled sympy-1.13.1\r\n", "  Attempting uninstall: pyzmq\r\n", "    Found existing installation: pyzmq 24.0.1\r\n", "    Uninstalling pyzmq-24.0.1:\r\n", "      Successfully uninstalled pyzmq-24.0.1\r\n", "  Attempting uninstall: nvidia-nvtx-cu12\r\n", "    Found existing installation: nvidia-nvtx-cu12 12.4.127\r\n", "    Uninstalling nvidia-nvtx-cu12-12.4.127:\r\n", "      Successfully uninstalled nvidia-nvtx-cu12-12.4.127\r\n", "  Attempting uninstall: nvidia-nvjitlink-cu12\r\n", "    Found existing installation: nvidia-nvjitlink-cu12 12.4.127\r\n", "    Uninstalling nvidia-nvjitlink-cu12-12.4.127:\r\n", "      Successfully uninstalled nvidia-nvjitlink-cu12-12.4.127\r\n", "  Attempting uninstall: nvidia-nccl-cu12\r\n", "    Found existing installation: nvidia-nccl-cu12 2.21.5\r\n", "    Uninstalling nvidia-nccl-cu12-2.21.5:\r\n", "      Successfully uninstalled nvidia-nccl-cu12-2.21.5\r\n", "  Attempting uninstall: nvidia-curand-cu12\r\n", "    Found existing installation: nvidia-curand-cu12 10.3.5.147\r\n", "    Uninstalling nvidia-curand-cu12-10.3.5.147:\r\n", "      Successfully uninstalled nvidia-curand-cu12-10.3.5.147\r\n", "  Attempting uninstall: nvidia-cuda-runtime-cu12\r\n", "    Found existing installation: nvidia-cuda-runtime-cu12 12.4.127\r\n", "    Uninstalling nvidia-cuda-runtime-cu12-12.4.127:\r\n", "      Successfully uninstalled nvidia-cuda-runtime-cu12-12.4.127\r\n", "  Attempting uninstall: nvidia-cuda-nvrtc-cu12\r\n", "    Found existing installation: nvidia-cuda-nvrtc-cu12 12.4.127\r\n", "    Uninstalling nvidia-cuda-nvrtc-cu12-12.4.127:\r\n", "      Successfully uninstalled nvidia-cuda-nvrtc-cu12-12.4.127\r\n", "  Attempting uninstall: nvidia-cuda-cupti-cu12\r\n", "    Found existing installation: nvidia-cuda-cupti-cu12 12.4.127\r\n", "    Uninstalling nvidia-cuda-cupti-cu12-12.4.127:\r\n", "      Successfully uninstalled nvidia-cuda-cupti-cu12-12.4.127\r\n", "  Attempting uninstall: nvidia-cublas-cu12\r\n", "    Found existing installation: nvidia-cublas-cu12 12.4.5.8\r\n", "    Uninstalling nvidia-cublas-cu12-12.4.5.8:\r\n", "      Successfully uninstalled nvidia-cublas-cu12-12.4.5.8\r\n", "  Attempting uninstall: llvmlite\r\n", "    Found existing installation: llvmlite 0.43.0\r\n", "    Uninstalling llvmlite-0.43.0:\r\n", "      Successfully uninstalled llvmlite-0.43.0\r\n", "  Attempting uninstall: fsspec\r\n", "    Found existing installation: fsspec 2025.5.1\r\n", "    Uninstalling fsspec-2025.5.1:\r\n", "      Successfully uninstalled fsspec-2025.5.1\r\n", "  Attempting uninstall: nvidia-cusparse-cu12\r\n", "    Found existing installation: nvidia-cusparse-cu12 12.3.1.170\r\n", "    Uninstalling nvidia-cusparse-cu12-12.3.1.170:\r\n", "      Successfully uninstalled nvidia-cusparse-cu12-12.3.1.170\r\n", "  Attempting uninstall: nvidia-cufft-cu12\r\n", "    Found existing installation: nvidia-cufft-cu12 11.2.1.3\r\n", "    Uninstalling nvidia-cufft-cu12-11.2.1.3:\r\n", "      Successfully uninstalled nvidia-cufft-cu12-11.2.1.3\r\n", "  Attempting uninstall: nvidia-cudnn-cu12\r\n", "    Found existing installation: nvidia-cudnn-cu12 9.1.0.70\r\n", "    Uninstalling nvidia-cudnn-cu12-9.1.0.70:\r\n", "      Successfully uninstalled nvidia-cudnn-cu12-9.1.0.70\r\n", "  Attempting uninstall: huggingface_hub\r\n", "    Found existing installation: huggingface-hub 0.33.1\r\n", "    Uninstalling huggingface-hub-0.33.1:\r\n", "      Successfully uninstalled huggingface-hub-0.33.1\r\n", "  Attempting uninstall: openai\r\n", "    Found existing installation: openai 1.91.0\r\n", "    Uninstalling openai-1.91.0:\r\n", "      Successfully uninstalled openai-1.91.0\r\n", "  Attempting uninstall: nvidia-cusolver-cu12\r\n", "    Found existing installation: nvidia-cusolver-cu12 11.6.1.9\r\n", "    Uninstalling nvidia-cusolver-cu12-11.6.1.9:\r\n", "      Successfully uninstalled nvidia-cusolver-cu12-11.6.1.9\r\n", "  Attempting uninstall: torch\r\n", "    Found existing installation: torch 2.6.0+cu124\r\n", "    Uninstalling torch-2.6.0+cu124:\r\n", "      Successfully uninstalled torch-2.6.0+cu124\r\n", "  Attempting uninstall: <PERSON><PERSON><PERSON>\r\n", "    Found existing installation: torchaudio 2.6.0+cu124\r\n", "    Uninstalling torchaudio-2.6.0+cu124:\r\n", "      Successfully uninstalled torchaudio-2.6.0+cu124\r\n", "  Attempting uninstall: transformers\r\n", "    Found existing installation: transformers 4.52.4\r\n", "    Uninstalling transformers-4.52.4:\r\n", "      Successfully uninstalled transformers-4.52.4\r\n", "  Attempting uninstall: xformers\r\n", "    Found existing installation: xformers 0.0.29.post3\r\n", "    Uninstalling xformers-0.0.29.post3:\r\n", "      Successfully uninstalled xformers-0.0.29.post3\r\n", "  Attempting uninstall: torchvision\r\n", "    Found existing installation: torchvision 0.21.0+cu124\r\n", "    Uninstalling torchvision-0.21.0+cu124:\r\n", "      Successfully uninstalled torchvision-0.21.0+cu124\r\n", "  Attempting uninstall: numba\r\n", "    Found existing installation: numba 0.60.0\r\n", "    Uninstalling numba-0.60.0:\r\n", "      Successfully uninstalled numba-0.60.0\r\n", "\u001b[31mERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.\r\n", "bigframes 2.8.0 requires google-cloud-bigquery-storage<3.0.0,>=2.30.0, which is not installed.\r\n", "dask-cuda 25.2.0 requires numba<0.61.0a0,>=0.59.1, but you have numba 0.61.2 which is incompatible.\r\n", "cuml-cu12 25.2.1 requires numba<0.61.0a0,>=0.59.1, but you have numba 0.61.2 which is incompatible.\r\n", "cudf-cu12 25.2.2 requires numba<0.61.0a0,>=0.59.1, but you have numba 0.61.2 which is incompatible.\r\n", "distributed-ucxx-cu12 0.42.0 requires numba<0.61.0a0,>=0.59.1, but you have numba 0.61.2 which is incompatible.\r\n", "ydata-profiling 4.16.1 requires numba<=0.61,>=0.56.0, but you have numba 0.61.2 which is incompatible.\r\n", "cesium 0.12.4 requires numpy<3.0,>=2.0, but you have numpy 1.26.4 which is incompatible.\r\n", "google-colab 1.0.0 requires google-auth==2.38.0, but you have google-auth 2.40.3 which is incompatible.\r\n", "google-colab 1.0.0 requires notebook==6.5.7, but you have notebook 6.5.4 which is incompatible.\r\n", "google-colab 1.0.0 requires pandas==2.2.2, but you have pandas 2.2.3 which is incompatible.\r\n", "google-colab 1.0.0 requires requests==2.32.3, but you have requests 2.32.4 which is incompatible.\r\n", "google-colab 1.0.0 requires tornado==6.4.2, but you have tornado 6.5.1 which is incompatible.\r\n", "gcsfs 2025.3.2 requires fsspec==2025.3.2, but you have fsspec 2025.3.0 which is incompatible.\r\n", "fastai 2.7.19 requires torch<2.7,>=1.10, but you have torch 2.7.1 which is incompatible.\r\n", "bigframes 2.8.0 requires google-cloud-bigquery[bqstorage,pandas]>=3.31.0, but you have google-cloud-bigquery 3.25.0 which is incompatible.\r\n", "bigframes 2.8.0 requires rich<14,>=12.4.4, but you have rich 14.0.0 which is incompatible.\r\n", "jupyter-kernel-gateway 2.5.2 requires jupyter-client<8.0,>=5.2.0, but you have jupyter-client 8.6.3 which is incompatible.\u001b[0m\u001b[31m\r\n", "\u001b[0mSuccessfully installed astor-0.8.1 bitsandbytes-0.46.1 blake3-1.0.5 cbor2-5.6.5 compressed-tensors-0.10.2 cut_cross_entropy-25.1.1 depyf-0.19.0 diskcache-5.6.3 fastapi-cli-0.0.8 fastapi-cloud-cli-0.1.5 fsspec-2025.3.0 gguf-0.17.1 httptools-0.6.4 huggingface_hub-0.34.3 interegular-0.3.3 lark-1.2.2 llguidance-0.7.30 llvmlite-0.44.0 lm-format-enforcer-0.10.12 mistral_common-1.8.3 msgspec-0.19.0 numba-0.61.2 nvidia-cublas-cu12-12.6.4.1 nvidia-cuda-cupti-cu12-12.6.80 nvidia-cuda-nvrtc-cu12-12.6.77 nvidia-cuda-runtime-cu12-12.6.77 nvidia-cudnn-cu12-9.5.1.17 nvidia-cufft-cu12-11.3.0.4 nvidia-cufile-cu12-1.11.1.6 nvidia-curand-cu12-10.3.7.77 nvidia-cusolver-cu12-11.7.1.2 nvidia-cusparse-cu12-12.5.4.2 nvidia-cusparselt-cu12-0.6.3 nvidia-nccl-cu12-2.26.2 nvidia-nvjitlink-cu12-12.6.85 nvidia-nvtx-cu12-12.6.77 openai-1.90.0 outlines_core-0.2.10 partial-json-parser-0.2.1.1.post6 prometheus-fastapi-instrumentator-7.1.0 pybase64-1.4.2 pycountry-24.6.1 pydantic-extra-types-2.10.5 python-dotenv-1.1.1 pyzmq-27.0.1 rich-toolkit-0.14.9 rignore-0.6.4 shtab-1.7.2 sympy-1.14.0 torch-2.7.1 torchaudio-2.7.1 torchvision-0.22.1 transformers-4.54.1 triton-3.3.1 trl-0.20.0 tyro-0.9.27 unsloth-2025.8.1 unsloth_zoo-2025.8.1 uvloop-0.21.0 vllm-0.10.0 watchfiles-1.1.0 xformers-0.0.31 xgrammar-0.1.21\r\n", "Requirement already satisfied: huggingface_hub>=0.34.0 in /usr/local/lib/python3.11/dist-packages (0.34.3)\r\n", "Requirement already satisfied: datasets<4.0.0,>=3.4.1 in /usr/local/lib/python3.11/dist-packages (3.6.0)\r\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.11/dist-packages (from huggingface_hub>=0.34.0) (3.18.0)\r\n", "Requirement already satisfied: fsspec>=2023.5.0 in /usr/local/lib/python3.11/dist-packages (from huggingface_hub>=0.34.0) (2025.3.0)\r\n", "Requirement already satisfied: packaging>=20.9 in /usr/local/lib/python3.11/dist-packages (from huggingface_hub>=0.34.0) (25.0)\r\n", "Requirement already satisfied: pyyaml>=5.1 in /usr/local/lib/python3.11/dist-packages (from huggingface_hub>=0.34.0) (6.0.2)\r\n", "Requirement already satisfied: requests in /usr/local/lib/python3.11/dist-packages (from huggingface_hub>=0.34.0) (2.32.4)\r\n", "Requirement already satisfied: tqdm>=4.42.1 in /usr/local/lib/python3.11/dist-packages (from huggingface_hub>=0.34.0) (4.67.1)\r\n", "Requirement already satisfied: typing-extensions>=3.7.4.3 in /usr/local/lib/python3.11/dist-packages (from huggingface_hub>=0.34.0) (4.14.0)\r\n", "Requirement already satisfied: hf-xet<2.0.0,>=1.1.3 in /usr/local/lib/python3.11/dist-packages (from huggingface_hub>=0.34.0) (1.1.5)\r\n", "Requirement already satisfied: numpy>=1.17 in /usr/local/lib/python3.11/dist-packages (from datasets<4.0.0,>=3.4.1) (1.26.4)\r\n", "Requirement already satisfied: pyarrow>=15.0.0 in /usr/local/lib/python3.11/dist-packages (from datasets<4.0.0,>=3.4.1) (19.0.1)\r\n", "Requirement already satisfied: dill<0.3.9,>=0.3.0 in /usr/local/lib/python3.11/dist-packages (from datasets<4.0.0,>=3.4.1) (0.3.8)\r\n", "Requirement already satisfied: pandas in /usr/local/lib/python3.11/dist-packages (from datasets<4.0.0,>=3.4.1) (2.2.3)\r\n", "Requirement already satisfied: xxhash in /usr/local/lib/python3.11/dist-packages (from datasets<4.0.0,>=3.4.1) (3.5.0)\r\n", "Requirement already satisfied: multiprocess<0.70.17 in /usr/local/lib/python3.11/dist-packages (from datasets<4.0.0,>=3.4.1) (0.70.16)\r\n", "Requirement already satisfied: aiohttp!=4.0.0a0,!=4.0.0a1 in /usr/local/lib/python3.11/dist-packages (from fsspec[http]<=2025.3.0,>=2023.1.0->datasets<4.0.0,>=3.4.1) (3.12.13)\r\n", "Requirement already satisfied: mkl_fft in /usr/local/lib/python3.11/dist-packages (from numpy>=1.17->datasets<4.0.0,>=3.4.1) (1.3.8)\r\n", "Requirement already satisfied: mkl_random in /usr/local/lib/python3.11/dist-packages (from numpy>=1.17->datasets<4.0.0,>=3.4.1) (1.2.4)\r\n", "Requirement already satisfied: mkl_umath in /usr/local/lib/python3.11/dist-packages (from numpy>=1.17->datasets<4.0.0,>=3.4.1) (0.1.1)\r\n", "Requirement already satisfied: mkl in /usr/local/lib/python3.11/dist-packages (from numpy>=1.17->datasets<4.0.0,>=3.4.1) (2025.2.0)\r\n", "Requirement already satisfied: tbb4py in /usr/local/lib/python3.11/dist-packages (from numpy>=1.17->datasets<4.0.0,>=3.4.1) (2022.2.0)\r\n", "Requirement already satisfied: mkl-service in /usr/local/lib/python3.11/dist-packages (from numpy>=1.17->datasets<4.0.0,>=3.4.1) (2.4.1)\r\n", "Requirement already satisfied: charset_normalizer<4,>=2 in /usr/local/lib/python3.11/dist-packages (from requests->huggingface_hub>=0.34.0) (3.4.2)\r\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.11/dist-packages (from requests->huggingface_hub>=0.34.0) (3.10)\r\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.11/dist-packages (from requests->huggingface_hub>=0.34.0) (2.5.0)\r\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.11/dist-packages (from requests->huggingface_hub>=0.34.0) (2025.6.15)\r\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /usr/local/lib/python3.11/dist-packages (from pandas->datasets<4.0.0,>=3.4.1) (2.9.0.post0)\r\n", "Requirement already satisfied: pytz>=2020.1 in /usr/local/lib/python3.11/dist-packages (from pandas->datasets<4.0.0,>=3.4.1) (2025.2)\r\n", "Requirement already satisfied: tzdata>=2022.7 in /usr/local/lib/python3.11/dist-packages (from pandas->datasets<4.0.0,>=3.4.1) (2025.2)\r\n", "Requirement already satisfied: aiohappyeyeballs>=2.5.0 in /usr/local/lib/python3.11/dist-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<=2025.3.0,>=2023.1.0->datasets<4.0.0,>=3.4.1) (2.6.1)\r\n", "Requirement already satisfied: aiosignal>=1.1.2 in /usr/local/lib/python3.11/dist-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<=2025.3.0,>=2023.1.0->datasets<4.0.0,>=3.4.1) (1.3.2)\r\n", "Requirement already satisfied: attrs>=17.3.0 in /usr/local/lib/python3.11/dist-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<=2025.3.0,>=2023.1.0->datasets<4.0.0,>=3.4.1) (25.3.0)\r\n", "Requirement already satisfied: frozenlist>=1.1.1 in /usr/local/lib/python3.11/dist-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<=2025.3.0,>=2023.1.0->datasets<4.0.0,>=3.4.1) (1.7.0)\r\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /usr/local/lib/python3.11/dist-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<=2025.3.0,>=2023.1.0->datasets<4.0.0,>=3.4.1) (6.6.3)\r\n", "Requirement already satisfied: propcache>=0.2.0 in /usr/local/lib/python3.11/dist-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<=2025.3.0,>=2023.1.0->datasets<4.0.0,>=3.4.1) (0.3.2)\r\n", "Requirement already satisfied: yarl<2.0,>=1.17.0 in /usr/local/lib/python3.11/dist-packages (from aiohttp!=4.0.0a0,!=4.0.0a1->fsspec[http]<=2025.3.0,>=2023.1.0->datasets<4.0.0,>=3.4.1) (1.20.1)\r\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.11/dist-packages (from python-dateutil>=2.8.2->pandas->datasets<4.0.0,>=3.4.1) (1.17.0)\r\n", "Requirement already satisfied: intel-openmp<2026,>=2024 in /usr/local/lib/python3.11/dist-packages (from mkl->numpy>=1.17->datasets<4.0.0,>=3.4.1) (2024.2.0)\r\n", "Requirement already satisfied: tbb==2022.* in /usr/local/lib/python3.11/dist-packages (from mkl->numpy>=1.17->datasets<4.0.0,>=3.4.1) (2022.2.0)\r\n", "Requirement already satisfied: tcmlib==1.* in /usr/local/lib/python3.11/dist-packages (from tbb==2022.*->mkl->numpy>=1.17->datasets<4.0.0,>=3.4.1) (1.4.0)\r\n", "Requirement already satisfied: intel-cmplr-lib-rt in /usr/local/lib/python3.11/dist-packages (from mkl_umath->numpy>=1.17->datasets<4.0.0,>=3.4.1) (2024.2.0)\r\n", "Requirement already satisfied: intel-cmplr-lib-ur==2024.2.0 in /usr/local/lib/python3.11/dist-packages (from intel-openmp<2026,>=2024->mkl->numpy>=1.17->datasets<4.0.0,>=3.4.1) (2024.2.0)\r\n"]}], "source": ["!pip install pip3-autoremove\n", "!pip install torch torchvision torchaudio xformers --index-url https://download.pytorch.org/whl/cu124\n", "!pip install unsloth vllm\n", "!pip install \"huggingface_hub>=0.34.0\" \"datasets>=3.4.1,<4.0.0\""]}, {"cell_type": "code", "execution_count": 2, "id": "a1d89fcf", "metadata": {"execution": {"iopub.execute_input": "2025-08-05T07:24:03.016783Z", "iopub.status.busy": "2025-08-05T07:24:03.016217Z", "iopub.status.idle": "2025-08-05T11:22:56.894799Z", "shell.execute_reply": "2025-08-05T11:22:56.893879Z"}, "papermill": {"duration": 14333.952613, "end_time": "2025-08-05T11:22:56.896192", "exception": false, "start_time": "2025-08-05T07:24:02.943579", "status": "completed"}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🦥 Unsloth: Will patch your computer to enable 2x faster free finetuning.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-08-05 07:24:15.168252: E external/local_xla/xla/stream_executor/cuda/cuda_fft.cc:477] Unable to register cuFFT factory: Attempting to register factory for plugin cuFFT when one has already been registered\n", "WARNING: All log messages before absl::InitializeLog() is called are written to STDERR\n", "E0000 00:00:1754378655.539363      19 cuda_dnn.cc:8310] Unable to register cuDNN factory: Attempting to register factory for plugin cuDNN when one has already been registered\n", "E0000 00:00:1754378655.646436      19 cuda_blas.cc:1418] Unable to register cuBLAS factory: Attempting to register factory for plugin cuBLAS when one has already been registered\n"]}, {"name": "stdout", "output_type": "stream", "text": ["🦥 Unsloth Zoo will now patch everything to make training faster!\n", "INFO 08-05 07:24:42 [__init__.py:235] Automatically detected platform cuda.\n", "Loading model...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Unrecognized keys in `rope_scaling` for 'rope_type'='yarn': {'attn_factor'}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["==((====))==  Unsloth 2025.8.1: Fast Qwen3 patching. Transformers: 4.54.1. vLLM: 0.10.0.\n", "   \\\\   /|    Tesla T4. Num GPUs = 2. Max memory: 14.741 GB. Platform: Linux.\n", "O^O/ \\_/ \\    Torch: 2.7.1+cu126. CUDA: 7.5. CUDA Toolkit: 12.6. Triton: 3.3.1\n", "\\        /    Bfloat16 = FALSE. FA [Xformers = 0.0.31. FA2 = False]\n", " \"-____-\"     Free license: http://github.com/unslothai/unsloth\n", "Unsloth: Fast downloading is enabled - ignore downloading bars which are red colored!\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Unrecognized keys in `rope_scaling` for 'rope_type'='yarn': {'attn_factor'}\n", "Unrecognized keys in `rope_scaling` for 'rope_type'='yarn': {'attn_factor'}\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "85a97b317c1148b9909834a48be4d305", "version_major": 2, "version_minor": 0}, "text/plain": ["model.safetensors.index.json: 0.00B [00:00, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "b9deac3d4c4744129cacb8ba77618dc8", "version_major": 2, "version_minor": 0}, "text/plain": ["model-00001-of-00002.safetensors:   0%|          | 0.00/4.98G [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "07f092bfddd3467196144c627fadaf05", "version_major": 2, "version_minor": 0}, "text/plain": ["model-00002-of-00002.safetensors:   0%|          | 0.00/2.47G [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "8d12438ac88f4e06b625810202416bb1", "version_major": 2, "version_minor": 0}, "text/plain": ["Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "dd0e58e2a39749e5bee1426c34f73d75", "version_major": 2, "version_minor": 0}, "text/plain": ["generation_config.json:   0%|          | 0.00/171 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "2b382b1b72d44c54af1d61de68a27ca5", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer_config.json: 0.00B [00:00, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "8e5f0d70e3af42028765f61c9090ba6e", "version_major": 2, "version_minor": 0}, "text/plain": ["tokenizer.json:   0%|          | 0.00/11.4M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "f18e71b6af684b9586164317d389545f", "version_major": 2, "version_minor": 0}, "text/plain": ["special_tokens_map.json:   0%|          | 0.00/472 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "9d9202416ead44799557996f9191d670", "version_major": 2, "version_minor": 0}, "text/plain": ["chat_template.jinja: 0.00B [00:00, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Loading dataset from: /kaggle/input/json-prompt-llm-score/merged_training_dataset.json\n", "Scoring dataset...\n", "[1/201] Score: 7.0 \t llm Score: 6.0\n", "[2/201] Score: 8.0 \t llm Score: 5.0\n", "[3/201] Score: 2.0 \t llm Score: 3.0\n", "[4/201] Score: 5.0 \t llm Score: 10.0\n", "[5/201] Score: 10.0 \t llm Score: 10.0\n", "[6/201] Score: 6.0 \t llm Score: 5.0\n", "[7/201] Score: 7.0 \t llm Score: 10.0\n", "[8/201] Score: 7.0 \t llm Score: 7.5\n", "[9/201] Score: 10.0 \t llm Score: 10.0\n", "[10/201] Score: 7.0 \t llm Score: 7.0\n", "[11/201] Score: 10.0 \t llm Score: 10.0\n", "[12/201] Score: 9.0 \t llm Score: 5.5\n", "[13/201] Score: 10.0 \t llm Score: 5.0\n", "[14/201] Score: 4.0 \t llm Score: 8.0\n", "[15/201] Score: 7.0 \t llm Score: 7.0\n", "[16/201] Score: 6.0 \t llm Score: 3.0\n", "[17/201] Score: 6.0 \t llm Score: 3.0\n", "[18/201] Score: 10.0 \t llm Score: 10.0\n", "[19/201] Score: 9.0 \t llm Score: 9.0\n", "[20/201] Score: 9.0 \t llm Score: 5.0\n", "[21/201] Score: 9.0 \t llm Score: 10.0\n", "[22/201] Score: 9.0 \t llm Score: 9.0\n", "[23/201] Score: 9.0 \t llm Score: 6.0\n", "[24/201] Score: 9.0 \t llm Score: 10.0\n", "[25/201] Score: 7.0 \t llm Score: 4.0\n", "[26/201] Score: 8.0 \t llm Score: 9.0\n", "[27/201] Score: 7.0 \t llm Score: 7.0\n", "[28/201] Score: 10.0 \t llm Score: 10.0\n", "[29/201] Score: 7.0 \t llm Score: 10.0\n", "[30/201] Score: 4.0 \t llm Score: 3\n", "[31/201] Score: 4.0 \t llm Score: 6\n", "[32/201] Score: 0.0 \t llm Score: 1\n", "[33/201] Score: 5.0 \t llm Score: 4\n", "[34/201] Score: 9.0 \t llm Score: 1\n", "[35/201] Score: 10.0 \t llm Score: 10\n", "[36/201] Score: 0.0 \t llm Score: 3\n", "[37/201] Score: 8.0 \t llm Score: 10\n", "[38/201] Score: 7.0 \t llm Score: 4\n", "[39/201] Score: 4.0 \t llm Score: 3\n", "[40/201] Score: 10.0 \t llm Score: 10\n", "[41/201] Score: 5.0 \t llm Score: 3\n", "[42/201] Score: 4.0 \t llm Score: 3\n", "[43/201] Score: 9.0 \t llm Score: 9\n", "[44/201] Score: 2.0 \t llm Score: 3\n", "[45/201] Score: 8.0 \t llm Score: 3\n", "[46/201] Score: 8.0 \t llm Score: 9\n", "[47/201] Score: 6.0 \t llm Score: 5\n", "[48/201] Score: 7.0 \t llm Score: 8\n", "[49/201] Score: 5.0 \t llm Score: 4\n", "[50/201] Score: 7.0 \t llm Score: 8\n", "[51/201] Score: 10.0 \t llm Score: 8\n", "[52/201] Score: 4.0 \t llm Score: 6\n", "[53/201] Score: 5.0 \t llm Score: 7\n", "[54/201] Score: 10.0 \t llm Score: 10\n", "[55/201] Score: 8.0 \t llm Score: 10\n", "[56/201] Score: 4.0 \t llm Score: 4\n", "[57/201] Score: 8.0 \t llm Score: 3\n", "[58/201] Score: 6.0 \t llm Score: 3\n", "[59/201] Score: 7.0 \t llm Score: 10\n", "[60/201] Score: 8.0 \t llm Score: 6\n", "[61/201] Score: 5.0 \t llm Score: 4\n", "[62/201] Score: 4.0 \t llm Score: 3\n", "[63/201] Score: 10.0 \t llm Score: 10\n", "[64/201] Score: 7.0 \t llm Score: 2\n", "[65/201] Score: 6.0 \t llm Score: 7\n", "[66/201] Score: 7.0 \t llm Score: 3\n", "[67/201] Score: 4.0 \t llm Score: 3\n", "[68/201] Score: 9.0 \t llm Score: 7\n", "[69/201] Score: 4.0 \t llm Score: 4.0\n", "[70/201] Score: 7.0 \t llm Score: 10.0\n", "[71/201] Score: 4.0 \t llm Score: 6.0\n", "[72/201] Score: 6.0 \t llm Score: 5.0\n", "[73/201] Score: 4.0 \t llm Score: 1.0\n", "[74/201] Score: 4.0 \t llm Score: 7.0\n", "[75/201] Score: 4.0 \t llm Score: 7.0\n", "[76/201] Score: 10.0 \t llm Score: 10.0\n", "[77/201] Score: 7.0 \t llm Score: 6.0\n", "[78/201] Score: 8.0 \t llm Score: 10.0\n", "[79/201] Score: 8.0 \t llm Score: 4.0\n", "[80/201] Score: 9.0 \t llm Score: 7.0\n", "[81/201] Score: 5.0 \t llm Score: 3.0\n", "[82/201] Score: 3.0 \t llm Score: 6.0\n", "[83/201] Score: 8.0 \t llm Score: 4.0\n", "[84/201] Score: 8.0 \t llm Score: 3.0\n", "[85/201] Score: 10.0 \t llm Score: 10.0\n", "[86/201] Score: 4.0 \t llm Score: 3.0\n", "[87/201] Score: 4.0 \t llm Score: 10.0\n", "[88/201] Score: 8.0 \t llm Score: 10.0\n", "[89/201] Score: 10.0 \t llm Score: 10.0\n", "[90/201] Score: 4.0 \t llm Score: 1.0\n", "[91/201] Score: 7.0 \t llm Score: 2.0\n", "[92/201] Score: 6.0 \t llm Score: 6.5\n", "[93/201] Score: 7.0 \t llm Score: 4.0\n", "[94/201] Score: 9.0 \t llm Score: 10.0\n", "[95/201] Score: 7.0 \t llm Score: 3.0\n", "[96/201] Score: 7.0 \t llm Score: 3\n", "[97/201] Score: 4.0 \t llm Score: 3\n", "[98/201] Score: 9.0 \t llm Score: 6\n", "[99/201] Score: 9.0 \t llm Score: 10\n", "[100/201] Score: 5.0 \t llm Score: 7\n", "[101/201] Score: 9.0 \t llm Score: 9\n", "[102/201] Score: 7.0 \t llm Score: 10\n", "[103/201] Score: 0.0 \t llm Score: 10\n", "[104/201] Score: 4.0 \t llm Score: 10\n", "[105/201] Score: 6.0 \t llm Score: 7\n", "[106/201] Score: 3.0 \t llm Score: 4\n", "[107/201] Score: 7.0 \t llm Score: 10\n", "[108/201] Score: 3.0 \t llm Score: 3\n", "[109/201] Score: 5.0 \t llm Score: 4\n", "[110/201] Score: 8.0 \t llm Score: 10\n", "[111/201] Score: 9.0 \t llm Score: 10\n", "[112/201] Score: 10.0 \t llm Score: 10\n", "[113/201] Score: 7.0 \t llm Score: 6\n", "[114/201] Score: 6.0 \t llm Score: 4\n", "[115/201] Score: 2.0 \t llm Score: 10\n", "[116/201] Score: 9.0 \t llm Score: 10\n", "[117/201] Score: 9.0 \t llm Score: 10\n", "[118/201] Score: 8.0 \t llm Score: 10\n", "[119/201] Score: 10.0 \t llm Score: 4\n", "[120/201] Score: 10.0 \t llm Score: 9\n", "[121/201] Score: 10.0 \t llm Score: 10\n", "[122/201] Score: 9.0 \t llm Score: 10\n", "[123/201] Score: 6.0 \t llm Score: 7\n", "[124/201] Score: 10.0 \t llm Score: 3\n", "[125/201] Score: 4.0 \t llm Score: 9\n", "[126/201] Score: 9.0 \t llm Score: 9\n", "[127/201] Score: 10.0 \t llm Score: 9\n", "[128/201] Score: 7.0 \t llm Score: 10\n", "[129/201] Score: 4.0 \t llm Score: 8\n", "[130/201] Score: 7.0 \t llm Score: 9\n", "[131/201] Score: 10.0 \t llm Score: 10\n", "[132/201] Score: 10.0 \t llm Score: 10\n", "[133/201] Score: 7.0 \t llm Score: 5\n", "[134/201] Score: 9.0 \t llm Score: 6\n", "[135/201] Score: 7.0 \t llm Score: 10\n", "[136/201] Score: 1.0 \t llm Score: 6\n", "[137/201] Score: 7.0 \t llm Score: 10\n", "[138/201] Score: 4.0 \t llm Score: 10\n", "[139/201] Score: 4.0 \t llm Score: 3\n", "[140/201] Score: 9.0 \t llm Score: 10\n", "[141/201] Score: 7.0 \t llm Score: 6\n", "[142/201] Score: 9.0 \t llm Score: 8\n", "[143/201] Score: 3.0 \t llm Score: 10\n", "[144/201] Score: 10.0 \t llm Score: 10\n", "[145/201] Score: 9.0 \t llm Score: 10\n", "[146/201] Score: 9.0 \t llm Score: 10\n", "[147/201] Score: 10.0 \t llm Score: 3\n", "[148/201] Score: 9.0 \t llm Score: 9\n", "[149/201] Score: 8.0 \t llm Score: 6\n", "[150/201] Score: 9.0 \t llm Score: 10\n", "[151/201] Score: 5.0 \t llm Score: 4\n", "[152/201] Score: 9.0 \t llm Score: 2\n", "[153/201] Score: 7.0 \t llm Score: 10\n", "[154/201] Score: 10.0 \t llm Score: 10\n", "[155/201] Score: 10.0 \t llm Score: 8\n", "[156/201] Score: 0.0 \t llm Score: 9\n", "[157/201] Score: 7.0 \t llm Score: 8\n", "[158/201] Score: 4.0 \t llm Score: 10\n", "[159/201] Score: 8.0 \t llm Score: 10\n", "[160/201] Score: 10.0 \t llm Score: 10\n", "[161/201] Score: 8.0 \t llm Score: 10\n", "[162/201] Score: 1.0 \t llm Score: 4\n", "[163/201] Score: 9.0 \t llm Score: 10\n", "[164/201] Score: 8.0 \t llm Score: 1\n", "[165/201] Score: 10.0 \t llm Score: 10\n", "[166/201] Score: 4.0 \t llm Score: 2\n", "[167/201] Score: 4.0 \t llm Score: 4\n", "[168/201] Score: 10.0 \t llm Score: 10\n", "[169/201] Score: 10.0 \t llm Score: 7\n", "[170/201] Score: 8.0 \t llm Score: 9\n", "[171/201] Score: 10.0 \t llm Score: 4\n", "[172/201] Score: 9.0 \t llm Score: 10\n", "[173/201] Score: 0.0 \t llm Score: 6\n", "[174/201] Score: 9.0 \t llm Score: 10\n", "[175/201] Score: 0.0 \t llm Score: 6\n", "[176/201] Score: 8.0 \t llm Score: 7\n", "[177/201] Score: 4.0 \t llm Score: 10\n", "[178/201] Score: 7.0 \t llm Score: 10\n", "[179/201] Score: 10.0 \t llm Score: 9\n", "[180/201] Score: 6.0 \t llm Score: 6\n", "[181/201] Score: 8.0 \t llm Score: 10\n", "[182/201] Score: 7.0 \t llm Score: 10\n", "[183/201] Score: 10.0 \t llm Score: 10\n", "[184/201] Score: 9.0 \t llm Score: 2\n", "[185/201] Score: 6.0 \t llm Score: 5\n", "[186/201] Score: 8.0 \t llm Score: 10\n", "[187/201] Score: 8.0 \t llm Score: 4\n", "[188/201] Score: 7.0 \t llm Score: 4\n", "[189/201] Score: 9.0 \t llm Score: 10\n", "[190/201] Score: 4.0 \t llm Score: 7\n", "[191/201] Score: 10.0 \t llm Score: 10\n", "[192/201] Score: 7.0 \t llm Score: 9\n", "[193/201] Score: 3.0 \t llm Score: 6\n", "[194/201] Score: 5.0 \t llm Score: 3\n", "[195/201] Score: 10.0 \t llm Score: 10\n", "[196/201] Score: 10.0 \t llm Score: 6\n", "[197/201] Score: 9.0 \t llm Score: 9\n", "[198/201] Score: 10.0 \t llm Score: 4\n", "[199/201] Score: 9.0 \t llm Score: 10\n", "[200/201] Score: 7.0 \t llm Score: 6\n", "[201/201] Score: 9.0 \t llm Score: 10\n", "\n", "Done! Scored dataset saved to: output_score_DeepSeek-R1-0528-Qwen3-8B-unsloth-bnb-4bit.json\n"]}], "source": ["import json\n", "import torch\n", "from unsloth import FastLanguageModel\n", "from transformers import AutoTokenizer\n", "\n", "# ========== Step 1: Load the model ==========\n", "print(\"Loading model...\")\n", "model_name = \"unsloth/DeepSeek-R1-0528-Qwen3-8B-unsloth-bnb-4bit\"  # Replace with your preferred model\n", "model, tokenizer = FastLanguageModel.from_pretrained(\n", "    model_name=model_name,\n", "    max_seq_length=2048 * 5,\n", "    dtype=None,\n", "    load_in_4bit=True,\n", ")\n", "model = FastLanguageModel.for_inference(model)\n", "\n", "# ========== Step 2: Define the scoring function ==========\n", "def verify_prompt_json_match(prompt: str, json_str: str) -> float:\n", "    message = f\"\"\"PROMPT:\\n{prompt}\\n\\nWORKFLOW JSON:\\n{json_str}\"\"\"\n", "    \n", "    system_prompt = \"\"\"\n", "You are an expert in workflow automation and n8n. Your task is to judge whether a given n8n workflow correctly follows the intent and logic described in a natural language prompt.\n", "\n", "Return only a score from 1 to 10 where:\n", "- 10: Perfect match, clean and efficient implementation.\n", "- 7–9: Mostly correct, small mistakes or minor inefficiencies.\n", "- 4–6: Partial implementation, some steps missed or incorrect.\n", "- 1–3: Poor implementation or totally unrelated.\n", "- 0: Malformed or empty workflow.\n", "\n", "Only output the score as a number. Do not explain.\n", "\"\"\"\n", "    input_text = tokenizer.apply_chat_template([\n", "        {\"role\":\"system\", \"content\":system_prompt},\n", "        {\"role\":\"user\", \"content\":message}\n", "    ], tokenize=False, add_generation_prompt=True)\n", "    inputs = tokenizer(input_text, return_tensors=\"pt\").to(\"cuda\")\n", "\n", "    output = model.generate(\n", "        **inputs,\n", "        max_new_tokens=2048*3,\n", "        do_sample=False,\n", "        pad_token_id=tokenizer.eos_token_id,\n", "    )\n", "    \n", "    decoded_output = tokenizer.decode(output[0], skip_special_tokens=True).strip()\n", "    try:\n", "        return float(decoded_output.split(\"</think>\")[1].strip())\n", "    except:\n", "        return 0.0\n", "    # Try parsing valid JSON from model output\n", "    # try:\n", "    #     return json.loads(decoded_output).get(\"prompt_json_score\", 0.0)\n", "    # except json.JSONDecodeError:\n", "    #     if \"```json\" in decoded_output:\n", "    #         json_start = decoded_output.find(\"```json\") + 7\n", "    #         json_end = decoded_output.find(\"```\", json_start)\n", "    #         json_block = decoded_output[json_start:json_end].strip()\n", "    #         try:\n", "    #             return json.loads(json_block).get(\"prompt_json_score\", 0.0)\n", "    #         except json.JSONDecodeError:\n", "    #             print(f\"[!] Failed to parse fallback JSON:\\n{json_block}\")\n", "    #             return 0.0\n", "    #     else:\n", "    #         print(f\"[!] Failed to parse model output:\\n{decoded_output}\")\n", "    #         return 0.0\n", "\n", "# ========== Step 3: Load the dataset ==========\n", "input_file = \"/kaggle/input/json-prompt-llm-score/merged_training_dataset.json\"\n", "print(f\"Loading dataset from: {input_file}\")\n", "with open(input_file, \"r\") as f:\n", "    dataset = json.load(f)\n", "\n", "# ========== Step 4: Score each sample ==========\n", "print(\"Scoring dataset...\")\n", "for i, item in enumerate(dataset):\n", "    prompt = item.get(\"prompt\", \"\")\n", "    generated_json = item.get(\"generated_json\", \"\")\n", "    llm = item.get(\"llm_score\", 0.0)\n", "    score = verify_prompt_json_match(prompt, generated_json)\n", "    item[\"generated_score\"] = score\n", "    print(f\"[{i + 1}/{len(dataset)}] Score: {score} \\t llm Score: {llm}\")\n", "\n", "# ========== Step 5: Save updated dataset ==========\n", "suffix = model_name.split(\"/\")[1]\n", "output_file = f\"output_score_{suffix}.json\"\n", "with open(output_file, \"w\") as f:\n", "    json.dump(dataset, f, indent=2)\n", "\n", "print(f\"\\nDone! Scored dataset saved to: {output_file}\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "9408d168", "metadata": {"papermill": {"duration": 0.082969, "end_time": "2025-08-05T11:22:57.065581", "exception": false, "start_time": "2025-08-05T11:22:56.982612", "status": "completed"}, "tags": []}, "outputs": [], "source": []}], "metadata": {"kaggle": {"accelerator": "nvidiaTeslaT4", "dataSources": [{"datasetId": 8004143, "sourceId": 12666125, "sourceType": "datasetVersion"}], "dockerImageVersionId": 31089, "isGpuEnabled": true, "isInternetEnabled": true, "language": "python", "sourceType": "notebook"}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}, "papermill": {"default_parameters": {}, "duration": 14702.648063, "end_time": "2025-08-05T11:23:00.080759", "environment_variables": {}, "exception": null, "input_path": "__notebook__.ipynb", "output_path": "__notebook__.ipynb", "parameters": {}, "start_time": "2025-08-05T07:17:57.432696", "version": "2.6.0"}, "widgets": {"application/vnd.jupyter.widget-state+json": {"state": {"00a8d8c9f0164d00bbd7bf6d74981c7f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "01bd8372cdc6486c9893d44af33f4577": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "039d215ad5c74d3388f4e6ff668a948c": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "05744094383e4ee3b3103c27854eb070": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "058195ee9e534ac1ba471f3bcb74c4a1": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "05d9972a8c6a4ff1a31db7cc0ef09538": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "062461675e0d4cd593b67c934649b4a7": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "07f092bfddd3467196144c627fadaf05": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_f5ba2198d4c84079a6715e23dd2ad98f", "IPY_MODEL_b67b5739664f452d95cfb581c22e147e", "IPY_MODEL_da525be5c6cf4a468dbe9b331b3f47c7"], "layout": "IPY_MODEL_91d5078bcf884a53a034bf106a166648", "tabbable": null, "tooltip": null}}, "094c9a16ff614b6285d09ea51f57a223": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "0d0852bf0067458383958958d85db970": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "0d1212e99a0e4e8fbf503a9a8a37957d": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "109d5c8ae50e47feb7121793bef71a08": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "10a0ee3a0a3242bc8ba4645377d40ac3": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "13df8b16070044d7853007a3a8312305": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_54369d2f39c5435389a4b06017c83b49", "placeholder": "​", "style": "IPY_MODEL_a57e6cf5aadd47ed931477515479d05f", "tabbable": null, "tooltip": null, "value": " 2/2 [00:03&lt;00:00,  1.49s/it]"}}, "1477d84511544b2e920916b8bff28bab": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "2b382b1b72d44c54af1d61de68a27ca5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_84ae0e17bd2745d9b6f5d240654db3e5", "IPY_MODEL_31ab2c9a4574498d96cdd1f32e91e2b5", "IPY_MODEL_336235aa0b3d4260b096900a1dc21a1c"], "layout": "IPY_MODEL_d995444983ae453e848811797c539308", "tabbable": null, "tooltip": null}}, "2bbdddb0b80b4e43997578b6456d6618": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "2cdf23f423f44a0695f1eb33b0afe01b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "2e5657cab45c4adb9849b04808f0b325": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_1477d84511544b2e920916b8bff28bab", "placeholder": "​", "style": "IPY_MODEL_8598befddde8488698213ac7e4401161", "tabbable": null, "tooltip": null, "value": " 171/171 [00:00&lt;00:00, 16.3kB/s]"}}, "31ab2c9a4574498d96cdd1f32e91e2b5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_3eb36c1a025b444d9fa65af95e0a6a79", "max": 1.0, "min": 0.0, "orientation": "horizontal", "style": "IPY_MODEL_d560045a49da413bb19316c346e58b02", "tabbable": null, "tooltip": null, "value": 1.0}}, "31cb64f2ecd342998365bd3b9590cced": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "321ae2ef6d1c4bd2ada5ea66da6f6181": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_3d7ff075ffb1498d8dd265f47b281385", "placeholder": "​", "style": "IPY_MODEL_31cb64f2ecd342998365bd3b9590cced", "tabbable": null, "tooltip": null, "value": "Loading checkpoint shards: 100%"}}, "336235aa0b3d4260b096900a1dc21a1c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_4988e5de2d134e24859eb80c707ad128", "placeholder": "​", "style": "IPY_MODEL_8b4c67d03ee44b64865806dd039b9069", "tabbable": null, "tooltip": null, "value": " 11.0k/? [00:00&lt;00:00, 1.25MB/s]"}}, "344fe8dbaa95420eaa5ad7368f4c1d34": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "359f227a3e3441b28e8192fa09b22485": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3622634c72164e5d912ebb1cd1b8a072": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "3d7ff075ffb1498d8dd265f47b281385": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "3dabcce08b9d4a4d9615596f4a0a9bf5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "3eb36c1a025b444d9fa65af95e0a6a79": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "20px"}}, "485dc1e2f10e4c7f8bb1dd364fd7a5a7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "4988e5de2d134e24859eb80c707ad128": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "52e4eb3a3b844fbcb9d0a94a3478545f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_039d215ad5c74d3388f4e6ff668a948c", "max": 2.0, "min": 0.0, "orientation": "horizontal", "style": "IPY_MODEL_68d9df2aa4d546dcb6096dd0873cd1d7", "tabbable": null, "tooltip": null, "value": 2.0}}, "5434b19ee76940889ec7117402db6486": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "54369d2f39c5435389a4b06017c83b49": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "57914cb7f2994d1087e927049c8c92a4": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_be9f35ac766d4dbcbacca539feff29ef", "placeholder": "​", "style": "IPY_MODEL_adce766dd8b543aab1a3fdf37d16954b", "tabbable": null, "tooltip": null, "value": "model.safetensors.index.json: "}}, "582e2728c6a243bd88db9545f292a1b8": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5b3b1e1793a847bcb336237ca0b6d440": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5fd5c9670d9e4d2db65dfabc85b65934": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_10a0ee3a0a3242bc8ba4645377d40ac3", "max": 4975323743.0, "min": 0.0, "orientation": "horizontal", "style": "IPY_MODEL_2bbdddb0b80b4e43997578b6456d6618", "tabbable": null, "tooltip": null, "value": 4975323743.0}}, "61ba3bc8d30c449ba10776fbc42065fe": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_d55dde1692d54391af057e462d819126", "placeholder": "​", "style": "IPY_MODEL_109d5c8ae50e47feb7121793bef71a08", "tabbable": null, "tooltip": null, "value": " 4.98G/4.98G [00:16&lt;00:00, 667MB/s]"}}, "6495270f03454c4b9f5141536a97f3c7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_e47b77b373b945b29626a19ee02e7049", "max": 11423346.0, "min": 0.0, "orientation": "horizontal", "style": "IPY_MODEL_3622634c72164e5d912ebb1cd1b8a072", "tabbable": null, "tooltip": null, "value": 11423346.0}}, "64db2149dd764da2866e0554e3ad21f4": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "68d9df2aa4d546dcb6096dd0873cd1d7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "68e3130be5484ab09db0410256a0bf78": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "72f466945d58463c8eb95e0b57e99e90": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_f7f01f6cb71f4b899b4264d6b911485b", "placeholder": "​", "style": "IPY_MODEL_7f2dcc7fced64b929a086d139c3bfdd6", "tabbable": null, "tooltip": null, "value": " 140k/? [00:00&lt;00:00, 14.0MB/s]"}}, "774dfd14a2a04afc8c466ae470f76fbb": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7802fb0a488d42f3ae31705507128f7a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_a04785427f0d423d8a8abf462a2ae9e6", "max": 472.0, "min": 0.0, "orientation": "horizontal", "style": "IPY_MODEL_485dc1e2f10e4c7f8bb1dd364fd7a5a7", "tabbable": null, "tooltip": null, "value": 472.0}}, "7d3276773ade408ba0fd32c0cd61becc": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7f2dcc7fced64b929a086d139c3bfdd6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "8110a37dec754710851439a01d2a827b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_ef8d3fb3edce4fc595fb98d2be7f7a02", "max": 1.0, "min": 0.0, "orientation": "horizontal", "style": "IPY_MODEL_00a8d8c9f0164d00bbd7bf6d74981c7f", "tabbable": null, "tooltip": null, "value": 1.0}}, "84ae0e17bd2745d9b6f5d240654db3e5": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_9980eec4fbd44ec2b03c3c660e49a1ae", "placeholder": "​", "style": "IPY_MODEL_3dabcce08b9d4a4d9615596f4a0a9bf5", "tabbable": null, "tooltip": null, "value": "tokenizer_config.json: "}}, "8598befddde8488698213ac7e4401161": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "85a97b317c1148b9909834a48be4d305": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_57914cb7f2994d1087e927049c8c92a4", "IPY_MODEL_8110a37dec754710851439a01d2a827b", "IPY_MODEL_72f466945d58463c8eb95e0b57e99e90"], "layout": "IPY_MODEL_fd31846a30894aaf936ef707b7ee4ee4", "tabbable": null, "tooltip": null}}, "8accdbd909ef4c4bbd1aa322bc5838e6": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "8b4c67d03ee44b64865806dd039b9069": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "8d12438ac88f4e06b625810202416bb1": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_321ae2ef6d1c4bd2ada5ea66da6f6181", "IPY_MODEL_52e4eb3a3b844fbcb9d0a94a3478545f", "IPY_MODEL_13df8b16070044d7853007a3a8312305"], "layout": "IPY_MODEL_64db2149dd764da2866e0554e3ad21f4", "tabbable": null, "tooltip": null}}, "8e557562de94495cb047bcf7739c9aba": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8e5f0d70e3af42028765f61c9090ba6e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_f0f2a318af3344ea8063d53be60ae757", "IPY_MODEL_6495270f03454c4b9f5141536a97f3c7", "IPY_MODEL_8fc3a23dc5b142e082acfbc0f4ab6b1c"], "layout": "IPY_MODEL_8e557562de94495cb047bcf7739c9aba", "tabbable": null, "tooltip": null}}, "8efd16843f8e4ff6b5b9cca77cf7793d": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8fc3a23dc5b142e082acfbc0f4ab6b1c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_5b3b1e1793a847bcb336237ca0b6d440", "placeholder": "​", "style": "IPY_MODEL_a4a632213e4a4e269defcb7007439f8b", "tabbable": null, "tooltip": null, "value": " 11.4M/11.4M [00:00&lt;00:00, 25.4MB/s]"}}, "912e3adc8df74d88b7403660d2b3dd12": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "91d5078bcf884a53a034bf106a166648": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "920665677ce24edeadf61cee14fcaf97": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "9980eec4fbd44ec2b03c3c660e49a1ae": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9d9202416ead44799557996f9191d670": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_b5231760c71241f895a6e112c5cc7b18", "IPY_MODEL_e344b150ddbc4250b15455c51e16c9a9", "IPY_MODEL_ad0ab92ddfae441aaca03606816e101b"], "layout": "IPY_MODEL_359f227a3e3441b28e8192fa09b22485", "tabbable": null, "tooltip": null}}, "a04785427f0d423d8a8abf462a2ae9e6": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a4a632213e4a4e269defcb7007439f8b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "a57e6cf5aadd47ed931477515479d05f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "ad0ab92ddfae441aaca03606816e101b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_d95a6b079ce74294accbac16392e9552", "placeholder": "​", "style": "IPY_MODEL_912e3adc8df74d88b7403660d2b3dd12", "tabbable": null, "tooltip": null, "value": " 5.26k/? [00:00&lt;00:00, 562kB/s]"}}, "adce766dd8b543aab1a3fdf37d16954b": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "b1d162b50c7e4ba0965ad59d3ac5e73a": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_094c9a16ff614b6285d09ea51f57a223", "max": 171.0, "min": 0.0, "orientation": "horizontal", "style": "IPY_MODEL_058195ee9e534ac1ba471f3bcb74c4a1", "tabbable": null, "tooltip": null, "value": 171.0}}, "b5231760c71241f895a6e112c5cc7b18": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_05d9972a8c6a4ff1a31db7cc0ef09538", "placeholder": "​", "style": "IPY_MODEL_68e3130be5484ab09db0410256a0bf78", "tabbable": null, "tooltip": null, "value": "chat_template.jinja: "}}, "b54239447cf7459e9a5e2dd4e9ccad7c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_ca8467d73261475e801c0c0752031371", "placeholder": "​", "style": "IPY_MODEL_920665677ce24edeadf61cee14fcaf97", "tabbable": null, "tooltip": null, "value": " 472/472 [00:00&lt;00:00, 64.9kB/s]"}}, "b67b5739664f452d95cfb581c22e147e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_062461675e0d4cd593b67c934649b4a7", "max": 2467653435.0, "min": 0.0, "orientation": "horizontal", "style": "IPY_MODEL_05744094383e4ee3b3103c27854eb070", "tabbable": null, "tooltip": null, "value": 2467653435.0}}, "b81c45064b764929a813bef0466c2a8e": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "background": null, "description_width": "", "font_size": null, "text_color": null}}, "b9deac3d4c4744129cacb8ba77618dc8": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_e467decab6e94bfe82ce3a2452d872c9", "IPY_MODEL_5fd5c9670d9e4d2db65dfabc85b65934", "IPY_MODEL_61ba3bc8d30c449ba10776fbc42065fe"], "layout": "IPY_MODEL_774dfd14a2a04afc8c466ae470f76fbb", "tabbable": null, "tooltip": null}}, "be9f35ac766d4dbcbacca539feff29ef": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c1fb57c2669845f08941a776ba2b3808": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "c7602f1583f24f5bb4174887644ec14e": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "20px"}}, "ca8467d73261475e801c0c0752031371": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "cd411dc77907430ab729d6bd73b7de8d": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_0d1212e99a0e4e8fbf503a9a8a37957d", "placeholder": "​", "style": "IPY_MODEL_8accdbd909ef4c4bbd1aa322bc5838e6", "tabbable": null, "tooltip": null, "value": "special_tokens_map.json: 100%"}}, "d55dde1692d54391af057e462d819126": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d560045a49da413bb19316c346e58b02": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "ProgressStyleModel", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "d95a6b079ce74294accbac16392e9552": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d995444983ae453e848811797c539308": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "da525be5c6cf4a468dbe9b331b3f47c7": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_ff1f6fbda4f34b799c096bb09491f0b8", "placeholder": "​", "style": "IPY_MODEL_5434b19ee76940889ec7117402db6486", "tabbable": null, "tooltip": null, "value": " 2.47G/2.47G [00:10&lt;00:00, 693MB/s]"}}, "dd0e58e2a39749e5bee1426c34f73d75": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_fe10ba6577964f3c9e9a0544774e379c", "IPY_MODEL_b1d162b50c7e4ba0965ad59d3ac5e73a", "IPY_MODEL_2e5657cab45c4adb9849b04808f0b325"], "layout": "IPY_MODEL_8efd16843f8e4ff6b5b9cca77cf7793d", "tabbable": null, "tooltip": null}}, "e344b150ddbc4250b15455c51e16c9a9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "FloatProgressModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_c7602f1583f24f5bb4174887644ec14e", "max": 1.0, "min": 0.0, "orientation": "horizontal", "style": "IPY_MODEL_c1fb57c2669845f08941a776ba2b3808", "tabbable": null, "tooltip": null, "value": 1.0}}, "e467decab6e94bfe82ce3a2452d872c9": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_e8c6abf8598c4f8db500ffaa2ac16153", "placeholder": "​", "style": "IPY_MODEL_b81c45064b764929a813bef0466c2a8e", "tabbable": null, "tooltip": null, "value": "model-00001-of-00002.safetensors: 100%"}}, "e47b77b373b945b29626a19ee02e7049": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e8c6abf8598c4f8db500ffaa2ac16153": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e953f3e35db5453a84fdfa6861195aa6": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ef8d3fb3edce4fc595fb98d2be7f7a02": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": "20px"}}, "f0f2a318af3344ea8063d53be60ae757": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_344fe8dbaa95420eaa5ad7368f4c1d34", "placeholder": "​", "style": "IPY_MODEL_01bd8372cdc6486c9893d44af33f4577", "tabbable": null, "tooltip": null, "value": "tokenizer.json: 100%"}}, "f18e71b6af684b9586164317d389545f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HBoxModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_cd411dc77907430ab729d6bd73b7de8d", "IPY_MODEL_7802fb0a488d42f3ae31705507128f7a", "IPY_MODEL_b54239447cf7459e9a5e2dd4e9ccad7c"], "layout": "IPY_MODEL_e953f3e35db5453a84fdfa6861195aa6", "tabbable": null, "tooltip": null}}, "f5ba2198d4c84079a6715e23dd2ad98f": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_7d3276773ade408ba0fd32c0cd61becc", "placeholder": "​", "style": "IPY_MODEL_2cdf23f423f44a0695f1eb33b0afe01b", "tabbable": null, "tooltip": null, "value": "model-00002-of-00002.safetensors: 100%"}}, "f7f01f6cb71f4b899b4264d6b911485b": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "fd31846a30894aaf936ef707b7ee4ee4": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "fe10ba6577964f3c9e9a0544774e379c": {"model_module": "@jupyter-widgets/controls", "model_module_version": "2.0.0", "model_name": "HTMLModel", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "2.0.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "2.0.0", "_view_name": "HTMLView", "description": "", "description_allow_html": false, "layout": "IPY_MODEL_582e2728c6a243bd88db9545f292a1b8", "placeholder": "​", "style": "IPY_MODEL_0d0852bf0067458383958958d85db970", "tabbable": null, "tooltip": null, "value": "generation_config.json: 100%"}}, "ff1f6fbda4f34b799c096bb09491f0b8": {"model_module": "@jupyter-widgets/base", "model_module_version": "2.0.0", "model_name": "LayoutModel", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "2.0.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "2.0.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border_bottom": null, "border_left": null, "border_right": null, "border_top": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}}, "version_major": 2, "version_minor": 0}}}, "nbformat": 4, "nbformat_minor": 5}